<?php
/**
 * Setup Messaging System Tables
 * Run this once to create messaging tables
 */

require_once '../config/database.php';

try {
    echo "Setting up messaging system tables...\n";
    
    // Read and execute SQL file
    $sql = file_get_contents('messaging_system.sql');
    
    // Split by semicolon and execute each statement
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
            try {
                executeQuery($statement);
                echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (Exception $e) {
                // Skip if table already exists
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    echo "⚠ Skipped (already exists): " . substr($statement, 0, 50) . "...\n";
                } else {
                    throw $e;
                }
            }
        }
    }
    
    echo "\n✅ Messaging system setup completed successfully!\n";
    echo "You can now use the messaging feature.\n";
    
} catch (Exception $e) {
    echo "\n❌ Error setting up messaging system: " . $e->getMessage() . "\n";
    exit(1);
}
?>
