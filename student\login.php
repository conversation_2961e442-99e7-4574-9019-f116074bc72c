<?php
/**
 * Student Login Page for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is already logged in
startSecureSession();
if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'student') {
    header('Location: dashboard.php');
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $studentId = sanitizeInput($_POST['student_id']);
    $password = $_POST['password'];

    // Debug logging
    error_log("Login attempt - Student ID: $studentId");

    if (empty($studentId) || empty($password)) {
        $error = 'Please enter both Matriculation Number and password.';
    } else {
        // First, check if student exists (simplified query)
        $student = fetchOne("
            SELECT s.id, s.student_id, s.first_name, s.last_name, s.email,
                   s.password_hash, s.department_id, s.academic_level_id, s.is_approved,
                   s.registration_date
            FROM students s
            WHERE s.student_id = :student_id
        ", ['student_id' => $studentId]);

        error_log("Student found: " . ($student ? "YES" : "NO"));

        if (!$student) {
            $error = 'Invalid Matriculation Number or password.';
            error_log("Login failed - Student not found: $studentId");
        } elseif (!$student['is_approved']) {
            $error = 'Your account is pending approval. Please wait for admin approval.';
            error_log("Login failed - Student not approved: $studentId");
        } else {
            // Test password verification
            $passwordValid = verifyPassword($password, $student['password_hash']);
            error_log("Password verification: " . ($passwordValid ? "VALID" : "INVALID"));

            if (!$passwordValid) {
                $error = 'Invalid Matriculation Number or password.';
                error_log("Login failed - Invalid password for: $studentId");
            } else {
                // Get additional student info with LEFT JOINs to avoid issues
                $studentDetails = fetchOne("
                    SELECT s.*,
                           COALESCE(s.xp_points, 0) as xp_points,
                           COALESCE(s.current_level, 1) as current_level,
                           COALESCE(s.total_quizzes_completed, 0) as total_quizzes,
                           d.name as department_name,
                           al.level_name
                    FROM students s
                    LEFT JOIN departments d ON s.department_id = d.id
                    LEFT JOIN academic_levels al ON s.academic_level_id = al.id
                    WHERE s.id = :id
                ", ['id' => $student['id']]);

                if ($studentDetails) {
                    $student = $studentDetails;
                }

                error_log("Login successful for: $studentId");

                // Create session
                $sessionId = generateSessionId();
            
            // Store session in database
            insertRecord('user_sessions', [
                'id' => $sessionId,
                'user_id' => $student['id'],
                'user_type' => 'student',
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'],
                'expires_at' => date('Y-m-d H:i:s', strtotime('+7 days'))
            ]);
            
            // Update last login
            updateRecord('students', 
                ['last_login' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $student['id']]
            );
            
            // Set session variables
            $_SESSION['user_id'] = $student['id'];
            $_SESSION['user_type'] = 'student';
            $_SESSION['student_id'] = $student['student_id'];
            $_SESSION['first_name'] = $student['first_name'];
            $_SESSION['last_name'] = $student['last_name'];
            $_SESSION['email'] = $student['email'];
            $_SESSION['department_id'] = $student['department_id'];
            $_SESSION['department_name'] = $student['department_name'];
            $_SESSION['academic_level_id'] = $student['academic_level_id'];
            $_SESSION['level_name'] = $student['level_name'];
            $_SESSION['session_id'] = $sessionId;
            
                header('Location: dashboard.php');
                exit();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Login - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="logo-container">
                    <img src="../images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="logo">
                </div>
                <h1>Student Portal</h1>
                <p>AI-Powered Learning Management System</p>
                <p class="school-name">Ogbonnaya Onu Polytechnic, Aba</p>
            </div>
            
            <div class="auth-body">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="auth-form">
                    <div class="form-group">
                        <label for="student_id">
                            <i class="fas fa-id-card"></i>
                            Matriculation Number
                        </label>
                        <input
                            type="text"
                            id="student_id"
                            name="student_id"
                            required
                            placeholder="Enter your Matriculation Number"
                            value="<?php echo isset($_POST['student_id']) ? htmlspecialchars($_POST['student_id']) : ''; ?>"
                            autocomplete="username"
                        >
                        <div class="input-help">
                            <i class="fas fa-info-circle"></i>
                            <span>Your Matriculation Number was provided during registration</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            Password
                        </label>
                        <div class="password-input">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                required 
                                placeholder="Enter your password"
                                autocomplete="current-password"
                            >
                            <button type="button" class="toggle-password" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember_me">
                            <span class="checkmark"></span>
                            Remember me for 7 days
                        </label>
                        <a href="forgot-password.php" class="forgot-password">
                            Forgot Password?
                        </a>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        Login to Dashboard
                    </button>
                </form>
                
                <div class="auth-divider">
                    <span>New Student?</span>
                </div>
                
                <a href="register.php" class="btn btn-secondary btn-register-link">
                    <i class="fas fa-user-plus"></i>
                    Create Account
                </a>
                
                <div class="auth-links">
                    <a href="../index.php" class="link-secondary">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </a>
                    <a href="../admin/login.php" class="link-secondary">
                        <i class="fas fa-user-shield"></i>
                        Admin Login
                    </a>
                </div>
            </div>
            
            <div class="auth-footer">
                <div class="security-info">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure Student Access</span>
                </div>
                <p>&copy; 2025 Ogbonnaya Onu Polytechnic, Aba. All rights reserved.</p>
            </div>
        </div>
        
        <div class="background-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>
    </div>
    
    <script src="../assets/js/auth.js"></script>
    <script>
        // Add matriculation number formatting
        document.addEventListener('DOMContentLoaded', function() {
            const matriculationInput = document.getElementById('student_id');
            if (matriculationInput) {
                matriculationInput.addEventListener('input', function() {
                    // Convert to uppercase for consistency
                    this.value = this.value.toUpperCase();
                });

                matriculationInput.addEventListener('blur', function() {
                    validateMatriculationFormat(this);
                });
            }
        });
        
        function validateMatriculationFormat(input) {
            const matriculationNumber = input.value.trim();
            const pattern = /^[A-Z]{2,5}\/\d{4}\/\d{5}\/\d{1}\/[A-Z]{2,5}$/;
            
            if (matriculationNumber && !pattern.test(matriculationNumber)) {
                input.style.borderColor = '#f59e0b';
                input.style.backgroundColor = '#fffbeb';
                
                // Format hint removed as requested
            } else {
                input.style.borderColor = '';
                input.style.backgroundColor = '';
            }
        }

        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleButton = passwordInput.nextElementSibling;
            const toggleIcon = toggleButton.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
