// Mission Game JavaScript - Immersive Adventure Learning

let missionState = {
    currentQuestion: 0,
    score: 0,
    lives: 3,
    correctAnswers: 0,
    selectedAnswer: null,
    questions: [],
    startTime: null,
    questionStartTime: null,
    missionTimer: null,
    timeRemaining: 0,
    isPaused: false
};

// Initialize mission when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeMission();
});

function initializeMission() {
    // Set initial time
    missionState.timeRemaining = missionConfig.timeLimit * 60; // Convert to seconds
    updateTimer();
    
    // Show story intro
    showStoryIntro();
}

function showStoryIntro() {
    document.getElementById('story-intro').style.display = 'flex';
}

async function startMission() {
    try {
        // Hide story intro and show loading
        document.getElementById('story-intro').style.display = 'none';
        document.getElementById('loading-screen').style.display = 'flex';

        // Show loading animation
        showLoadingProgress();

        // Generate questions using AI
        await generateMissionQuestions();

        // Start the actual mission
        beginMissionGameplay();

    } catch (error) {
        console.error('Failed to start mission:', error);
        showError('Failed to start mission. Please try again.');
    }
}

function showLoadingProgress() {
    const loadingBar = document.getElementById('loading-bar');
    let progress = 0;
    
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;
        
        loadingBar.style.width = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
        }
    }, 200);
}

async function generateMissionQuestions() {
    try {
        const response = await fetch('../api/generate-game-questions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: missionConfig.sessionId,
                difficulty: missionConfig.difficulty,
                question_count: missionConfig.totalQuestions,
                department_id: missionConfig.departmentId,
                academic_level_id: missionConfig.academicLevelId,
                game_mode: 'mission'
            })
        });

        const data = await response.json();

        if (data.success) {
            missionState.questions = data.questions;
        } else {
            throw new Error(data.message || 'Failed to generate questions');
        }

    } catch (error) {
        console.error('Error generating questions:', error);
        throw error; // Let the calling function handle the error
    }
}

function generateSampleQuestions() {
    const sampleQuestions = [];
    for (let i = 1; i <= missionConfig.totalQuestions; i++) {
        sampleQuestions.push({
            id: i,
            question: `Sample Mission Question ${i}: What is the correct answer for this challenging question?`,
            option_a: "Option A - First choice",
            option_b: "Option B - Second choice", 
            option_c: "Option C - Third choice",
            option_d: "Option D - Fourth choice",
            correct_answer: ['A', 'B', 'C', 'D'][Math.floor(Math.random() * 4)],
            difficulty: missionConfig.difficulty
        });
    }
    return sampleQuestions;
}

function beginMissionGameplay() {
    // Check if we have questions
    if (!missionState.questions || missionState.questions.length === 0) {
        console.error('No questions available for mission');
        showError('No questions available. Please try again.');
        return;
    }

    // Hide loading screen and show question container
    const loadingScreen = document.getElementById('loading-screen');
    const questionContainer = document.getElementById('question-container');

    if (loadingScreen) loadingScreen.style.display = 'none';
    if (questionContainer) questionContainer.style.display = 'block';

    // Initialize mission state
    missionState.startTime = Date.now();
    missionState.currentQuestion = 0;
    missionState.score = 0;
    missionState.correctAnswers = 0;
    missionState.lives = 3;

    // Start mission timer
    startMissionTimer();

    // Load first question
    loadQuestion();

    // Play mission start sound
    playMissionSound('start');
}

function startMissionTimer() {
    missionState.missionTimer = setInterval(() => {
        // Don't update timer if mission is paused
        if (missionState.isPaused) {
            return;
        }

        missionState.timeRemaining--;
        updateTimer();

        if (missionState.timeRemaining <= 0) {
            clearInterval(missionState.missionTimer);
            missionFailed('Time\'s up!');
        }
    }, 1000);
}

function updateTimer() {
    const minutes = Math.floor(missionState.timeRemaining / 60);
    const seconds = missionState.timeRemaining % 60;
    const timerDisplay = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    document.getElementById('timer').textContent = timerDisplay;
    
    // Add warning animation when time is low
    if (missionState.timeRemaining <= 60) {
        document.getElementById('timer').classList.add('timer-warning');
    }
}

function loadQuestion() {
    if (missionState.currentQuestion >= missionState.questions.length) {
        missionComplete();
        return;
    }
    
    const question = missionState.questions[missionState.currentQuestion];
    missionState.selectedAnswer = null;
    missionState.questionStartTime = Date.now();
    
    // Update question display
    document.getElementById('question-num').textContent = missionState.currentQuestion + 1;
    document.getElementById('question-text').textContent = question.question;
    document.getElementById('option-a').textContent = question.option_a;
    document.getElementById('option-b').textContent = question.option_b;
    document.getElementById('option-c').textContent = question.option_c;
    document.getElementById('option-d').textContent = question.option_d;
    
    // Reset option buttons
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
        btn.classList.remove('selected', 'correct', 'incorrect');
        btn.disabled = false;
    });
    
    // Disable submit button
    document.getElementById('submit-btn').disabled = true;
    
    // Update progress
    updateMissionProgress();
    
    // Add entrance animation
    const questionCard = document.querySelector('.question-card');
    questionCard.classList.remove('animate__fadeInUp');
    questionCard.classList.add('animate__fadeInUp');
}

function selectAnswer(option) {
    // Allow changing selection - remove the prevention line
    missionState.selectedAnswer = option;

    // Update UI with enhanced animations
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
        btn.classList.remove('selected');
        if (btn.dataset.option === option) {
            btn.classList.add('selected');

            // Add selection animation
            btn.style.transform = 'scale(1.05)';
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
            }, 200);

            // Create ripple effect
            createRippleEffect(btn);
        }
    });
    
    // Play selection sound
    playMissionSound('select');
    
    // Enable submit button
    const submitBtn = document.getElementById('submit-btn');
    submitBtn.disabled = false;
    submitBtn.classList.add('pulse-animation');
    
    // Add haptic feedback if supported
    if (navigator.vibrate) {
        navigator.vibrate(50);
    }
}

function createRippleEffect(element) {
    const ripple = document.createElement('div');
    ripple.className = 'ripple-effect';
    
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        background: rgba(102, 126, 234, 0.6);
        transform: scale(0);
        animation: ripple 0.6s linear;
        top: 50%;
        left: 50%;
        margin-left: -${size/2}px;
        margin-top: -${size/2}px;
        pointer-events: none;
        z-index: 10;
    `;
    
    element.style.position = 'relative';
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

function submitAnswer() {
    if (!missionState.selectedAnswer) return;
    
    const question = missionState.questions[missionState.currentQuestion];
    const isCorrect = missionState.selectedAnswer === question.correct_answer;
    
    // Show answer feedback
    showAnswerFeedback(isCorrect, question.correct_answer);
    
    if (isCorrect) {
        missionState.correctAnswers++;
        const points = calculatePoints();
        missionState.score += points;
        updateScoreDisplay();
        playMissionSound('correct');
    } else {
        missionState.lives--;
        updateLivesDisplay();
        playMissionSound('incorrect');
        
        if (missionState.lives <= 0) {
            setTimeout(() => {
                missionFailed('No lives remaining!');
            }, 2000);
            return;
        }
    }
    
    // Move to next question after delay
    setTimeout(() => {
        missionState.currentQuestion++;
        loadQuestion();
    }, 2000);
}

function calculatePoints() {
    // Calculate points per correct answer based on total mission reward
    const totalMissionReward = missionConfig.pointsReward || 50; // Total points for completing the mission
    const totalQuestions = missionConfig.totalQuestions || 5;

    // Points per correct answer = total reward / total questions
    const pointsPerQuestion = Math.round(totalMissionReward / totalQuestions);

    const difficultyMultiplier = {
        'easy': 1,
        'medium': 1.5,
        'hard': 2
    };

    return Math.round(pointsPerQuestion * difficultyMultiplier[missionConfig.difficulty]);
}

function showAnswerFeedback(isCorrect, correctAnswer) {
    const optionBtns = document.querySelectorAll('.option-btn');
    
    optionBtns.forEach(btn => {
        btn.disabled = true;
        
        if (btn.dataset.option === correctAnswer) {
            btn.classList.add('correct');
            btn.style.animation = 'correctAnswer 0.6s ease-in-out';
        } else if (btn.dataset.option === missionState.selectedAnswer && !isCorrect) {
            btn.classList.add('incorrect');
            btn.style.animation = 'incorrectAnswer 0.6s ease-in-out';
        }
    });
    
    // Add haptic feedback
    if (navigator.vibrate) {
        navigator.vibrate(isCorrect ? [100] : [100, 50, 100]);
    }
    
    // Show floating feedback
    if (isCorrect) {
        showFloatingText('Correct!', 'success');
    } else {
        showFloatingText('Incorrect!', 'error');
        shakeScreen();
    }
}

function showFloatingText(text, type) {
    const floatingText = document.createElement('div');
    floatingText.className = `floating-text ${type}`;
    floatingText.textContent = text;
    floatingText.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        font-weight: bold;
        color: ${type === 'success' ? '#10b981' : '#ef4444'};
        z-index: 1000;
        pointer-events: none;
        animation: floatUp 2s ease-out forwards;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    `;
    
    document.body.appendChild(floatingText);
    
    setTimeout(() => {
        floatingText.remove();
    }, 2000);
}

function shakeScreen() {
    const gameContainer = document.querySelector('.mission-game-container');
    gameContainer.style.animation = 'screenShake 0.5s ease-in-out';
    
    setTimeout(() => {
        gameContainer.style.animation = '';
    }, 500);
}

function updateMissionProgress() {
    const progress = ((missionState.currentQuestion) / missionConfig.totalQuestions) * 100;
    document.getElementById('mission-progress').style.width = progress + '%';
    document.getElementById('current-question').textContent = missionState.currentQuestion;
}

function updateScoreDisplay() {
    document.getElementById('current-score').textContent = missionState.score;
}

function updateLivesDisplay() {
    document.getElementById('lives-count').textContent = missionState.lives;
    
    // Update heart display
    const hearts = document.querySelectorAll('.life-heart');
    hearts.forEach((heart, index) => {
        if (index >= missionState.lives) {
            heart.classList.remove('active');
            heart.classList.add('lost');
        }
    });
}

function missionComplete() {
    clearInterval(missionState.missionTimer);
    
    // Calculate final stats
    const accuracy = Math.round((missionState.correctAnswers / missionConfig.totalQuestions) * 100);
    const starsEarned = calculateStarsEarned(accuracy);
    
    // Update display
    document.getElementById('final-score').textContent = missionState.score;
    document.getElementById('stars-earned').textContent = starsEarned;
    document.getElementById('accuracy-rate').textContent = accuracy + '%';
    
    // Save mission results
    saveMissionResults(accuracy, starsEarned);
    
    // Show completion screen
    document.getElementById('question-container').style.display = 'none';
    document.getElementById('mission-complete').style.display = 'flex';
    
    // Play victory sound and effects
    playMissionSound('victory');
    createCelebrationEffect();
}

function calculateStarsEarned(accuracy) {
    if (accuracy >= 90) return 3;
    if (accuracy >= 70) return 2;
    if (accuracy >= 50) return 1;
    return 1; // Minimum 1 star for completion
}

function missionFailed(reason) {
    clearInterval(missionState.missionTimer);
    
    document.getElementById('question-container').style.display = 'none';
    document.getElementById('mission-failed').style.display = 'flex';
    
    playMissionSound('failure');
}

// Enhanced sound system for missions
function playMissionSound(type) {
    if (!window.audioContext) {
        window.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
    
    const ctx = window.audioContext;
    
    switch(type) {
        case 'start':
            playMelody([523.25, 659.25, 783.99], [0.3, 0.3, 0.6]);
            break;
        case 'correct':
            playMelody([523.25, 659.25], [0.2, 0.3]);
            break;
        case 'incorrect':
            playMelody([220, 196], [0.2, 0.4]);
            break;
        case 'victory':
            playMelody([523.25, 659.25, 783.99, 1046.50, 1318.51], [0.2, 0.2, 0.2, 0.2, 0.4]);
            break;
        case 'failure':
            playMelody([196, 174.61, 146.83], [0.3, 0.3, 0.6]);
            break;
        case 'select':
            playTone(800, 0.1);
            break;
    }
}

function playMelody(frequencies, durations) {
    const ctx = window.audioContext;
    let currentTime = ctx.currentTime;
    
    frequencies.forEach((freq, index) => {
        const osc = ctx.createOscillator();
        const gain = ctx.createGain();
        
        osc.connect(gain);
        gain.connect(ctx.destination);
        
        osc.frequency.setValueAtTime(freq, currentTime);
        gain.gain.setValueAtTime(0.2, currentTime);
        gain.gain.exponentialRampToValueAtTime(0.01, currentTime + durations[index]);
        
        osc.start(currentTime);
        osc.stop(currentTime + durations[index]);
        
        currentTime += durations[index];
    });
}

function playTone(frequency, duration) {
    const ctx = window.audioContext;
    const osc = ctx.createOscillator();
    const gain = ctx.createGain();
    
    osc.connect(gain);
    gain.connect(ctx.destination);
    
    osc.frequency.setValueAtTime(frequency, ctx.currentTime);
    gain.gain.setValueAtTime(0.1, ctx.currentTime);
    gain.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + duration);
    
    osc.start(ctx.currentTime);
    osc.stop(ctx.currentTime + duration);
}

function createCelebrationEffect() {
    // Create celebration particles
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: fixed;
            width: 8px;
            height: 8px;
            background: ${['#667eea', '#764ba2', '#10b981', '#f59e0b'][Math.floor(Math.random() * 4)]};
            border-radius: 50%;
            top: 50%;
            left: 50%;
            animation: celebrationParticle ${2 + Math.random() * 2}s ease-out forwards;
            z-index: 1000;
            pointer-events: none;
        `;

        document.body.appendChild(particle);

        setTimeout(() => {
            particle.remove();
        }, 4000);
    }
}

function showLevelUnlockNotification(levelInfo) {
    if (!levelInfo) return;

    // Create unlock notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        z-index: 10000;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        animation: unlockPulse 2s ease-in-out;
        max-width: 400px;
        border: 3px solid #ffd700;
    `;

    notification.innerHTML = `
        <div style="font-size: 48px; margin-bottom: 15px;">🔓</div>
        <h2 style="margin: 0 0 10px 0; color: #ffd700;">Level Unlocked!</h2>
        <h3 style="margin: 0 0 10px 0;">Level ${levelInfo.level_number}</h3>
        <p style="margin: 0; font-size: 18px;">${levelInfo.title}</p>
        <div style="margin-top: 15px; font-size: 14px; opacity: 0.9;">
            You can now play this level!
        </div>
    `;

    document.body.appendChild(notification);

    // Play unlock sound
    playMissionSound('unlock');

    // Remove notification after 4 seconds
    setTimeout(() => {
        notification.style.animation = 'fadeOut 0.5s ease-out';
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 4000);
}

async function saveMissionResults(accuracy, starsEarned) {
    try {
        const payload = {
            session_id: missionConfig.sessionId,
            questions_answered: missionConfig.totalQuestions, // Ensure this matches required questions
            correct_answers: missionState.correctAnswers,
            wrong_answers: missionConfig.totalQuestions - missionState.correctAnswers,
            points_earned: missionState.score,
            accuracy: accuracy,
            stars_earned: starsEarned,
            game_mode: 'mission'
        };

        console.log('Saving mission results:', payload);
        console.log('Mission completion check: Answered', payload.questions_answered, 'of', missionConfig.totalQuestions, 'required questions');

        const response = await fetch('../api/complete-game-session.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
        });

        const result = await response.json();
        console.log('Mission save result:', result);

        if (!result.success) {
            console.error('Failed to save mission:', result.message);
            // Show error to user
            showError('Failed to save mission progress. Please try again.');
        } else {
            console.log('Mission completed successfully!');

            // Set flag to indicate mission was completed (for mission mode page refresh)
            localStorage.setItem('missionJustCompleted', 'true');

            // Check for final mission completion first
            if (result.final_mission_completed) {
                showFinalMissionCongratulations(result.congratulations_message);
                return; // Don't show other messages for final mission
            }

            // Show appropriate completion message
            if (result.perfect_score) {
                showSuccess('🌟 Perfect Score! All questions correct!');
            } else {
                showInfo('✅ Level completed! Get all questions correct to unlock the next level.');
            }

            // Show level unlock notification if next level was unlocked
            if (result.next_level_unlocked) {
                showLevelUnlockNotification(result.next_level_info);
            } else if (result.unlock_message) {
                // Show unlock requirement message after a delay
                setTimeout(() => {
                    showInfo(result.unlock_message);
                }, 2000);
            }
        }
    } catch (error) {
        console.error('Failed to save mission results:', error);
        showError('Network error while saving mission progress.');
    }
}

// Mission control functions
function pauseMission() {
    // Toggle pause state
    if (missionState.isPaused) {
        // Resume mission
        missionState.isPaused = false;
        if (missionState.missionTimer) {
            startMissionTimer();
        }
        const pauseBtn = document.querySelector('button[onclick="pauseMission()"]');
        if (pauseBtn) {
            pauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            pauseBtn.title = 'Pause Mission';
        }

        // Hide pause overlay if it exists
        const pauseOverlay = document.getElementById('pause-overlay');
        if (pauseOverlay) {
            pauseOverlay.style.display = 'none';
        }

        playMissionSound('resume');
    } else {
        // Pause mission
        missionState.isPaused = true;
        if (missionState.missionTimer) {
            clearInterval(missionState.missionTimer);
        }
        const pauseBtn = document.querySelector('button[onclick="pauseMission()"]');
        if (pauseBtn) {
            pauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            pauseBtn.title = 'Resume Mission';
        }

        // Show pause overlay
        showPauseOverlay();

        playMissionSound('pause');
    }
}

function quitMission() {
    if (confirm('Are you sure you want to quit the mission? Your progress will be lost.')) {
        window.location.href = 'mission-mode.php';
    }
}

function retryMission() {
    window.location.reload();
}

function nextMission() {
    // Set flag to refresh mission mode page
    localStorage.setItem('missionJustCompleted', 'true');
    window.location.href = 'mission-mode.php';
}

function backToMissions() {
    window.location.href = 'mission-mode.php';
}

function showError(message) {
    showNotification(message, 'error');
    setTimeout(() => {
        window.location.href = 'mission-mode.php';
    }, 3000);
}

function showSuccess(message) {
    showNotification(message, 'success');
}

function showInfo(message) {
    showNotification(message, 'info');
}

function showNotification(message, type = 'info') {
    const colors = {
        error: { bg: '#ff4444', icon: '❌' },
        success: { bg: '#44ff44', icon: '✅' },
        info: { bg: '#4488ff', icon: 'ℹ️' }
    };

    const color = colors[type] || colors.info;

    const notificationDiv = document.createElement('div');
    notificationDiv.className = `notification-message ${type}`;
    notificationDiv.innerHTML = `${color.icon} ${message}`;
    notificationDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${color.bg};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        max-width: 300px;
        word-wrap: break-word;
        animation: slideIn 0.3s ease-out;
    `;

    // Add animation styles
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notificationDiv);

    setTimeout(() => {
        notificationDiv.style.animation = 'slideIn 0.3s ease-out reverse';
        setTimeout(() => {
            notificationDiv.remove();
        }, 300);
    }, 4000);
}

function showPauseOverlay() {
    // Create pause overlay if it doesn't exist
    let pauseOverlay = document.getElementById('pause-overlay');
    if (!pauseOverlay) {
        pauseOverlay = document.createElement('div');
        pauseOverlay.id = 'pause-overlay';
        pauseOverlay.className = 'pause-overlay';
        pauseOverlay.innerHTML = `
            <div class="pause-content">
                <div class="pause-icon">
                    <i class="fas fa-pause-circle"></i>
                </div>
                <h2>Mission Paused</h2>
                <p>Click the play button to resume your mission</p>
                <div class="pause-buttons">
                    <button class="btn btn-primary" onclick="pauseMission()">
                        <i class="fas fa-play"></i> Resume
                    </button>
                    <button class="btn btn-secondary" onclick="quitMission()">
                        <i class="fas fa-times"></i> Quit Mission
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(pauseOverlay);
    }
    pauseOverlay.style.display = 'flex';
}

function showFinalMissionCongratulations(message) {
    // Create a special full-screen congratulations overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        overflow: hidden;
    `;

    overlay.innerHTML = `
        <div style="
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
            position: relative;
            animation: bounceIn 1s ease-out;
        ">
            <div style="font-size: 4rem; color: #ffd700; margin-bottom: 1rem;">
                🏆
            </div>
            <h1 style="font-size: 2.5rem; margin-bottom: 1.5rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                🎉 MISSION MASTERY ACHIEVED! 🎉
            </h1>
            <div style="
                font-size: 1.2rem;
                line-height: 1.6;
                margin-bottom: 2rem;
                background: rgba(255,255,255,0.1);
                padding: 1.5rem;
                border-radius: 15px;
            ">
                <p>${message}</p>
            </div>
            <button onclick="celebrateAndReturn()" style="
                padding: 1rem 2rem;
                font-size: 1.2rem;
                border-radius: 50px;
                background: linear-gradient(45deg, #ffd700, #ffed4e);
                color: #333;
                border: none;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
            ">
                <i class="fas fa-home"></i>
                Return as Champion
            </button>
        </div>
    `;

    document.body.appendChild(overlay);
    playMissionSound('victory');
    createCelebrationEffect();
}

function celebrateAndReturn() {
    createCelebrationEffect();
    // Set flag to refresh mission mode page
    localStorage.setItem('missionJustCompleted', 'true');
    setTimeout(() => {
        window.location.href = 'mission-mode.php';
    }, 1000);
}
