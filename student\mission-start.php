<?php
/**
 * Mission Start API - Get mission details for starting
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit();
}

// Get level ID from request
$levelId = isset($_GET['level_id']) ? (int)$_GET['level_id'] : 0;

if (!$levelId) {
    echo json_encode(['success' => false, 'message' => 'Invalid level ID']);
    exit();
}

try {
    // Get mission level details with student progress
    $mission = fetchOne("
        SELECT 
            ml.*,
            COALESCE(smp.status, 'locked') as student_status,
            COALESCE(smp.attempts, 0) as attempts,
            COALESCE(smp.best_score, 0) as best_score,
            COALESCE(smp.stars_earned, 0) as stars_earned,
            smp.completed_at
        FROM mission_levels ml
        LEFT JOIN student_mission_progress smp ON ml.id = smp.level_id AND smp.student_id = :student_id
        WHERE ml.id = :level_id AND ml.is_active = 1
    ", [
        'student_id' => $_SESSION['user_id'],
        'level_id' => $levelId
    ]);

    if (!$mission) {
        echo json_encode(['success' => false, 'message' => 'Mission not found']);
        exit();
    }

    // Check if student can access this mission
    if ($mission['student_status'] === 'locked') {
        $levelNumber = $mission['level_number'];
        if ($levelNumber == 1) {
            $message = "Level 1 should always be available. Please refresh the page.";
        } else {
            $message = "Complete Level " . ($levelNumber - 1) . " to unlock this mission.";
        }

        echo json_encode([
            'success' => false,
            'message' => $message
        ]);
        exit();
    }

    // Format mission data for frontend
    $missionData = [
        'id' => (int)$mission['id'],
        'level_number' => (int)$mission['level_number'],
        'title' => $mission['title'],
        'description' => $mission['description'],
        'difficulty' => $mission['difficulty'],
        'questions_required' => (int)$mission['questions_required'],
        'points_to_unlock' => (int)$mission['points_to_unlock'],
        'points_reward' => (int)$mission['points_reward'],
        'boss_level' => (bool)$mission['boss_level'],
        'student_status' => $mission['student_status'],
        'attempts' => (int)$mission['attempts'],
        'best_score' => (int)$mission['best_score'],
        'stars_earned' => (int)$mission['stars_earned'],
        'completed_at' => $mission['completed_at']
    ];

    echo json_encode([
        'success' => true,
        'mission' => $missionData
    ]);

} catch (Exception $e) {
    error_log("Mission start error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while loading the mission'
    ]);
}
?>
