<?php
/**
 * Get Mission Statistics API
 * Returns real-time mission statistics for the current student
 */

session_start();
require_once '../config/database.php';

// Set JSON header
header('Content-Type: application/json');

try {
    // Check if student is logged in
    if (!isset($_SESSION['student_id'])) {
        echo json_encode(['success' => false, 'error' => 'Not authenticated']);
        exit;
    }

    $studentId = $_SESSION['student_id'];

    // Get all mission levels with student progress
    $missionLevels = fetchAll("
        SELECT
            ml.*,
            COALESCE(smp.status, 'locked') as student_status,
            COALESCE(smp.attempts, 0) as attempts,
            COALESCE(smp.best_score, 0) as best_score,
            COALESCE(smp.stars_earned, 0) as stars_earned,
            smp.completed_at
        FROM mission_levels ml
        LEFT JOIN student_mission_progress smp ON ml.id = smp.level_id AND smp.student_id = :student_id
        WHERE ml.is_active = 1
        ORDER BY ml.level_number
    ", ['student_id' => $studentId]);

    // Calculate mission statistics
    $missionStats = [
        'levels_completed' => count(array_filter($missionLevels, function($l) { 
            return $l['student_status'] === 'completed'; 
        })),
        'total_levels' => count($missionLevels),
        'total_stars' => array_sum(array_column($missionLevels, 'stars_earned')),
        'current_level' => 1
    ];

    // Find current level (first incomplete level)
    foreach ($missionLevels as $level) {
        if ($level['student_status'] !== 'completed') {
            $missionStats['current_level'] = $level['level_number'];
            break;
        }
    }

    // Return the statistics
    echo json_encode([
        'success' => true,
        'total_stars' => $missionStats['total_stars'],
        'levels_completed' => $missionStats['levels_completed'],
        'total_levels' => $missionStats['total_levels'],
        'current_level' => $missionStats['current_level']
    ]);

} catch (Exception $e) {
    error_log("Mission stats API error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch mission statistics'
    ]);
}
?>
