<?php
/**
 * Game Analytics Dashboard for Admin
 * Real-time tracking of Mission Mode and Endless Mode progress
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Get current admin info (if admin_users table exists)
$admin = null;
try {
    $admin = fetchOne("SELECT * FROM admin_users WHERE id = :id", ['id' => $_SESSION['admin_id'] ?? 0]);
} catch (Exception $e) {
    // Admin users table might not exist, use session data
    $admin = ['full_name' => $_SESSION['username'] ?? 'Admin'];
}

// Page title
$pageTitle = "Game Analytics Dashboard";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - LMS Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin-dashboard.css" rel="stylesheet">
    <style>
        /* Match the existing admin dashboard design */
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: #f8fafc;
        }

        .sidebar {
            width: 280px;
            background: #2d3748;
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            object-fit: cover;
        }

        .sidebar-title h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .sidebar-title p {
            margin: 0;
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .nav-item.active {
            background: #805ad5;
            color: white;
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: white;
        }

        .nav-divider {
            height: 1px;
            background: rgba(255,255,255,0.1);
            margin: 1rem 0;
        }

        .main-content {
            margin-left: 280px;
            flex: 1;
            padding: 2rem;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .mission-icon {
            background: #10b981;
        }
        
        .endless-icon {
            background: #f59e0b;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .stat-card {
            text-align: center;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            font-size: 0.85rem;
            color: #64748b;
        }
        
        .leaderboard {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .leaderboard-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            font-weight: bold;
            color: #374151;
        }
        
        .student-progress-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .student-progress-item:hover {
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .student-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .student-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .student-details h4 {
            margin: 0;
            font-size: 0.9rem;
            color: #1e293b;
        }
        
        .student-details p {
            margin: 0;
            font-size: 0.8rem;
            color: #64748b;
        }
        
        .progress-info {
            text-align: right;
        }
        
        .progress-primary {
            font-weight: bold;
            color: #1e293b;
            font-size: 0.9rem;
        }
        
        .progress-secondary {
            font-size: 0.8rem;
            color: #64748b;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-active { background: #10b981; }
        .status-completed { background: #3b82f6; }
        .status-inactive { background: #9ca3af; }
        
        .analytics-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border-left: 4px solid #6366f1;
        }

        .analytics-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
        }

        .analytics-title h1 {
            margin: 0;
            font-size: 2rem;
            color: #1f2937;
            font-weight: 700;
        }

        .analytics-subtitle {
            color: #6b7280;
            margin: 0;
            font-size: 1rem;
        }

        .refresh-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-top: 1rem;
        }

        .auto-refresh-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6b7280;
        }

        .game-modes-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .game-mode-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .mission-mode-section {
            border-left: 4px solid #10b981;
        }

        .endless-mode-section {
            border-left: 4px solid #f59e0b;
        }

        .mode-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .mode-icon {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .mission-icon {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .endless-icon {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .mode-title h3 {
            margin: 0;
            font-size: 1.5rem;
            color: #1f2937;
            font-weight: 600;
        }

        .mode-title p {
            margin: 0;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        .progress-section {
            margin-top: 2rem;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .student-progress {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1.25rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .student-progress:hover {
            border-color: #d1d5db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .student-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }

        .student-details h4 {
            margin: 0;
            font-size: 1rem;
            color: #1f2937;
            font-weight: 600;
        }

        .student-details p {
            margin: 0;
            font-size: 0.875rem;
            color: #6b7280;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
        }

        .level-badge {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .score-display {
            color: #6b7280;
            font-weight: 500;
        }

        .live-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 0.375rem 0.875rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
            margin-bottom: 1rem;
        }

        .live-dot {
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .refresh-btn {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
        }

        .auto-refresh-checkbox {
            margin-right: 0.5rem;
        }

        .no-data {
            text-align: center;
            color: #6b7280;
            font-style: italic;
            padding: 3rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 2px dashed #e2e8f0;
        }

        /* New Analytics Layout */
        .game-modes-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid #f1f5f9;
            padding-bottom: 1rem;
        }

        .tab-btn {
            background: none;
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tab-btn:hover {
            border-color: #6366f1;
            color: #6366f1;
        }

        .tab-btn.active {
            background: #6366f1;
            border-color: #6366f1;
            color: white;
        }

        .analytics-section {
            display: none;
        }

        .analytics-section.active {
            display: block;
        }

        .student-scores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1rem;
        }

        .student-score-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .student-score-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .student-score-card.quick {
            border-left-color: #3b82f6;
        }

        .student-score-card.endless {
            border-left-color: #f59e0b;
        }

        .student-score-card.mission {
            border-left-color: #10b981;
        }

        .student-card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .student-card-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .student-card-info h4 {
            margin: 0;
            font-size: 1.1rem;
            color: #1f2937;
            font-weight: 600;
        }

        .student-card-info p {
            margin: 0;
            font-size: 0.875rem;
            color: #6b7280;
        }

        .student-card-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .loading-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
            grid-column: 1 / -1;
        }

        .loading-state i {
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="game-analytics.php" class="nav-item active">
                    <i class="fas fa-gamepad"></i>
                    <span>Game Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="analytics-header">
                <div class="analytics-title">
                    <i class="fas fa-chart-line" style="color: #6366f1; font-size: 2rem;"></i>
                    <h1>Game Analytics Dashboard</h1>
                </div>
                <p class="analytics-subtitle">Real-time tracking of Mission Mode and Endless Mode progress</p>

                <div class="refresh-controls">
                    <div class="auto-refresh-toggle">
                        <label>
                            <input type="checkbox" id="autoRefresh" checked class="auto-refresh-checkbox">
                            Auto-refresh (30s)
                        </label>
                    </div>
                    <button class="refresh-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh Now
                    </button>
                </div>
            </div>

            <!-- Game Mode Tabs -->
            <div class="game-modes-tabs">
                <button class="tab-btn active" onclick="switchGameMode('quick')">
                    <i class="fas fa-bolt"></i> Quick Game
                </button>
                <button class="tab-btn" onclick="switchGameMode('endless')">
                    <i class="fas fa-infinity"></i> Endless Mode
                </button>
                <button class="tab-btn" onclick="switchGameMode('mission')">
                    <i class="fas fa-flag-checkered"></i> Mission Mode
                </button>
            </div>

            <!-- Quick Game Section -->
            <div id="quick-analytics" class="analytics-section active">
                <div class="section-header">
                    <div class="section-icon" style="background: #3b82f6;">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div>
                        <h2>Quick Game Analytics</h2>
                        <p>Fast-paced quiz performance tracking</p>
                    </div>
                </div>
                <div class="student-scores-grid" id="quick-scores">
                    <div class="loading-state">Loading Quick Game scores...</div>
                </div>
            </div>

            <!-- Endless Mode Section -->
            <div id="endless-analytics" class="analytics-section">
                <div class="section-header">
                    <div class="section-icon endless-icon">
                        <i class="fas fa-infinity"></i>
                    </div>
                    <div>
                        <h2>Endless Mode Analytics</h2>
                        <p>High scores and level progression</p>
                    </div>
                </div>
                <div class="student-scores-grid" id="endless-scores">
                    <div class="loading-state">Loading Endless Mode scores...</div>
                </div>
            </div>

            <!-- Mission Mode Section -->
            <div id="mission-analytics" class="analytics-section">
                <div class="section-header">
                    <div class="section-icon mission-icon">
                        <i class="fas fa-flag-checkered"></i>
                    </div>
                    <div>
                        <h2>Mission Mode Analytics</h2>
                        <p>Level-based progression tracking</p>
                    </div>
                </div>
                <div class="student-scores-grid" id="mission-scores">
                    <div class="loading-state">Loading Mission Mode progress...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin-dashboard.js"></script>
    <script>
        let currentGameMode = 'quick';

        function switchGameMode(mode) {
            currentGameMode = mode;

            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Update sections
            document.querySelectorAll('.analytics-section').forEach(section => section.classList.remove('active'));
            document.getElementById(mode + '-analytics').classList.add('active');

            // Load data for the selected mode
            loadGameModeData(mode);
        }

        function loadGameModeData(mode) {
            const scoresEl = document.getElementById(mode + '-scores');
            scoresEl.innerHTML = '<div class="loading-state"><i class="fas fa-spinner"></i><br>Loading ' + mode + ' data...</div>';

            fetch('api/student-scores.php?mode=' + mode)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.students) {
                        displayStudentScores(data.students, mode, scoresEl);
                    } else {
                        scoresEl.innerHTML = '<div class="loading-state">No data available for ' + mode + ' mode</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading scores:', error);
                    scoresEl.innerHTML = '<div class="loading-state">Error loading scores</div>';
                });
        }

        function displayStudentScores(students, mode, container) {
            if (!students || students.length === 0) {
                container.innerHTML = '<div class="loading-state">No students have played ' + mode + ' mode yet</div>';
                return;
            }

            let html = '';
            students.forEach((student, index) => {
                const initials = (student.first_name.charAt(0) + student.last_name.charAt(0)).toUpperCase();

                let stats = '';
                if (mode === 'quick') {
                    stats = `
                        <div class="stat-item">
                            <div class="stat-value">${student.best_score || 0}</div>
                            <div class="stat-label">Best Score</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${student.games_played || 0}</div>
                            <div class="stat-label">Games Played</div>
                        </div>
                    `;
                } else if (mode === 'endless') {
                    stats = `
                        <div class="stat-item">
                            <div class="stat-value">${student.highest_score || 0}</div>
                            <div class="stat-label">High Score</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${student.current_level || 1}</div>
                            <div class="stat-label">Max Level</div>
                        </div>
                    `;
                } else if (mode === 'mission') {
                    stats = `
                        <div class="stat-item">
                            <div class="stat-value">${student.current_level || 1}</div>
                            <div class="stat-label">Current Level</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${student.missions_completed || 0}</div>
                            <div class="stat-label">Completed</div>
                        </div>
                    `;
                }

                html += `
                    <div class="student-score-card ${mode}">
                        <div class="student-card-header">
                            <div class="student-card-avatar">${initials}</div>
                            <div class="student-card-info">
                                <h4>${student.first_name} ${student.last_name}</h4>
                                <p>Mat No: ${student.student_id}</p>
                            </div>
                        </div>
                        <div class="student-card-stats">
                            ${stats}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Load initial data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadGameModeData('quick');
        });

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            loadGameModeData(currentGameMode);
        }, 30000);

        // Manual refresh function
        function refreshData() {
            loadGameModeData(currentGameMode);
        }
    </script>
</body>
</html>
