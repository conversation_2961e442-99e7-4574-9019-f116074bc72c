<?php
/**
 * Mission Launch API - Create a new mission session
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit();
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$levelId = isset($input['level_id']) ? (int)$input['level_id'] : 0;

if (!$levelId) {
    echo json_encode(['success' => false, 'message' => 'Invalid level ID']);
    exit();
}

try {
    // Get mission level details
    $mission = fetchOne("
        SELECT ml.*, 
               COALESCE(smp.status, 'locked') as student_status
        FROM mission_levels ml
        LEFT JOIN student_mission_progress smp ON ml.id = smp.level_id AND smp.student_id = :student_id
        WHERE ml.id = :level_id AND ml.is_active = 1
    ", [
        'student_id' => $_SESSION['user_id'],
        'level_id' => $levelId
    ]);

    if (!$mission) {
        echo json_encode(['success' => false, 'message' => 'Mission not found']);
        exit();
    }

    // Check if student can access this mission
    if ($mission['student_status'] === 'locked') {
        echo json_encode(['success' => false, 'message' => 'Mission is locked']);
        exit();
    }

    // Get student info for department and level
    $student = fetchOne("
        SELECT department_id, academic_level_id 
        FROM students 
        WHERE id = :id
    ", ['id' => $_SESSION['user_id']]);

    if (!$student) {
        echo json_encode(['success' => false, 'message' => 'Student not found']);
        exit();
    }

    // Check if student has an active mission session
    $activeSession = fetchOne("
        SELECT id FROM game_sessions 
        WHERE student_id = :student_id 
        AND game_mode = 'mission' 
        AND status = 'active'
    ", ['student_id' => $_SESSION['user_id']]);

    if ($activeSession) {
        // Return existing session
        echo json_encode([
            'success' => true,
            'session_id' => $activeSession['id'],
            'message' => 'Resuming existing mission session'
        ]);
        exit();
    }

    // Create new mission session
    $sessionData = [
        'mission_level_id' => $levelId,
        'difficulty' => $mission['difficulty'],
        'questions_required' => $mission['questions_required'],
        'points_reward' => $mission['points_reward'],
        'boss_level' => $mission['boss_level']
    ];

    $sessionId = insertRecord('game_sessions', [
        'student_id' => $_SESSION['user_id'],
        'game_mode' => 'mission',
        'difficulty_level' => strtolower($mission['difficulty']),
        'questions_count' => $mission['questions_required'],
        'lives_remaining' => 3, // Standard lives for mission mode
        'level_reached' => $mission['level_number'],
        'status' => 'active',
        'session_data' => json_encode($sessionData)
    ]);

    if (!$sessionId) {
        echo json_encode(['success' => false, 'message' => 'Failed to create mission session']);
        exit();
    }

    // Update student mission progress
    executeQuery("
        INSERT INTO student_mission_progress (student_id, level_id, status, attempts, unlocked_at) 
        VALUES (:student_id, :level_id, 'in_progress', 1, NOW())
        ON DUPLICATE KEY UPDATE 
            status = 'in_progress',
            attempts = attempts + 1,
            unlocked_at = COALESCE(unlocked_at, NOW())
    ", [
        'student_id' => $_SESSION['user_id'],
        'level_id' => $levelId
    ]);

    echo json_encode([
        'success' => true,
        'session_id' => $sessionId,
        'message' => 'Mission session created successfully'
    ]);

} catch (Exception $e) {
    error_log("Mission launch error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while starting the mission'
    ]);
}
?>
