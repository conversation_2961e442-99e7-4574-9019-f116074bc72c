/* Admin Setup Styles for AI-Powered LMS */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.setup-container {
    width: 100%;
    max-width: 500px;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.setup-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.setup-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
}

.setup-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.logo-container {
    position: relative;
    z-index: 1;
    margin-bottom: 20px;
}

.logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.setup-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
}

.setup-header p {
    font-size: 1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.school-name {
    font-weight: 600;
    font-size: 1.1rem !important;
    margin-top: 8px;
}

.setup-body {
    padding: 40px 30px;
}

.alert {
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: fadeIn 0.3s ease;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.alert i {
    font-size: 1.2rem;
    margin-top: 2px;
}

.success-actions {
    margin-top: 16px;
}

.setup-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.form-group label i {
    color: #6366f1;
    width: 16px;
}

.form-group input {
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-group input:focus {
    outline: none;
    border-color: #6366f1;
    background: white;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #6366f1;
}

.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 8px;
    transition: all 0.3s ease;
}

.password-strength.weak {
    background: #ef4444;
    width: 33%;
}

.password-strength.medium {
    background: #f59e0b;
    width: 66%;
}

.password-strength.strong {
    background: #10b981;
    width: 100%;
}

.security-notice {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    gap: 12px;
    color: #92400e;
}

.security-notice i {
    color: #f59e0b;
    font-size: 1.2rem;
    margin-top: 2px;
}

.security-notice strong {
    color: #92400e;
}

.security-notice p {
    margin-top: 4px;
    font-size: 0.9rem;
    line-height: 1.4;
}

.btn {
    padding: 14px 24px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.btn-setup {
    width: 100%;
    padding: 16px;
    font-size: 1.1rem;
}

.setup-footer {
    background: #f9fafb;
    padding: 24px 30px;
    text-align: center;
    color: #6b7280;
    font-size: 0.9rem;
    border-top: 1px solid #e5e7eb;
}

.version {
    margin-top: 8px;
    font-weight: 500;
    color: #9ca3af;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .setup-container {
        margin: 10px;
    }
    
    .setup-header {
        padding: 30px 20px;
    }
    
    .setup-body {
        padding: 30px 20px;
    }
    
    .setup-footer {
        padding: 20px;
    }
    
    .logo {
        width: 60px;
        height: 60px;
    }
    
    .setup-header h1 {
        font-size: 1.5rem;
    }
}
