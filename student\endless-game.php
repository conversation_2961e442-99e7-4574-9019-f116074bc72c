<?php
/**
 * Endless Mode - Infinite Challenge Game
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

if (!$student) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Get student's best endless score
$bestScore = fetchOne("
    SELECT MAX(points_earned) as best_score 
    FROM game_sessions 
    WHERE student_id = :student_id AND game_mode = 'endless' AND status = 'completed'
", ['student_id' => $student['id']])['best_score'] ?? 0;

// Create new endless game session
$sessionId = insertRecord('game_sessions', [
    'student_id' => $student['id'],
    'game_mode' => 'endless',
    'difficulty_level' => 'progressive',
    'questions_count' => 0, // Unlimited
    'status' => 'active'
]);

if (!$sessionId) {
    die('Failed to create endless game session');
}

$pageTitle = 'Endless Mode - Infinite Challenge';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - LMS</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px 0;
        }

        .endless-game-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 40px);
        }

        .endless-header {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            padding: 20px 25px;
            border-radius: 20px 20px 0 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            flex-shrink: 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .endless-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .game-info h1 {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
            margin: 8px 0 0 0;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .game-stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 600;
            font-size: 0.9rem;
            backdrop-filter: blur(5px);
        }

        .game-actions {
            display: flex;
            gap: 10px;
        }

        .btn-icon {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .btn-icon:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .level-progress-container {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
            font-size: 0.9rem;
        }

        .level-info {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .difficulty-indicator {
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
        }

        .level-progress-bar {
            flex: 1;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .level-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .endless-main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .intro-screen {
            background: white;
            border-radius: 25px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            max-width: 500px;
            width: 100%;
        }

        .intro-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .intro-screen h2 {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .intro-description p {
            font-size: 1.1rem;
            color: #4a5568;
            margin-bottom: 25px;
        }

        .game-rules {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .rule-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            color: #4a5568;
        }

        .rule-item i {
            color: #667eea;
            font-size: 1.2rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 10px;
            }

            .game-stats {
                gap: 10px;
                flex-wrap: wrap;
            }

            .stat-item {
                font-size: 0.8rem;
                padding: 6px 10px;
            }

            .intro-screen {
                padding: 30px 20px;
                margin: 0 10px;
            }

            .game-rules {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .intro-screen h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="endless-game-container">
        <!-- Game Header -->
        <header class="endless-header">
            <div class="header-content">
                <div class="game-info">
                    <div class="endless-badge">
                        <i class="fas fa-infinity"></i>
                        <span>Endless Mode</span>
                    </div>
                    <h1>Infinite Challenge</h1>
                </div>
                
                <div class="game-stats">
                    <div class="stat-item">
                        <i class="fas fa-heart"></i>
                        <span id="lives-count">3</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span id="current-score">0</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-layer-group"></i>
                        <span>Level <span id="current-level">1</span></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-trophy"></i>
                        <span>Best: <?php echo number_format($bestScore); ?></span>
                    </div>
                </div>
                
                <div class="game-actions">
                    <button class="btn-icon" onclick="pauseGame()" title="Pause">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="btn-icon" onclick="quitGame()" title="Quit">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <!-- Level Progress -->
            <div class="level-progress-container">
                <div class="level-info">
                    <span>Level <span id="level-display">1</span></span>
                    <span class="difficulty-indicator" id="difficulty-indicator">Easy</span>
                </div>
                <div class="level-progress-bar">
                    <div class="level-progress-fill" id="level-progress"></div>
                </div>
                <div class="progress-text">
                    <span id="questions-in-level">0</span> / <span id="questions-needed">5</span>
                </div>
            </div>
        </header>

        <!-- Main Game Area -->
        <main class="endless-main">
            <!-- Game Introduction -->
            <div id="game-intro" class="intro-screen">
                <div class="intro-content animate__animated animate__fadeIn">
                    <div class="intro-icon">
                        <i class="fas fa-infinity"></i>
                    </div>
                    <h2>Endless Challenge</h2>
                    <div class="intro-description">
                        <p>Test your knowledge in an infinite challenge!</p>
                        <div class="game-rules">
                            <div class="rule-item">
                                <i class="fas fa-heart"></i>
                                <span>Start with 3 lives</span>
                            </div>
                            <div class="rule-item">
                                <i class="fas fa-chart-line"></i>
                                <span>Difficulty increases every level</span>
                            </div>
                            <div class="rule-item">
                                <i class="fas fa-trophy"></i>
                                <span>Beat your high score</span>
                            </div>
                            <div class="rule-item">
                                <i class="fas fa-gift"></i>
                                <span>Earn bonus lives and power-ups</span>
                            </div>
                        </div>
                    </div>
                    <button class="btn-primary btn-large" onclick="startEndlessGame()">
                        <i class="fas fa-play"></i>
                        Start Challenge
                    </button>
                </div>
            </div>

            <!-- Loading Screen -->
            <div id="loading-screen" class="loading-screen" style="display: none;">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <h2>Generating Questions</h2>
                    <p>AI is creating your infinite challenge...</p>
                    <div class="loading-progress">
                        <div class="loading-bar" id="loading-bar"></div>
                    </div>
                </div>
            </div>

            <!-- Question Container -->
            <div id="question-container" class="question-container" style="display: none;">
                <div class="question-card animate__animated">
                    <div class="question-header">
                        <div class="question-info">
                            <span class="question-number">Question <span id="question-num">1</span></span>
                            <span class="streak-counter">Streak: <span id="streak-count">0</span></span>
                        </div>
                        <div class="lives-display">
                            <div class="life-heart active" data-life="1">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="life-heart active" data-life="2">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="life-heart active" data-life="3">
                                <i class="fas fa-heart"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="question-content">
                        <h2 id="question-text">Loading question...</h2>
                    </div>
                    
                    <div class="options-container">
                        <button class="option-btn" data-option="A" onclick="selectAnswer('A')">
                            <span class="option-letter">A</span>
                            <span class="option-text" id="option-a">Option A</span>
                        </button>
                        <button class="option-btn" data-option="B" onclick="selectAnswer('B')">
                            <span class="option-letter">B</span>
                            <span class="option-text" id="option-b">Option B</span>
                        </button>
                        <button class="option-btn" data-option="C" onclick="selectAnswer('C')">
                            <span class="option-letter">C</span>
                            <span class="option-text" id="option-c">Option C</span>
                        </button>
                        <button class="option-btn" data-option="D" onclick="selectAnswer('D')">
                            <span class="option-letter">D</span>
                            <span class="option-text" id="option-d">Option D</span>
                        </button>
                    </div>
                    
                    <div class="question-actions">
                        <button class="btn-submit" id="submit-btn" onclick="submitAnswer()" disabled>
                            <i class="fas fa-check"></i>
                            Submit Answer
                        </button>
                    </div>
                    
                    <!-- Power-ups -->
                    <div class="powerups-container">
                        <button class="powerup-btn" id="fifty-fifty" onclick="usePowerup('fifty-fifty')" title="50/50">
                            <i class="fas fa-divide"></i>
                            <span id="fifty-fifty-count">3</span>
                        </button>
                        <button class="powerup-btn" id="extra-time" onclick="usePowerup('extra-time')" title="Extra Time">
                            <i class="fas fa-clock"></i>
                            <span id="extra-time-count">2</span>
                        </button>
                        <button class="powerup-btn" id="skip-question" onclick="usePowerup('skip')" title="Skip Question">
                            <i class="fas fa-forward"></i>
                            <span id="skip-count">1</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Game Over Screen -->
            <div id="game-over" class="game-over-screen" style="display: none;">
                <div class="game-over-content animate__animated animate__bounceIn">
                    <div class="game-over-icon">
                        <i class="fas fa-skull-crossbones"></i>
                    </div>
                    <h1>Game Over</h1>
                    <div class="final-stats">
                        <div class="stat-card">
                            <div class="stat-value" id="final-score">0</div>
                            <div class="stat-label">Final Score</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="final-level">1</div>
                            <div class="stat-label">Level Reached</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="final-streak">0</div>
                            <div class="stat-label">Best Streak</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="questions-answered">0</div>
                            <div class="stat-label">Questions Answered</div>
                        </div>
                    </div>
                    
                    <div class="achievement-display" id="achievement-display" style="display: none;">
                        <h3>🏆 New Achievement!</h3>
                        <div class="achievement-item">
                            <span id="achievement-name"></span>
                        </div>
                    </div>
                    
                    <div class="game-over-actions">
                        <button class="btn-primary" onclick="playAgain()">
                            <i class="fas fa-redo"></i>
                            Play Again
                        </button>
                        <button class="btn-secondary" onclick="backToDashboard()">
                            <i class="fas fa-home"></i>
                            Dashboard
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Game Data -->
    <script>
        const endlessConfig = {
            sessionId: <?php echo $sessionId; ?>,
            studentId: <?php echo $student['id']; ?>,
            departmentId: <?php echo $student['department_id']; ?>,
            academicLevelId: <?php echo $student['academic_level_id']; ?>,
            bestScore: <?php echo $bestScore; ?>
        };
    </script>
    
    <script src="../assets/js/endless-game.js"></script>
</body>
</html>
