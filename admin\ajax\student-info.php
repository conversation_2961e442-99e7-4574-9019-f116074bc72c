<?php
// Set JSON header first
header('Content-Type: application/json');

// Suppress any output before JSON
ob_start();

try {
    require_once '../../config/database.php';

    // Start secure session
    startSecureSession();

    // Check if user is admin
    if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
        ob_clean();
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Access denied']);
        exit;
    }

} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'Configuration error']);
    exit;
}

$studentId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$studentId) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'Invalid student ID']);
    exit;
}

try {
    // Get student details
    $student = fetchOne("
        SELECT id, first_name, last_name, email, student_id
        FROM students
        WHERE id = :id
    ", ['id' => $studentId]);

    if (!$student) {
        ob_clean();
        echo json_encode(['success' => false, 'message' => 'Student not found']);
        exit;
    }

    ob_clean();
    echo json_encode([
        'success' => true,
        'student' => $student
    ]);

} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'Error loading student information: ' . $e->getMessage()]);
}
?>
