<?php
/**
 * Admin Login Page for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if admin is already logged in
startSecureSession();
if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin') {
    header('Location: dashboard.php');
    exit();
}

// Check if admin setup is complete
$adminSetup = fetchOne("SELECT is_setup FROM admins WHERE username = 'admin'");
if (!$adminSetup || !$adminSetup['is_setup']) {
    header('Location: ../setup-admin.php');
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password.';
    } else {
        // Fetch admin details
        $admin = fetchOne(
            "SELECT id, username, password_hash, email, last_login FROM admins WHERE username = :username AND is_setup = 1",
            ['username' => $username]
        );
        
        if ($admin && verifyPassword($password, $admin['password_hash'])) {
            // Create session
            $sessionId = generateSessionId();
            
            // Store session in database
            insertRecord('user_sessions', [
                'id' => $sessionId,
                'user_id' => $admin['id'],
                'user_type' => 'admin',
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'],
                'expires_at' => date('Y-m-d H:i:s', strtotime('+24 hours'))
            ]);
            
            // Update last login
            updateRecord('admins', 
                ['last_login' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $admin['id']]
            );
            
            // Set session variables
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['user_type'] = 'admin';
            $_SESSION['username'] = $admin['username'];
            $_SESSION['email'] = $admin['email'];
            $_SESSION['session_id'] = $sessionId;
            
            header('Location: dashboard.php');
            exit();
        } else {
            $error = 'Invalid username or password.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="logo-container">
                    <img src="../images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="logo">
                </div>
                <h1>Admin Portal</h1>
                <p>AI-Powered Learning Management System</p>
                <p class="school-name">Ogbonnaya Onu Polytechnic, Aba</p>
            </div>
            
            <div class="auth-body">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="auth-form">
                    <div class="form-group">
                        <label for="username">
                            <i class="fas fa-user-shield"></i>
                            Username
                        </label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            required 
                            placeholder="Enter admin username"
                            value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : 'admin'; ?>"
                            autocomplete="username"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            Password
                        </label>
                        <div class="password-input">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                required 
                                placeholder="Enter your password"
                                autocomplete="current-password"
                            >
                            <button type="button" class="toggle-password" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember_me">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        Login to Admin Panel
                    </button>
                </form>
                
                <div class="auth-links">
                    <a href="../index.php" class="link-secondary">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </a>
                    <a href="../student/login.php" class="link-secondary">
                        <i class="fas fa-graduation-cap"></i>
                        Student Login
                    </a>
                </div>
            </div>
            
            <div class="auth-footer">
                <div class="security-info">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure Admin Access</span>
                </div>
                <p>&copy; 2025 Ogbonnaya Onu Polytechnic, Aba. All rights reserved.</p>
            </div>
        </div>
        
        <div class="background-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>
    </div>
    
    <script src="../assets/js/auth.js"></script>
    <script>
        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleButton = passwordInput.nextElementSibling;
            const toggleIcon = toggleButton.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
