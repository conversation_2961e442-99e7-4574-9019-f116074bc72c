// Endless Mode JavaScript - Infinite Challenge Game

let endlessState = {
    currentQuestion: 0,
    score: 0,
    lives: 3,
    level: 1,
    correctAnswers: 0,
    streak: 0,
    bestStreak: 0,
    selectedAnswer: null,
    questions: [],
    questionsInLevel: 0,
    questionsNeededForLevel: 5,
    powerups: {
        fiftyFifty: 3,
        extraTime: 2,
        skip: 1
    },
    difficulty: 'easy',
    difficultyMultiplier: 1,
    isGameActive: false,
    isPaused: false,
    gameTimer: null,
    questionTimer: null
};

// Initialize endless game when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeEndlessGame();
});

function initializeEndlessGame() {
    // Show game intro
    showGameIntro();
    updatePowerupDisplay();
}

function showGameIntro() {
    document.getElementById('game-intro').style.display = 'flex';
}

async function startEndlessGame() {
    try {
        // Hide intro and show loading
        document.getElementById('game-intro').style.display = 'none';
        document.getElementById('loading-screen').style.display = 'flex';
        
        // Show loading animation
        showLoadingProgress();
        
        // Initialize game state
        resetGameState();
        
        // Generate initial questions
        await generateEndlessQuestions();
        
        // Start the game
        beginEndlessGameplay();
        
    } catch (error) {
        console.error('Failed to start endless game:', error);
        showError('Failed to start endless game. Please try again.');
    }
}

function resetGameState() {
    endlessState.currentQuestion = 0;
    endlessState.score = 0;
    endlessState.lives = 3;
    endlessState.level = 1;
    endlessState.correctAnswers = 0;
    endlessState.streak = 0;
    endlessState.bestStreak = 0;
    endlessState.questionsInLevel = 0;
    endlessState.questionsNeededForLevel = 5;
    endlessState.difficulty = 'easy';
    endlessState.difficultyMultiplier = 1;
    endlessState.isGameActive = true;
    
    // Reset powerups
    endlessState.powerups = {
        fiftyFifty: 3,
        extraTime: 2,
        skip: 1
    };
}

function showLoadingProgress() {
    const loadingBar = document.getElementById('loading-bar');
    let progress = 0;
    
    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 100) progress = 100;
        
        loadingBar.style.width = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
        }
    }, 150);
}

async function generateEndlessQuestions() {
    try {
        const response = await fetch('../api/generate-game-questions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: endlessConfig.sessionId,
                difficulty: endlessState.difficulty,
                question_count: 10, // Generate in batches
                department_id: endlessConfig.departmentId,
                academic_level_id: endlessConfig.academicLevelId,
                game_mode: 'endless'
            })
        });

        const data = await response.json();
        
        if (data.success) {
            endlessState.questions = data.questions;
        } else {
            throw new Error(data.message || 'Failed to generate questions');
        }
        
    } catch (error) {
        console.error('Error generating questions:', error);
        throw error; // Let the calling function handle the error
    }
}

function generateSampleQuestions() {
    const sampleQuestions = [];
    for (let i = 1; i <= 10; i++) {
        sampleQuestions.push({
            id: i,
            question: `Endless Challenge Question ${i}: What is the correct answer for this ${endlessState.difficulty} level question?`,
            option_a: "Option A - First choice",
            option_b: "Option B - Second choice", 
            option_c: "Option C - Third choice",
            option_d: "Option D - Fourth choice",
            correct_answer: ['A', 'B', 'C', 'D'][Math.floor(Math.random() * 4)],
            difficulty: endlessState.difficulty
        });
    }
    return sampleQuestions;
}

function beginEndlessGameplay() {
    // Hide loading screen and show question container
    document.getElementById('loading-screen').style.display = 'none';
    document.getElementById('question-container').style.display = 'block';
    
    // Update displays
    updateGameDisplay();
    updatePowerupDisplay();
    
    // Load first question
    loadQuestion();
    
    // Play game start sound
    playEndlessSound('start');
}

function loadQuestion() {
    // Check if we need more questions
    if (endlessState.currentQuestion >= endlessState.questions.length) {
        generateMoreQuestions();
        return;
    }
    
    const question = endlessState.questions[endlessState.currentQuestion];
    endlessState.selectedAnswer = null;
    
    // Update question display
    document.getElementById('question-num').textContent = endlessState.currentQuestion + 1;
    document.getElementById('question-text').textContent = question.question;
    document.getElementById('option-a').textContent = question.option_a;
    document.getElementById('option-b').textContent = question.option_b;
    document.getElementById('option-c').textContent = question.option_c;
    document.getElementById('option-d').textContent = question.option_d;
    
    // Reset option buttons
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
        btn.classList.remove('selected', 'correct', 'incorrect', 'disabled');
        btn.disabled = false;
        btn.style.display = 'flex'; // Show all options (in case 50/50 was used)
    });
    
    // Disable submit button
    document.getElementById('submit-btn').disabled = true;
    document.getElementById('submit-btn').classList.remove('pulse-animation');
    
    // Add entrance animation
    const questionCard = document.querySelector('.question-card');
    questionCard.classList.remove('animate__fadeInUp');
    questionCard.classList.add('animate__fadeInUp');
}

async function generateMoreQuestions() {
    try {
        await generateEndlessQuestions();
        loadQuestion();
    } catch (error) {
        console.error('Failed to generate more questions:', error);
        gameOver();
    }
}

function selectAnswer(option) {
    if (!endlessState.isGameActive || endlessState.selectedAnswer) return;
    
    endlessState.selectedAnswer = option;
    
    // Update UI with enhanced animations
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
        btn.classList.remove('selected');
        if (btn.dataset.option === option) {
            btn.classList.add('selected');
            
            // Add selection animation
            btn.style.transform = 'scale(1.05)';
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
            }, 200);
            
            // Create ripple effect
            createRippleEffect(btn);
        }
    });
    
    // Play selection sound
    playEndlessSound('select');
    
    // Enable submit button
    const submitBtn = document.getElementById('submit-btn');
    submitBtn.disabled = false;
    submitBtn.classList.add('pulse-animation');
    
    // Add haptic feedback if supported
    if (navigator.vibrate) {
        navigator.vibrate(50);
    }
}

function createRippleEffect(element) {
    const ripple = document.createElement('div');
    ripple.className = 'ripple-effect';
    
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        background: rgba(139, 92, 246, 0.6);
        transform: scale(0);
        animation: ripple 0.6s linear;
        top: 50%;
        left: 50%;
        margin-left: -${size/2}px;
        margin-top: -${size/2}px;
        pointer-events: none;
        z-index: 10;
    `;
    
    element.style.position = 'relative';
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

function submitAnswer() {
    if (!endlessState.selectedAnswer || !endlessState.isGameActive) return;
    
    const question = endlessState.questions[endlessState.currentQuestion];
    const isCorrect = endlessState.selectedAnswer === question.correct_answer;
    
    // Show answer feedback
    showAnswerFeedback(isCorrect, question.correct_answer);
    
    if (isCorrect) {
        handleCorrectAnswer();
    } else {
        handleIncorrectAnswer();
    }
    
    // Move to next question after delay (respecting pause state)
    const checkAndProceed = () => {
        if (endlessState.isPaused) {
            setTimeout(checkAndProceed, 100); // Check again in 100ms if paused
            return;
        }
        if (endlessState.isGameActive) {
            nextQuestion();
        }
    };
    setTimeout(checkAndProceed, 2000);
}

function handleCorrectAnswer() {
    endlessState.correctAnswers++;
    endlessState.streak++;
    endlessState.questionsInLevel++;
    
    if (endlessState.streak > endlessState.bestStreak) {
        endlessState.bestStreak = endlessState.streak;
    }
    
    // Calculate points with multipliers
    const basePoints = 100;
    const streakBonus = Math.min(endlessState.streak * 10, 200);
    const levelBonus = (endlessState.level - 1) * 50;
    const points = Math.round((basePoints + streakBonus + levelBonus) * endlessState.difficultyMultiplier);
    
    endlessState.score += points;
    
    // Show floating points
    showFloatingText(`+${points}`, 'success');
    
    // Check for level up
    if (endlessState.questionsInLevel >= endlessState.questionsNeededForLevel) {
        levelUp();
    }
    
    // Update displays
    updateGameDisplay();
    updateLevelProgress();
    
    // Play success sound
    playEndlessSound('correct');
    
    // Show streak effects
    if (endlessState.streak >= 5) {
        showStreakEffect();
    }
}

function handleIncorrectAnswer() {
    endlessState.lives--;
    endlessState.streak = 0;
    
    updateGameDisplay();
    updateLivesDisplay();
    
    playEndlessSound('incorrect');
    shakeScreen();
    
    if (endlessState.lives <= 0) {
        const checkAndGameOver = () => {
            if (endlessState.isPaused) {
                setTimeout(checkAndGameOver, 100); // Check again in 100ms if paused
                return;
            }
            gameOver();
        };
        setTimeout(checkAndGameOver, 2000);
    }
}

function levelUp() {
    endlessState.level++;
    endlessState.questionsInLevel = 0;
    endlessState.questionsNeededForLevel = Math.min(5 + endlessState.level, 10);
    
    // Increase difficulty
    updateDifficulty();
    
    // Award bonus life every 3 levels
    if (endlessState.level % 3 === 0 && endlessState.lives < 5) {
        endlessState.lives++;
        showFloatingText('Bonus Life!', 'bonus');
        playEndlessSound('bonus');
    }
    
    // Award powerups every 5 levels
    if (endlessState.level % 5 === 0) {
        awardPowerups();
    }
    
    // Show level up animation
    showLevelUpEffect();
    
    updateGameDisplay();
    updateLevelProgress();
}

function updateDifficulty() {
    if (endlessState.level <= 3) {
        endlessState.difficulty = 'easy';
        endlessState.difficultyMultiplier = 1;
    } else if (endlessState.level <= 7) {
        endlessState.difficulty = 'medium';
        endlessState.difficultyMultiplier = 1.5;
    } else {
        endlessState.difficulty = 'hard';
        endlessState.difficultyMultiplier = 2;
    }
    
    document.getElementById('difficulty-indicator').textContent = 
        endlessState.difficulty.charAt(0).toUpperCase() + endlessState.difficulty.slice(1);
}

function awardPowerups() {
    endlessState.powerups.fiftyFifty++;
    endlessState.powerups.extraTime++;
    if (endlessState.level % 10 === 0) {
        endlessState.powerups.skip++;
    }
    
    updatePowerupDisplay();
    showFloatingText('Powerups Awarded!', 'bonus');
}

function usePowerup(type) {
    if (endlessState.powerups[type.replace('-', '')] <= 0) return;
    
    switch(type) {
        case 'fifty-fifty':
            if (endlessState.powerups.fiftyFifty > 0) {
                endlessState.powerups.fiftyFifty--;
                applyFiftyFifty();
            }
            break;
        case 'extra-time':
            if (endlessState.powerups.extraTime > 0) {
                endlessState.powerups.extraTime--;
                applyExtraTime();
            }
            break;
        case 'skip':
            if (endlessState.powerups.skip > 0) {
                endlessState.powerups.skip--;
                skipQuestion();
            }
            break;
    }
    
    updatePowerupDisplay();
    playEndlessSound('powerup');
}

function applyFiftyFifty() {
    const question = endlessState.questions[endlessState.currentQuestion];
    const correctAnswer = question.correct_answer;
    const options = ['A', 'B', 'C', 'D'];
    const incorrectOptions = options.filter(opt => opt !== correctAnswer);
    
    // Hide 2 incorrect options
    const toHide = incorrectOptions.slice(0, 2);
    toHide.forEach(option => {
        const btn = document.querySelector(`[data-option="${option}"]`);
        btn.style.display = 'none';
    });
    
    showFloatingText('50/50 Used!', 'powerup');
}

function applyExtraTime() {
    // This would extend time in a timed mode
    showFloatingText('Extra Time!', 'powerup');
}

function skipQuestion() {
    showFloatingText('Question Skipped!', 'powerup');
    const checkAndSkip = () => {
        if (endlessState.isPaused) {
            setTimeout(checkAndSkip, 100); // Check again in 100ms if paused
            return;
        }
        nextQuestion();
    };
    setTimeout(checkAndSkip, 1000);
}

function nextQuestion() {
    endlessState.currentQuestion++;
    loadQuestion();
}

function showAnswerFeedback(isCorrect, correctAnswer) {
    const optionBtns = document.querySelectorAll('.option-btn');
    
    optionBtns.forEach(btn => {
        btn.disabled = true;
        
        if (btn.dataset.option === correctAnswer) {
            btn.classList.add('correct');
            btn.style.animation = 'correctAnswer 0.6s ease-in-out';
        } else if (btn.dataset.option === endlessState.selectedAnswer && !isCorrect) {
            btn.classList.add('incorrect');
            btn.style.animation = 'incorrectAnswer 0.6s ease-in-out';
        }
    });
    
    // Add haptic feedback
    if (navigator.vibrate) {
        navigator.vibrate(isCorrect ? [100] : [100, 50, 100]);
    }
}

function updateGameDisplay() {
    document.getElementById('current-score').textContent = endlessState.score.toLocaleString();
    document.getElementById('current-level').textContent = endlessState.level;
    document.getElementById('level-display').textContent = endlessState.level;
    document.getElementById('lives-count').textContent = endlessState.lives;
    document.getElementById('streak-count').textContent = endlessState.streak;
}

function updateLevelProgress() {
    const progress = (endlessState.questionsInLevel / endlessState.questionsNeededForLevel) * 100;
    document.getElementById('level-progress').style.width = progress + '%';
    document.getElementById('questions-in-level').textContent = endlessState.questionsInLevel;
    document.getElementById('questions-needed').textContent = endlessState.questionsNeededForLevel;
}

function updateLivesDisplay() {
    const hearts = document.querySelectorAll('.life-heart');
    hearts.forEach((heart, index) => {
        if (index >= endlessState.lives) {
            heart.classList.remove('active');
            heart.classList.add('lost');
        }
    });
}

function updatePowerupDisplay() {
    document.getElementById('fifty-fifty-count').textContent = endlessState.powerups.fiftyFifty;
    document.getElementById('extra-time-count').textContent = endlessState.powerups.extraTime;
    document.getElementById('skip-count').textContent = endlessState.powerups.skip;
    
    // Disable buttons if no powerups left
    document.getElementById('fifty-fifty').disabled = endlessState.powerups.fiftyFifty <= 0;
    document.getElementById('extra-time').disabled = endlessState.powerups.extraTime <= 0;
    document.getElementById('skip-question').disabled = endlessState.powerups.skip <= 0;
}

function showFloatingText(text, type) {
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        bonus: '#f59e0b',
        powerup: '#8b5cf6'
    };
    
    const floatingText = document.createElement('div');
    floatingText.className = `floating-text ${type}`;
    floatingText.textContent = text;
    floatingText.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        font-weight: bold;
        color: ${colors[type] || '#10b981'};
        z-index: 1000;
        pointer-events: none;
        animation: floatUp 2s ease-out forwards;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    `;
    
    document.body.appendChild(floatingText);
    
    setTimeout(() => {
        floatingText.remove();
    }, 2000);
}

function showStreakEffect() {
    // Create streak particles
    for (let i = 0; i < 30; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: fixed;
            width: 6px;
            height: 6px;
            background: #8b5cf6;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            animation: streakParticle ${1.5 + Math.random()}s ease-out forwards;
            z-index: 1000;
            pointer-events: none;
        `;
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
            particle.remove();
        }, 2500);
    }
}

function showLevelUpEffect() {
    showFloatingText(`Level ${endlessState.level}!`, 'bonus');
    
    // Create level up particles
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: fixed;
            width: 8px;
            height: 8px;
            background: ${['#8b5cf6', '#7c3aed', '#10b981', '#f59e0b'][Math.floor(Math.random() * 4)]};
            border-radius: 50%;
            top: 50%;
            left: 50%;
            animation: levelUpParticle ${2 + Math.random() * 2}s ease-out forwards;
            z-index: 1000;
            pointer-events: none;
        `;
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
            particle.remove();
        }, 4000);
    }
}

function shakeScreen() {
    const gameContainer = document.querySelector('.endless-game-container');
    gameContainer.style.animation = 'screenShake 0.5s ease-in-out';
    
    setTimeout(() => {
        gameContainer.style.animation = '';
    }, 500);
}

function gameOver() {
    endlessState.isGameActive = false;
    
    // Save game results
    saveEndlessResults();
    
    // Update final stats
    document.getElementById('final-score').textContent = endlessState.score.toLocaleString();
    document.getElementById('final-level').textContent = endlessState.level;
    document.getElementById('final-streak').textContent = endlessState.bestStreak;
    document.getElementById('questions-answered').textContent = endlessState.currentQuestion;
    
    // Check for achievements
    checkAchievements();
    
    // Show game over screen
    document.getElementById('question-container').style.display = 'none';
    document.getElementById('game-over').style.display = 'flex';
    
    playEndlessSound('gameOver');
}

function checkAchievements() {
    const achievements = [];
    
    if (endlessState.score > endlessConfig.bestScore) {
        achievements.push('New High Score!');
    }
    
    if (endlessState.level >= 10) {
        achievements.push('Level Master');
    }
    
    if (endlessState.bestStreak >= 10) {
        achievements.push('Streak Champion');
    }
    
    if (achievements.length > 0) {
        const achievementDisplay = document.getElementById('achievement-display');
        document.getElementById('achievement-name').textContent = achievements[0];
        achievementDisplay.style.display = 'block';
    }
}

async function saveEndlessResults() {
    try {
        await fetch('../api/complete-game-session.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: endlessConfig.sessionId,
                questions_answered: endlessState.currentQuestion,
                correct_answers: endlessState.correctAnswers,
                wrong_answers: endlessState.currentQuestion - endlessState.correctAnswers,
                points_earned: endlessState.score,
                level_reached: endlessState.level,
                best_streak: endlessState.bestStreak,
                game_mode: 'endless'
            })
        });
    } catch (error) {
        console.error('Failed to save endless results:', error);
    }
}

// Enhanced sound system for endless mode
function playEndlessSound(type) {
    if (!window.audioContext) {
        window.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
    
    const ctx = window.audioContext;
    
    switch(type) {
        case 'start':
            playMelody([523.25, 659.25, 783.99, 1046.50], [0.2, 0.2, 0.2, 0.4]);
            break;
        case 'correct':
            playMelody([659.25, 783.99], [0.2, 0.3]);
            break;
        case 'incorrect':
            playMelody([220, 196], [0.2, 0.4]);
            break;
        case 'bonus':
            playMelody([783.99, 1046.50, 1318.51], [0.2, 0.2, 0.4]);
            break;
        case 'powerup':
            playMelody([1046.50, 1318.51, 1567.98], [0.15, 0.15, 0.3]);
            break;
        case 'gameOver':
            playMelody([196, 174.61, 146.83, 130.81], [0.3, 0.3, 0.3, 0.6]);
            break;
        case 'select':
            playTone(800, 0.1);
            break;
    }
}

function playMelody(frequencies, durations) {
    const ctx = window.audioContext;
    let currentTime = ctx.currentTime;
    
    frequencies.forEach((freq, index) => {
        const osc = ctx.createOscillator();
        const gain = ctx.createGain();
        
        osc.connect(gain);
        gain.connect(ctx.destination);
        
        osc.frequency.setValueAtTime(freq, currentTime);
        gain.gain.setValueAtTime(0.2, currentTime);
        gain.gain.exponentialRampToValueAtTime(0.01, currentTime + durations[index]);
        
        osc.start(currentTime);
        osc.stop(currentTime + durations[index]);
        
        currentTime += durations[index];
    });
}

function playTone(frequency, duration) {
    const ctx = window.audioContext;
    const osc = ctx.createOscillator();
    const gain = ctx.createGain();
    
    osc.connect(gain);
    gain.connect(ctx.destination);
    
    osc.frequency.setValueAtTime(frequency, ctx.currentTime);
    gain.gain.setValueAtTime(0.1, ctx.currentTime);
    gain.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + duration);
    
    osc.start(ctx.currentTime);
    osc.stop(ctx.currentTime + duration);
}

// Game control functions
function pauseGame() {
    // Toggle pause state
    if (endlessState.isPaused) {
        // Resume game
        endlessState.isPaused = false;

        // Update pause button
        const pauseBtn = document.querySelector('button[onclick="pauseGame()"]');
        if (pauseBtn) {
            pauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            pauseBtn.title = 'Pause Game';
        }

        // Hide pause overlay
        const pauseOverlay = document.getElementById('pause-overlay');
        if (pauseOverlay) {
            pauseOverlay.style.display = 'none';
        }

        playEndlessSound('correct'); // Use existing sound for resume
    } else {
        // Pause game
        endlessState.isPaused = true;

        // Update pause button
        const pauseBtn = document.querySelector('button[onclick="pauseGame()"]');
        if (pauseBtn) {
            pauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            pauseBtn.title = 'Resume Game';
        }

        // Show pause overlay
        showPauseOverlay();

        playEndlessSound('incorrect'); // Use existing sound for pause
    }
}

function quitGame() {
    if (confirm('Are you sure you want to quit? Your progress will be lost.')) {
        window.location.href = 'dashboard.php';
    }
}

function playAgain() {
    window.location.reload();
}

function backToDashboard() {
    window.location.href = 'dashboard.php';
}

function showPauseOverlay() {
    // Remove any existing pause overlay first
    const existingOverlay = document.getElementById('pause-overlay');
    if (existingOverlay) {
        existingOverlay.remove();
    }

    // Create new pause overlay
    const pauseOverlay = document.createElement('div');
    pauseOverlay.id = 'pause-overlay';
    pauseOverlay.className = 'pause-overlay';
    pauseOverlay.innerHTML = `
        <div class="pause-content">
            <div class="pause-icon">
                <i class="fas fa-pause-circle"></i>
            </div>
            <h2>Game Paused</h2>
            <p>Take a break! Click the play button to resume your endless challenge</p>
            <div class="pause-stats">
                <div class="stat-item">
                    <span class="stat-number">Level ${endlessState.level}</span>
                    <span class="stat-label">Current Level</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${endlessState.score}</span>
                    <span class="stat-label">Current Score</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${endlessState.streak}</span>
                    <span class="stat-label">Current Streak</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${endlessState.lives}</span>
                    <span class="stat-label">Lives Remaining</span>
                </div>
            </div>
            <div class="pause-buttons">
                <button class="btn btn-primary" onclick="pauseGame()">
                    <i class="fas fa-play"></i> Resume Game
                </button>
                <button class="btn btn-secondary" onclick="quitGame()">
                    <i class="fas fa-times"></i> Quit Game
                </button>
            </div>
        </div>
    `;

    // Add overlay to body
    document.body.appendChild(pauseOverlay);

    // Show overlay with animation
    setTimeout(() => {
        pauseOverlay.style.display = 'flex';
    }, 10);
}

function showError(message) {
    alert(message);
    window.location.href = 'dashboard.php';
}
