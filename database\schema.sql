-- AI-Powered LMS Database Schema for Ogbonnaya Onu Polytechnic
-- Created: 2025-06-25

CREATE DATABASE IF NOT EXISTS lms_ogbonnaya_onu;
USE lms_ogbonnaya_onu;

-- Admin table for system administrators
CREATE TABLE IF NOT EXISTS admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL DEFAULT 'admin',
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_setup BOOLEAN DEFAULT FALSE
);

-- Departments table (31 courses)
CREATE TABLE IF NOT EXISTS departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Academic levels
CREATE TABLE IF NOT EXISTS academic_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level_name VARCHAR(10) NOT NULL, -- ND1, ND2, HND1, HND2
    level_code VARCHAR(5) NOT NULL,
    description VARCHAR(100)
);

-- Security questions for password reset
CREATE TABLE IF NOT EXISTS security_questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    question TEXT NOT NULL
);

-- Students table
CREATE TABLE IF NOT EXISTS students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(15),
    password_hash VARCHAR(255) NOT NULL,
    department_id INT NOT NULL,
    academic_level_id INT NOT NULL,
    profile_image VARCHAR(255) DEFAULT 'default-avatar.png',
    is_approved BOOLEAN DEFAULT FALSE,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    total_points INT DEFAULT 0,
    current_streak INT DEFAULT 0,
    longest_streak INT DEFAULT 0,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id)
);

-- Student security answers
CREATE TABLE IF NOT EXISTS student_security_answers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    question_id INT NOT NULL,
    answer_hash VARCHAR(255) NOT NULL,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES security_questions(id)
);

-- Note: Subjects table removed - questions now link directly to department + academic level

-- Questions generated by AI
CREATE TABLE IF NOT EXISTS questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    academic_level_id INT NOT NULL,
    question_text TEXT NOT NULL,
    option_a VARCHAR(255) NOT NULL,
    option_b VARCHAR(255) NOT NULL,
    option_c VARCHAR(255) NOT NULL,
    option_d VARCHAR(255) NOT NULL,
    correct_answer ENUM('A', 'B', 'C', 'D') NOT NULL,
    difficulty_level ENUM('Easy', 'Medium', 'Hard') DEFAULT 'Medium',
    explanation TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id)
);

-- Quiz sessions
CREATE TABLE IF NOT EXISTS quiz_sessions (
    id VARCHAR(50) PRIMARY KEY,
    student_id INT NOT NULL,
    department_id INT NOT NULL,
    academic_level_id INT NOT NULL,
    total_questions INT NOT NULL,
    correct_answers INT DEFAULT 0,
    score INT DEFAULT 0,
    score_percentage DECIMAL(5,2) DEFAULT 0.00,
    difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
    time_taken INT DEFAULT 0, -- in seconds
    xp_earned INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress',
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id),
    INDEX idx_student (student_id),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);

-- Quiz questions (linking quiz sessions to questions)
CREATE TABLE IF NOT EXISTS quiz_questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    quiz_session_id VARCHAR(50) NOT NULL,
    question_id VARCHAR(50) NOT NULL, -- Can be temp ID for AI-generated questions
    question_number INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_quiz_session (quiz_session_id),
    INDEX idx_question_number (quiz_session_id, question_number)
);

-- Quiz answers (student answers to quiz questions)
CREATE TABLE IF NOT EXISTS quiz_answers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    quiz_session_id VARCHAR(50) NOT NULL,
    question_id VARCHAR(50) NOT NULL,
    question_number INT NOT NULL,
    selected_answer ENUM('A', 'B', 'C', 'D'),
    is_correct BOOLEAN DEFAULT FALSE,
    time_taken INT DEFAULT 0, -- in seconds
    answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_quiz_session (quiz_session_id),
    INDEX idx_question_number (quiz_session_id, question_number)
);

-- Individual question attempts
CREATE TABLE IF NOT EXISTS question_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    quiz_session_id VARCHAR(50) NOT NULL,
    question_id VARCHAR(50) NOT NULL,
    selected_answer ENUM('A', 'B', 'C', 'D'),
    is_correct BOOLEAN DEFAULT FALSE,
    time_taken INT DEFAULT 0, -- in seconds
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_session_id) REFERENCES quiz_sessions(id),
    INDEX idx_quiz_session (quiz_session_id),
    INDEX idx_question (question_id)
);

-- Reward types and badges
CREATE TABLE IF NOT EXISTS reward_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(100), -- emoji or icon class
    points_required INT DEFAULT 0,
    badge_image VARCHAR(255)
);

-- Student rewards and achievements
CREATE TABLE IF NOT EXISTS student_rewards (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    reward_type_id INT NOT NULL,
    points_earned INT DEFAULT 0,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    quiz_session_id VARCHAR(50),
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (reward_type_id) REFERENCES reward_types(id),
    FOREIGN KEY (quiz_session_id) REFERENCES quiz_sessions(id)
);

-- Student progress tracking
CREATE TABLE IF NOT EXISTS student_progress (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    department_id INT NOT NULL,
    academic_level_id INT NOT NULL,
    questions_attempted INT DEFAULT 0,
    questions_correct INT DEFAULT 0,
    average_score DECIMAL(5,2) DEFAULT 0.00,
    time_spent INT DEFAULT 0, -- total time in seconds
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    mastery_level ENUM('Beginner', 'Intermediate', 'Advanced', 'Expert') DEFAULT 'Beginner',
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id),
    UNIQUE KEY unique_student_dept_level (student_id, department_id, academic_level_id)
);

-- System settings
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Session management
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    user_type ENUM('student', 'admin') NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    type ENUM('achievement', 'quiz', 'reminder', 'system') NOT NULL DEFAULT 'system',
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
);
