/**
 * Password Reset JavaScript for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePasswordReset();
});

function initializePasswordReset() {
    const currentStep = getCurrentStep();
    
    // Initialize based on current step
    switch(currentStep) {
        case 'email':
            initializeEmailStep();
            break;
        case 'security':
            initializeSecurityStep();
            break;
        case 'reset':
            initializeResetStep();
            break;
        case 'complete':
            initializeCompleteStep();
            break;
    }
    
    // Add form validation
    addFormValidation();
    
    // Add progress animations
    animateProgress();
}

function getCurrentStep() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('step') || 'email';
}

function initializeEmailStep() {
    const identifierInput = document.getElementById('identifier');
    
    if (identifierInput) {
        // Add input formatting for Student ID
        identifierInput.addEventListener('input', function() {
            // Convert to uppercase for consistency
            this.value = this.value.toUpperCase();
        });
        
        identifierInput.addEventListener('blur', function() {
            validateIdentifier(this);
        });
        
        // Focus on load
        identifierInput.focus();
    }
}

function initializeSecurityStep() {
    const answerInputs = document.querySelectorAll('input[name^="answer_"]');
    
    answerInputs.forEach((input, index) => {
        input.addEventListener('input', function() {
            validateSecurityAnswer(this, index + 1);
        });
        
        // Focus first input on load
        if (index === 0) {
            input.focus();
        }
    });
    
    // Add real-time validation feedback
    addSecurityValidation();
}

function initializeResetStep() {
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            validatePasswordMatch();
        });
        
        passwordInput.focus();
    }
    
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validatePasswordMatch);
    }
}

function initializeCompleteStep() {
    // Add celebration animation
    const successIcon = document.querySelector('.success-icon');
    if (successIcon) {
        successIcon.style.animation = 'bounce 1s ease-in-out';
    }
    
    // Auto-focus login button
    const loginButton = document.querySelector('.success-actions .btn');
    if (loginButton) {
        setTimeout(() => {
            loginButton.focus();
        }, 500);
    }
}

function validateIdentifier(input) {
    const value = input.value.trim();
    const isEmail = value.includes('@');
    const isStudentId = /^[A-Z]{2,5}\/\d{4}\/\d{4}$/.test(value);
    
    clearFieldValidation(input);
    
    if (value === '') {
        return;
    } else if (!isEmail && !isStudentId) {
        setFieldInvalid(input, 'Please enter a valid Student ID (e.g., CS/2024/1234) or email address');
    } else {
        setFieldValid(input);
    }
}

function validateSecurityAnswer(input, questionNumber) {
    const answer = input.value.trim();
    
    clearFieldValidation(input);
    
    if (answer.length === 0) {
        return;
    } else if (answer.length < 2) {
        setFieldInvalid(input, 'Answer too short');
    } else {
        setFieldValid(input);
    }
}

function addSecurityValidation() {
    const form = document.querySelector('.auth-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        const answerInputs = document.querySelectorAll('input[name^="answer_"]');
        let filledAnswers = 0;
        
        answerInputs.forEach(input => {
            if (input.value.trim().length > 0) {
                filledAnswers++;
            }
        });
        
        if (filledAnswers < 2) {
            e.preventDefault();
            showError('Please answer at least 2 security questions');
            return false;
        }
    });
}

function checkPasswordStrength(password) {
    const strengthIndicator = document.getElementById('password-strength');
    if (!strengthIndicator) return;
    
    let strength = 0;
    let feedback = '';
    
    // Length checks
    if (password.length >= 8) strength += 1;
    if (password.length >= 12) strength += 1;
    
    // Character variety checks
    if (/[a-z]/.test(password)) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    
    // Remove existing classes
    strengthIndicator.classList.remove('weak', 'medium', 'strong');
    
    if (password.length === 0) {
        strengthIndicator.style.width = '0%';
        strengthIndicator.style.background = '#e5e7eb';
        return;
    }
    
    if (strength <= 2) {
        strengthIndicator.classList.add('weak');
        feedback = 'Weak password - add more characters and variety';
    } else if (strength <= 4) {
        strengthIndicator.classList.add('medium');
        feedback = 'Medium strength - consider adding special characters';
    } else {
        strengthIndicator.classList.add('strong');
        feedback = 'Strong password!';
    }
    
    strengthIndicator.setAttribute('title', feedback);
}

function validatePasswordMatch() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    if (!password || !confirmPassword) return;
    
    const isMatch = password.value === confirmPassword.value;
    const isEmpty = confirmPassword.value === '';
    
    clearFieldValidation(confirmPassword);
    
    if (!isEmpty) {
        if (isMatch) {
            setFieldValid(confirmPassword, 'Passwords match');
        } else {
            setFieldInvalid(confirmPassword, 'Passwords do not match');
        }
    }
}

function addFormValidation() {
    const forms = document.querySelectorAll('.auth-form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('.btn-primary');
            if (submitBtn) {
                setButtonLoading(submitBtn, true);
            }
        });
    });
}

function validateForm(form) {
    let isValid = true;
    let firstErrorField = null;
    
    // Clear previous errors
    const errorAlerts = document.querySelectorAll('.alert-error');
    errorAlerts.forEach(alert => alert.remove());
    
    // Validate required fields
    const requiredFields = form.querySelectorAll('input[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            setFieldInvalid(field, 'This field is required');
            isValid = false;
            if (!firstErrorField) firstErrorField = field;
        }
    });
    
    // Step-specific validation
    const currentStep = getCurrentStep();
    
    if (currentStep === 'reset') {
        const password = form.querySelector('#password');
        const confirmPassword = form.querySelector('#confirm_password');
        
        if (password && password.value.length < 8) {
            setFieldInvalid(password, 'Password must be at least 8 characters');
            isValid = false;
            if (!firstErrorField) firstErrorField = password;
        }
        
        if (password && confirmPassword && password.value !== confirmPassword.value) {
            setFieldInvalid(confirmPassword, 'Passwords do not match');
            isValid = false;
            if (!firstErrorField) firstErrorField = confirmPassword;
        }
    }
    
    if (!isValid && firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstErrorField.focus();
    }
    
    return isValid;
}

function setFieldValid(field, message = '') {
    field.classList.remove('invalid');
    field.classList.add('valid');
    field.style.borderColor = '#10b981';
    field.style.backgroundColor = '#f0fdf4';
    
    let validationMsg = field.parentNode.querySelector('.validation-message');
    if (validationMsg) {
        validationMsg.remove();
    }
    
    if (message) {
        validationMsg = document.createElement('div');
        validationMsg.className = 'validation-message success';
        validationMsg.style.cssText = 'font-size: 0.8rem; color: #16a34a; margin-top: 4px; padding: 4px 8px; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 6px;';
        validationMsg.textContent = message;
        field.parentNode.appendChild(validationMsg);
    }
}

function setFieldInvalid(field, message) {
    field.classList.remove('valid');
    field.classList.add('invalid');
    field.style.borderColor = '#ef4444';
    field.style.backgroundColor = '#fef2f2';
    
    let validationMsg = field.parentNode.querySelector('.validation-message');
    if (validationMsg) {
        validationMsg.remove();
    }
    
    validationMsg = document.createElement('div');
    validationMsg.className = 'validation-message error';
    validationMsg.style.cssText = 'font-size: 0.8rem; color: #dc2626; margin-top: 4px; padding: 4px 8px; background: #fef2f2; border: 1px solid #fecaca; border-radius: 6px;';
    validationMsg.textContent = message;
    field.parentNode.appendChild(validationMsg);
}

function clearFieldValidation(field) {
    field.classList.remove('valid', 'invalid');
    field.style.borderColor = '';
    field.style.backgroundColor = '';
    
    const validationMsg = field.parentNode.querySelector('.validation-message');
    if (validationMsg) {
        validationMsg.remove();
    }
}

function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-error';
    alertDiv.style.cssText = 'background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 16px; border-radius: 10px; margin-bottom: 20px; display: flex; align-items: center; gap: 12px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        ${message}
    `;
    
    const authBody = document.querySelector('.auth-body');
    const firstChild = authBody.querySelector('.reset-progress').nextElementSibling;
    authBody.insertBefore(alertDiv, firstChild);
    
    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.style.opacity = '0';
            setTimeout(() => alertDiv.remove(), 300);
        }
    }, 5000);
}

function setButtonLoading(button, isLoading) {
    if (isLoading) {
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        button.disabled = true;
        button.style.opacity = '0.8';
    } else {
        button.innerHTML = button.dataset.originalText || button.innerHTML;
        button.disabled = false;
        button.style.opacity = '1';
    }
}

function animateProgress() {
    const progressSteps = document.querySelectorAll('.progress-step');
    const progressLines = document.querySelectorAll('.progress-line');
    
    // Animate steps with delay
    progressSteps.forEach((step, index) => {
        setTimeout(() => {
            step.style.opacity = '1';
            step.style.transform = 'scale(1)';
        }, index * 200);
    });
    
    // Animate lines
    progressLines.forEach((line, index) => {
        setTimeout(() => {
            if (line.classList.contains('completed')) {
                line.style.width = '60px';
            }
        }, (index + 1) * 300);
    });
}

// Add bounce animation keyframes
const style = document.createElement('style');
style.textContent = `
    @keyframes bounce {
        0%, 20%, 53%, 80%, 100% {
            transform: translate3d(0,0,0);
        }
        40%, 43% {
            transform: translate3d(0,-15px,0);
        }
        70% {
            transform: translate3d(0,-7px,0);
        }
        90% {
            transform: translate3d(0,-2px,0);
        }
    }
`;
document.head.appendChild(style);
