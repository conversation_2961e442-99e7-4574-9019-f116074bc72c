<?php
/**
 * Student Profile Management for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        
        // Validation
        if (empty($firstName) || empty($lastName)) {
            $error = 'First name and last name are required.';
        } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } else {
            try {
                // Check if email is already used by another student
                if (!empty($email)) {
                    $existingEmail = fetchOne("
                        SELECT id FROM students 
                        WHERE email = :email AND id != :id
                    ", ['email' => $email, 'id' => $_SESSION['user_id']]);
                    
                    if ($existingEmail) {
                        $error = 'This email address is already in use.';
                    }
                }
                
                if (empty($error)) {
                    // Update profile
                    executeQuery("
                        UPDATE students
                        SET first_name = :first_name, last_name = :last_name,
                            email = :email, phone = :phone, updated_at = NOW()
                        WHERE id = :id
                    ", [
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'email' => $email,
                        'phone' => $phone,
                        'id' => $_SESSION['user_id']
                    ]);
                    
                    $message = 'Profile updated successfully!';
                    
                    // Log the update
                    insertRecord('activity_logs', [
                        'user_id' => $_SESSION['user_id'],
                        'user_type' => 'student',
                        'action' => 'profile_updated',
                        'details' => 'Student updated their profile information',
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ]);
                }
            } catch (Exception $e) {
                $error = 'Failed to update profile. Please try again.';
            }
        }
    } elseif ($action === 'change_password') {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validation
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $error = 'All password fields are required.';
        } elseif ($newPassword !== $confirmPassword) {
            $error = 'New passwords do not match.';
        } elseif (strlen($newPassword) < 6) {
            $error = 'New password must be at least 6 characters long.';
        } else {
            try {
                // Verify current password
                $student = fetchOne("SELECT password_hash FROM students WHERE id = :id", ['id' => $_SESSION['user_id']]);

                if (!$student || !password_verify($currentPassword, $student['password_hash'])) {
                    $error = 'Current password is incorrect.';
                } else {
                    // Update password
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    updateRecord('students',
                        ['password_hash' => $hashedPassword],
                        'id = ?',
                        [$_SESSION['user_id']]
                    );
                    
                    $message = 'Password changed successfully!';
                    
                    // Log the password change
                    insertRecord('activity_logs', [
                        'user_id' => $_SESSION['user_id'],
                        'user_type' => 'student',
                        'action' => 'password_changed',
                        'details' => 'Student changed their password',
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ]);
                }
            } catch (Exception $e) {
                $error = 'Failed to change password. Please try again.';
            }
        }
    }
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = ?
", [$_SESSION['user_id']]);

if (!$student) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Get student game statistics
$quickGameStats = fetchOne("
    SELECT
        COUNT(*) as games_played,
        AVG(points_earned) as average_score,
        MAX(points_earned) as best_score
    FROM game_sessions
    WHERE student_id = :student_id AND game_mode = 'quick' AND status = 'completed'
", ['student_id' => $student['id']]);

$endlessStats = fetchOne("
    SELECT
        COUNT(*) as games_played,
        AVG(points_earned) as average_score,
        MAX(points_earned) as best_score
    FROM game_sessions
    WHERE student_id = :student_id AND game_mode = 'endless' AND status = 'completed'
", ['student_id' => $student['id']]);

$missionStats = fetchOne("
    SELECT
        COUNT(*) as games_played,
        AVG(points_earned) as average_score,
        MAX(points_earned) as best_score
    FROM game_sessions
    WHERE student_id = :student_id AND game_mode = 'mission' AND status = 'completed'
", ['student_id' => $student['id']]);

$pageTitle = 'My Profile';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .sidebar {
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            box-shadow: 4px 0 20px rgba(102, 126, 234, 0.3);
        }

        .main-content {
            background: transparent;
        }

        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .profile-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 10px;
        }

        .profile-header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(255, 255, 255, 0.1);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin: 0 auto 15px;
        }

        .stat-card.quick-game .stat-icon {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }

        .stat-card.endless-mode .stat-icon {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .stat-card.mission-mode .stat-icon {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 5px;
        }

        .stat-card p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 500;
        }

        .profile-forms {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .form-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-card h3 i {
            color: #3498db;
        }

        .form-floating {
            margin-bottom: 15px;
        }

        .form-floating > .form-control {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
        }

        .form-floating > .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .form-floating > .form-control:focus ~ label {
            color: #667eea;
        }

        .btn-gradient {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
            color: white;
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 12px 18px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        @media (max-width: 768px) {
            .forms-container {
                grid-template-columns: 1fr;
            }

            .profile-header {
                flex-direction: column;
                text-align: center;
            }

            .stats-row {
                grid-template-columns: 1fr;
            }
        }

        /* Enhanced Professional Loading Screen Styles */
        .enhanced-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #6c5ce7dd, #6c5ce7aa);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            transform: scale(1.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .enhanced-loading-overlay.show {
            opacity: 1;
            transform: scale(1);
        }

        .enhanced-loading-overlay.complete {
            opacity: 0;
            transform: scale(0.9);
        }

        .loading-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            font-size: 20px;
            animation: particleFloat 6s ease-in-out infinite;
            opacity: 0.8;
            pointer-events: none;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .particle-lightning {
            animation: lightningFloat 4s ease-in-out infinite;
        }

        .particle-stars {
            animation: starFloat 5s ease-in-out infinite;
        }

        .particle-compass {
            animation: compassFloat 6s ease-in-out infinite;
        }

        .enhanced-loading-content {
            text-align: center;
            color: white;
            max-width: 400px;
            position: relative;
            z-index: 2;
        }

        .loading-logo {
            position: relative;
            margin-bottom: 30px;
            display: inline-block;
        }

        .logo-circle {
            width: 80px;
            height: 80px;
            border: 3px solid #6c5ce7;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
            animation: logoSpin 3s linear infinite;
        }

        .loading-main-icon {
            font-size: 32px;
            color: white;
            animation: iconPulse 2s ease-in-out infinite;
        }

        .logo-pulse {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px solid #6c5ce7;
            border-radius: 50%;
            animation: logoPulse 2s ease-in-out infinite;
            opacity: 0.6;
        }

        .loading-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            animation: titleGlow 2s ease-in-out infinite alternate;
        }

        .loading-message {
            font-size: 16px;
            margin-bottom: 30px;
            opacity: 0.9;
            font-weight: 400;
            animation: messageFloat 3s ease-in-out infinite;
        }

        .loading-progress-container {
            margin-bottom: 20px;
        }

        .loading-progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .loading-progress-fill {
            height: 100%;
            background: #6c5ce7;
            border-radius: 10px;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: 0%;
            box-shadow: 0 0 10px rgba(108, 92, 231, 0.5);
            position: relative;
        }

        .loading-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: progressShine 1.5s ease-in-out infinite;
        }

        .loading-percentage {
            font-size: 14px;
            font-weight: 600;
            opacity: 0.8;
        }

        .loading-dots {
            display: flex;
            justify-content: center;
            gap: 8px;
        }

        .loading-dots span {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: dotBounce 1.4s ease-in-out infinite both;
        }

        .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
        .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
        .loading-dots span:nth-child(3) { animation-delay: 0s; }

        /* Sound Wave Effect */
        .sound-waves {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }

        .sound-wave {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100px;
            height: 100px;
            border: 2px solid #6c5ce7;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: soundWaveExpand 2s ease-out;
            opacity: 0;
        }

        /* Enhanced Loading Animations */
        @keyframes particleFloat {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(0.8);
                opacity: 0;
            }
            10% {
                opacity: 0.8;
            }
            50% {
                transform: translate(var(--end-x, 50%), var(--end-y, -50%)) rotate(180deg) scale(1.2);
                opacity: 1;
            }
            90% {
                opacity: 0.6;
            }
            100% {
                transform: translate(var(--end-x, 100%), var(--end-y, -100%)) rotate(360deg) scale(0.5);
                opacity: 0;
            }
        }

        @keyframes lightningFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg) scale(1);
                opacity: 0.6;
                filter: brightness(1);
            }
            25% {
                transform: translateY(-30px) rotate(90deg) scale(1.3);
                opacity: 1;
                filter: brightness(1.5);
            }
            50% {
                transform: translateY(-60px) rotate(180deg) scale(0.8);
                opacity: 0.8;
                filter: brightness(1.2);
            }
            75% {
                transform: translateY(-30px) rotate(270deg) scale(1.1);
                opacity: 0.9;
                filter: brightness(1.3);
            }
        }

        @keyframes starFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg) scale(0.8);
                opacity: 0.5;
            }
            33% {
                transform: translateY(-40px) rotate(120deg) scale(1.2);
                opacity: 1;
            }
            66% {
                transform: translateY(-20px) rotate(240deg) scale(1);
                opacity: 0.8;
            }
        }

        @keyframes compassFloat {
            0%, 100% {
                transform: translateX(0px) rotate(0deg) scale(1);
                opacity: 0.7;
            }
            25% {
                transform: translateX(30px) rotate(90deg) scale(1.1);
                opacity: 0.9;
            }
            50% {
                transform: translateX(0px) rotate(180deg) scale(0.9);
                opacity: 1;
            }
            75% {
                transform: translateX(-30px) rotate(270deg) scale(1.1);
                opacity: 0.8;
            }
        }

        @keyframes logoSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes logoPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.6;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.3;
            }
        }

        @keyframes iconPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }

        @keyframes titleGlow {
            0% { text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); }
            100% { text-shadow: 0 2px 20px rgba(255, 255, 255, 0.3); }
        }

        @keyframes messageFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes dotBounce {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes screenShake {
            0%, 100% { transform: translateX(0); }
            10% { transform: translateX(-2px); }
            20% { transform: translateX(2px); }
            30% { transform: translateX(-2px); }
            40% { transform: translateX(2px); }
            50% { transform: translateX(-1px); }
            60% { transform: translateX(1px); }
            70% { transform: translateX(-1px); }
            80% { transform: translateX(1px); }
            90% { transform: translateX(0); }
        }

        @keyframes soundWaveExpand {
            0% {
                transform: translate(-50%, -50%) scale(0.5);
                opacity: 0.8;
            }
            50% {
                opacity: 0.4;
            }
            100% {
                transform: translate(-50%, -50%) scale(3);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Knowledge Arena</h3>
                    <p>Gaming LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="javascript:void(0)" onclick="launchQuickGame()" class="nav-item">
                    <i class="fas fa-bolt"></i>
                    <span>Quick Game</span>
                </a>
                <a href="javascript:void(0)" onclick="launchEndlessMode()" class="nav-item">
                    <i class="fas fa-infinity"></i>
                    <span>Endless Mode</span>
                </a>
                <a href="javascript:void(0)" onclick="launchMissionMode()" class="nav-item">
                    <i class="fas fa-map"></i>
                    <span>Mission Mode</span>
                </a>

                <a href="profile.php" class="nav-item active">
                    <i class="fas fa-user-circle"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
                <a href="../index.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Go Back to Site</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-ninja"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                        <p><?php echo htmlspecialchars($student['department_name'] . ' - ' . $student['level_name']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container-fluid">
                <!-- Profile Header -->
                <div class="profile-header text-center">
                    <div class="profile-avatar">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <h1 class="mb-2"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h1>
                    <p class="mb-1 fs-5"><strong><?php echo htmlspecialchars($student['student_id']); ?></strong></p>
                    <p class="mb-1"><?php echo htmlspecialchars($student['department_name']); ?> • <?php echo htmlspecialchars($student['level_name']); ?></p>
                    <p class="mb-0 opacity-75">Member since <?php echo isset($student['created_at']) ? date('F Y', strtotime($student['created_at'])) : 'N/A'; ?></p>
                </div>

                <!-- Alerts -->
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Game Mode Statistics -->
                <div class="stats-cards">
                    <div class="stat-card quick-game">
                        <div class="stat-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3 class="mb-1"><?php echo number_format($quickGameStats['games_played'] ?? 0); ?></h3>
                        <p class="text-muted mb-0">Quick Games</p>
                        <small class="text-muted">
                            Avg: <?php echo number_format($quickGameStats['average_score'] ?? 0, 1); ?>% |
                            Best: <?php echo number_format($quickGameStats['best_score'] ?? 0, 1); ?>%
                        </small>
                    </div>
                    <div class="stat-card endless-mode">
                        <div class="stat-icon">
                            <i class="fas fa-infinity"></i>
                        </div>
                        <h3 class="mb-1"><?php echo number_format($endlessStats['games_played'] ?? 0); ?></h3>
                        <p class="text-muted mb-0">Endless Games</p>
                        <small class="text-muted">
                            Avg: <?php echo number_format($endlessStats['average_score'] ?? 0, 1); ?>% |
                            Best: <?php echo number_format($endlessStats['best_score'] ?? 0, 1); ?>%
                        </small>
                    </div>
                    <div class="stat-card mission-mode">
                        <div class="stat-icon">
                            <i class="fas fa-target"></i>
                        </div>
                        <h3 class="mb-1"><?php echo number_format($missionStats['games_played'] ?? 0); ?></h3>
                        <p class="text-muted mb-0">Mission Games</p>
                        <small class="text-muted">
                            Avg: <?php echo number_format($missionStats['average_score'] ?? 0, 1); ?>% |
                            Best: <?php echo number_format($missionStats['best_score'] ?? 0, 1); %>%
                        </small>
                    </div>
                </div>

                <!-- Profile Forms -->
                <div class="profile-forms">
                    <!-- Personal Information Form -->
                    <div class="form-card">
                        <h3><i class="fas fa-user-edit"></i> Personal Information</h3>
                        <form method="POST" action="" id="profile-form">
                            <input type="hidden" name="action" value="update_profile">

                            <div class="form-floating">
                                <input type="text" class="form-control" id="student_id" value="<?php echo htmlspecialchars($student['student_id']); ?>" disabled>
                                <label for="student_id">Student ID</label>
                            </div>

                            <div class="form-floating">
                                <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo htmlspecialchars($student['first_name']); ?>" required>
                                <label for="first_name">First Name *</label>
                            </div>

                            <div class="form-floating">
                                <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo htmlspecialchars($student['last_name']); ?>" required>
                                <label for="last_name">Last Name *</label>
                            </div>

                            <div class="form-floating">
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($student['email'] ?? ''); ?>">
                                <label for="email">Email Address</label>
                            </div>

                            <div class="form-floating">
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($student['phone'] ?? ''); ?>">
                                <label for="phone">Phone Number</label>
                            </div>

                            <button type="submit" class="btn btn-gradient">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                        </form>
                    </div>

                    <!-- Password Change Form -->
                    <div class="form-card">
                        <h3><i class="fas fa-lock"></i> Change Password</h3>
                        <form method="POST" action="" id="password-form">
                            <input type="hidden" name="action" value="change_password">

                            <div class="form-floating">
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                                <label for="current_password">Current Password *</label>
                            </div>

                            <div class="form-floating">
                                <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                                <label for="new_password">New Password *</label>
                            </div>

                            <div class="form-floating">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                                <label for="confirm_password">Confirm New Password *</label>
                            </div>

                            <div id="password-strength" class="mt-2 mb-3"></div>

                            <button type="submit" class="btn btn-gradient">
                                <i class="fas fa-key"></i> Change Password
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Real-time form validation and feedback
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    if (alert.classList.contains('show')) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                });
            }, 5000);

            // Real-time email validation
            const emailInput = document.getElementById('email');
            emailInput.addEventListener('input', function() {
                const email = this.value;
                if (email && !isValidEmail(email)) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                } else if (email) {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                }
            });

            // Real-time phone validation
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function() {
                const phone = this.value;
                if (phone && !isValidPhone(phone)) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                } else if (phone) {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                }
            });

            // Password strength indicator
            const newPasswordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const strengthIndicator = document.getElementById('password-strength');

            newPasswordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = getPasswordStrength(password);
                updatePasswordStrength(strength);
                validatePasswordMatch();
            });

            confirmPasswordInput.addEventListener('input', validatePasswordMatch);

            function validatePasswordMatch() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (confirmPassword && newPassword !== confirmPassword) {
                    confirmPasswordInput.classList.add('is-invalid');
                    confirmPasswordInput.classList.remove('is-valid');
                } else if (confirmPassword && newPassword === confirmPassword) {
                    confirmPasswordInput.classList.add('is-valid');
                    confirmPasswordInput.classList.remove('is-invalid');
                } else {
                    confirmPasswordInput.classList.remove('is-valid', 'is-invalid');
                }
            }

            function getPasswordStrength(password) {
                let score = 0;
                if (password.length >= 8) score++;
                if (/[a-z]/.test(password)) score++;
                if (/[A-Z]/.test(password)) score++;
                if (/[0-9]/.test(password)) score++;
                if (/[^A-Za-z0-9]/.test(password)) score++;
                return score;
            }

            function updatePasswordStrength(strength) {
                const colors = ['#dc3545', '#fd7e14', '#ffc107', '#20c997', '#28a745'];
                const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];

                if (newPasswordInput.value.length === 0) {
                    strengthIndicator.innerHTML = '';
                    return;
                }

                const color = colors[strength] || colors[0];
                const label = labels[strength] || labels[0];
                const width = ((strength + 1) / 5) * 100;

                strengthIndicator.innerHTML = `
                    <div class="password-strength-bar" style="background: #e9ecef; height: 4px; border-radius: 2px; overflow: hidden;">
                        <div style="width: ${width}%; height: 100%; background: ${color}; transition: all 0.3s ease;"></div>
                    </div>
                    <small class="text-muted mt-1 d-block">Password strength: <span style="color: ${color}; font-weight: 600;">${label}</span></small>
                `;
            }

            function isValidEmail(email) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            }

            function isValidPhone(phone) {
                return /^[\+]?[1-9][\d]{0,15}$/.test(phone.replace(/[\s\-\(\)]/g, ''));
            }
        });

        // Enhanced Game Launch Functions with Professional Animations
        function launchQuickGame() {
            showEnhancedLoadingScreen({
                title: 'Quick Game',
                message: 'Preparing your lightning-fast challenge...',
                icon: 'fas fa-bolt',
                color: '#6c5ce7',
                particles: 'lightning',
                duration: 2500
            }, () => {
                window.location.href = 'quick-game-setup.php';
            });
        }

        function launchEndlessMode() {
            showEnhancedLoadingScreen({
                title: 'Endless Mode',
                message: 'Entering the infinite knowledge realm...',
                icon: 'fas fa-infinity',
                color: '#0984e3',
                particles: 'stars',
                duration: 2800
            }, () => {
                window.location.href = 'endless-game.php';
            });
        }

        function launchMissionMode() {
            showEnhancedLoadingScreen({
                title: 'Mission Mode',
                message: 'Loading your adventure map...',
                icon: 'fas fa-map',
                color: '#e17055',
                particles: 'compass',
                duration: 2600
            }, () => {
                window.location.href = 'mission-mode.php';
            });
        }

        // Enhanced Professional Loading Screen Function
        function showEnhancedLoadingScreen(config, callback) {
            // Create enhanced loading overlay
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'enhanced-loading-overlay';
            loadingOverlay.style.background = `linear-gradient(135deg, ${config.color}dd, ${config.color}aa)`;

            loadingOverlay.innerHTML = `
                <div class="loading-particles" id="loadingParticles"></div>
                <div class="enhanced-loading-content">
                    <div class="loading-logo">
                        <div class="logo-circle" style="border-color: ${config.color};">
                            <i class="${config.icon} loading-main-icon"></i>
                        </div>
                        <div class="logo-pulse" style="border-color: ${config.color};"></div>
                    </div>
                    <h2 class="loading-title">${config.title}</h2>
                    <p class="loading-message">${config.message}</p>
                    <div class="loading-progress-container">
                        <div class="loading-progress-bar">
                            <div class="loading-progress-fill" style="background: ${config.color};"></div>
                        </div>
                        <div class="loading-percentage">0%</div>
                    </div>
                    <div class="loading-dots">
                        <span></span><span></span><span></span>
                    </div>
                </div>
            `;

            document.body.appendChild(loadingOverlay);

            // Create particles based on type
            createLoadingParticles(config.particles, config.color);

            // Animate entrance with sound effect simulation
            setTimeout(() => {
                loadingOverlay.classList.add('show');

                // Create visual "sound wave" effect
                createSoundWaveEffect(loadingOverlay, config.color);
            }, 50);

            // Animate progress bar
            const progressBar = loadingOverlay.querySelector('.loading-progress-fill');
            const percentage = loadingOverlay.querySelector('.loading-percentage');
            let progress = 0;

            const progressInterval = setInterval(() => {
                progress += Math.random() * 8 + 2;
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';
                percentage.textContent = Math.floor(progress) + '%';

                if (progress >= 100) {
                    clearInterval(progressInterval);

                    // Complete animation with screen effect
                    setTimeout(() => {
                        loadingOverlay.classList.add('complete');

                        // Add subtle screen shake effect
                        document.body.style.animation = 'screenShake 0.3s ease-in-out';

                        setTimeout(() => {
                            document.body.style.animation = '';
                            document.body.removeChild(loadingOverlay);
                            if (callback) callback();
                        }, 500);
                    }, 300);
                }
            }, 80);
        }

        // Create animated particles for loading screen
        function createLoadingParticles(type, color) {
            const container = document.getElementById('loadingParticles');
            const particleCount = 25;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = `particle particle-${type}`;

                // Random positioning and movement
                const startX = Math.random() * 100;
                const startY = Math.random() * 100;
                const endX = Math.random() * 100;
                const endY = Math.random() * 100;

                particle.style.left = startX + '%';
                particle.style.top = startY + '%';
                particle.style.animationDelay = Math.random() * 2 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 3) + 's';

                // Create particle content based on type
                if (type === 'lightning') {
                    const icons = ['⚡', '🔥', '💫', '⭐'];
                    particle.innerHTML = icons[Math.floor(Math.random() * icons.length)];
                    particle.style.color = color;
                    particle.style.fontSize = (Math.random() * 10 + 15) + 'px';
                } else if (type === 'stars') {
                    const icons = ['✨', '⭐', '🌟', '💫', '🔮'];
                    particle.innerHTML = icons[Math.floor(Math.random() * icons.length)];
                    particle.style.color = '#fff';
                    particle.style.fontSize = (Math.random() * 8 + 12) + 'px';
                } else if (type === 'compass') {
                    const icons = ['🧭', '🗺️', '🏔️', '🎯', '🚀'];
                    particle.innerHTML = icons[Math.floor(Math.random() * icons.length)];
                    particle.style.fontSize = (Math.random() * 8 + 14) + 'px';
                }

                // Add custom CSS variables for animation
                particle.style.setProperty('--end-x', endX + '%');
                particle.style.setProperty('--end-y', endY + '%');

                container.appendChild(particle);

                // Remove particle after animation
                setTimeout(() => {
                    if (container.contains(particle)) {
                        container.removeChild(particle);
                    }
                }, 8000);
            }
        }

        // Create visual sound wave effect
        function createSoundWaveEffect(container, color) {
            const waveContainer = document.createElement('div');
            waveContainer.className = 'sound-waves';
            waveContainer.innerHTML = `
                <div class="sound-wave" style="border-color: ${color};"></div>
                <div class="sound-wave" style="border-color: ${color}; animation-delay: 0.2s;"></div>
                <div class="sound-wave" style="border-color: ${color}; animation-delay: 0.4s;"></div>
            `;
            container.appendChild(waveContainer);

            // Remove after animation
            setTimeout(() => {
                if (container.contains(waveContainer)) {
                    container.removeChild(waveContainer);
                }
            }, 2000);
        }
    </script>
</body>
</html>
