/**
 * Mission Mode Styles - Enhanced <PERSON> Design
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

body.mission-page {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif !important;
    background: #663399 !important;
    min-height: 100vh !important;
    display: block !important;
    align-items: unset !important;
    justify-content: unset !important;
    position: relative;
    overflow-x: hidden;
}

/* FontAwesome Icon Fix - Comprehensive */
.fas, .far, .fab,
i.fas, i.far, i.fab,
[class^="fa-"], [class*=" fa-"] {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

.far, i.far {
    font-weight: 400 !important;
}

.fab, i.fab {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
}

/* Ensure icons don't inherit body font */
* [class^="fa-"]::before,
* [class*=" fa-"]::before {
    font-family: "Font Awesome 6 Free" !important;
}

.mission-container {
    min-height: 100vh;
    background: #663399;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow-x: hidden;
}

/* Subtle animated background elements */
.mission-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 60% 60%, rgba(255, 255, 255, 0.02) 0%, transparent 45%);
    z-index: -1;
    animation: subtleFloat 20s ease-in-out infinite;
}

/* Additional subtle floating elements */
.mission-container::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.04) 0%, transparent 35%),
        radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.03) 0%, transparent 40%),
        radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.02) 0%, transparent 30%);
    z-index: -1;
    animation: subtleFloatReverse 25s ease-in-out infinite;
}

@keyframes subtleFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-10px) scale(1.02); }
}

@keyframes subtleFloatReverse {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(8px) scale(0.98); }
}

/* Header Styles */
.mission-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
    backdrop-filter: blur(20px);
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.back-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: 1rem;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.back-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 35px rgba(255, 107, 107, 0.5);
    border-color: rgba(255, 255, 255, 0.4);
}

.mission-title {
    text-align: center;
    color: white;
}

.mission-title h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 800;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    background: linear-gradient(45deg, #fff, #f1c40f, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.mission-title p {
    margin: 0.25rem 0 0 0;
    font-size: 0.95rem;
    font-weight: 500;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.mission-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.stat-item {
    text-align: center;
    color: white;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
    padding: 1.5rem 1.25rem;
    border-radius: 20px;
    min-width: 100px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.35), rgba(255, 255, 255, 0.2));
}

.stat-item i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    display: block;
    color: #f1c40f;
    text-shadow: 0 2px 10px rgba(241, 196, 15, 0.4);
}

.stat-item span {
    display: block;
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.stat-item small {
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.9;
}

/* Mission Map Styles */
.mission-main {
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
}

.mission-map {
    position: relative;
    padding: 0.5rem 0;
}

.map-path {
    position: relative;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
}

/* Mission Level Container - Zigzag Layout */
.mission-level-container {
    position: relative;
    margin: 1rem 0;
    width: 100%;
}

/* First card spacing adjustment */
.mission-level-container:first-child {
    margin-top: 0.25rem;
}

.mission-level-container:nth-child(odd) {
    display: flex;
    justify-content: flex-start;
    padding-left: 5%;
}

.mission-level-container:nth-child(even) {
    display: flex;
    justify-content: flex-end;
    padding-right: 5%;
}

.mission-level-card {
    display: flex;
    align-items: center;
    gap: 2.5rem;
    max-width: 700px;
    width: 95%;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
    padding: 0.5rem;
    border-radius: 25px;
}

.mission-level-card.clickable:hover {
    transform: translateY(-12px) scale(1.05);
}

.mission-level-card.locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.mission-level-card.locked:hover {
    transform: none;
}



/* Play Button Styles */
.play-button {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.8rem;
    flex-shrink: 0;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.play-button i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
}

.play-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.2) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
    border-radius: 50%;
}

.play-button.locked {
    background: linear-gradient(145deg,
        rgba(156, 163, 175, 0.5),
        rgba(203, 213, 225, 0.4));
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(156, 163, 175, 0.6);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(156, 163, 175, 0.2);
}

.play-button.available {
    background: linear-gradient(145deg,
        #ffd700,
        #ffb300,
        #ff8f00);
    color: #1a1a1a;
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow:
        0 0 40px rgba(255, 215, 0, 0.7),
        0 15px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    animation: pulseGlow 2s ease-in-out infinite;
}

.play-button.completed {
    background: linear-gradient(145deg,
        #00c851,
        #007e33,
        #004d20);
    color: white;
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow:
        0 0 40px rgba(0, 200, 81, 0.6),
        0 15px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

/* Level Card Styles */
.level-card {
    flex: 1;
    background: linear-gradient(145deg,
        rgba(30, 144, 255, 0.25),
        rgba(138, 43, 226, 0.22),
        rgba(100, 149, 237, 0.3),
        rgba(147, 112, 219, 0.25));
    backdrop-filter: blur(25px);
    border: 3px solid rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 1.5rem;
    color: white;
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset,
        0 0 40px rgba(255, 255, 255, 0.15);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}



.mission-level-card.locked .level-card {
    background: linear-gradient(145deg,
        rgba(156, 163, 175, 0.4),
        rgba(203, 213, 225, 0.35),
        rgba(226, 232, 240, 0.3));
    border-color: rgba(156, 163, 175, 0.6);
    color: rgba(255, 255, 255, 0.9);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.15),
        0 0 25px rgba(156, 163, 175, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset;
}

.mission-level-card.available .level-card {
    background: linear-gradient(145deg,
        rgba(30, 144, 255, 0.25),
        rgba(138, 43, 226, 0.22),
        rgba(100, 149, 237, 0.3),
        rgba(147, 112, 219, 0.25));
    border-color: rgba(255, 255, 255, 1);
    box-shadow:
        0 0 35px rgba(255, 255, 255, 0.5),
        0 20px 50px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.3) inset,
        0 0 100px rgba(30, 144, 255, 0.3);
    animation: availableGlow 3s ease-in-out infinite;
}

.mission-level-card.completed .level-card {
    background: linear-gradient(145deg,
        rgba(16, 185, 129, 0.25),
        rgba(34, 197, 94, 0.22),
        rgba(74, 222, 128, 0.3),
        rgba(134, 239, 172, 0.25));
    border-color: rgba(255, 255, 255, 0.9);
    box-shadow:
        0 0 25px rgba(255, 255, 255, 0.3),
        0 15px 40px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(16, 185, 129, 0.3) inset;
}

.mission-level-card.clickable:hover .level-card {
    transform: translateY(-5px);
    box-shadow:
        0 20px 50px rgba(30, 144, 255, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.4) inset,
        0 0 60px rgba(255, 255, 255, 0.25);
}

/* Connecting Tails Between Cards */
.mission-level-container:not(:last-child) .mission-level-card::after {
    content: '';
    position: absolute;
    bottom: -3rem;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 3rem;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 3px;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
    z-index: 1;
    overflow: hidden;
}

/* Snake-like energy flowing through tails */
.mission-level-container:not(:last-child) .mission-level-card::before {
    content: '';
    position: absolute;
    bottom: -3rem;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 3rem;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(255, 255, 255, 0.9) 20%,
        rgba(255, 255, 255, 1) 50%,
        rgba(255, 255, 255, 0.9) 80%,
        transparent 100%);
    border-radius: 3px;
    box-shadow:
        0 0 15px rgba(255, 255, 255, 0.8),
        0 0 30px rgba(255, 255, 255, 0.4);
    z-index: 2;
    animation: snakeFlow 4s linear infinite;
    opacity: 0;
}

/* Staggered animation delays for continuous snake effect */
.mission-level-container:nth-child(1) .mission-level-card::before {
    animation-delay: 0s;
}

.mission-level-container:nth-child(2) .mission-level-card::before {
    animation-delay: 0.8s;
}

.mission-level-container:nth-child(3) .mission-level-card::before {
    animation-delay: 1.6s;
}

.mission-level-container:nth-child(4) .mission-level-card::before {
    animation-delay: 2.4s;
}

.mission-level-container:nth-child(5) .mission-level-card::before {
    animation-delay: 3.2s;
}

/* Snake flow animation */
@keyframes snakeFlow {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(100%);
        box-shadow:
            0 0 15px rgba(255, 255, 255, 0.8),
            0 0 30px rgba(255, 255, 255, 0.4);
    }

    10% {
        opacity: 1;
        transform: translateX(-50%) translateY(50%);
    }

    90% {
        opacity: 1;
        transform: translateX(-50%) translateY(-50%);
        box-shadow:
            0 0 20px rgba(255, 255, 255, 1),
            0 0 40px rgba(255, 255, 255, 0.6);
    }

    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-100%);
        box-shadow:
            0 0 15px rgba(255, 255, 255, 0.8),
            0 0 30px rgba(255, 255, 255, 0.4);
    }
}

/* Floating Bubbles and Particles */
.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.light-streak {
    position: absolute;
    width: 1px;
    background: linear-gradient(to top,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 20%,
        rgba(30, 144, 255, 0.4) 50%,
        rgba(138, 43, 226, 0.3) 80%,
        transparent 100%);
    animation: streakUp linear infinite;
    border-radius: 1px;
    box-shadow: 0 0 2px rgba(255, 255, 255, 0.2);
}

.glow-dot {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 255, 255, 0.6) 0%,
        rgba(30, 144, 255, 0.4) 40%,
        transparent 70%);
    animation: dotRise linear infinite;
    box-shadow: 0 0 4px rgba(30, 144, 255, 0.3);
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    animation: sparkle linear infinite;
    box-shadow: 0 0 3px rgba(255, 255, 255, 0.4);
}

.energy-orb {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(30, 144, 255, 0.15) 0%,
        rgba(138, 43, 226, 0.1) 50%,
        transparent 70%);
    animation: orbFloat linear infinite;
    filter: blur(2px);
}

@keyframes streakUp {
    0% {
        transform: translateY(100vh) scaleY(0.5);
        opacity: 0;
        height: 20px;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) scaleY(1);
        height: 40px;
    }
    50% {
        height: 60px;
        transform: translateY(50vh) scaleY(1.2);
    }
    90% {
        opacity: 1;
        height: 30px;
        transform: translateY(10vh) scaleY(0.8);
    }
    100% {
        transform: translateY(-10vh) scaleY(0.3);
        opacity: 0;
        height: 10px;
    }
}

@keyframes dotRise {
    0% {
        transform: translateY(100vh) translateX(0px) scale(0.2);
        opacity: 0;
    }
    15% {
        opacity: 1;
        transform: translateY(85vh) translateX(10px) scale(1);
    }
    50% {
        transform: translateY(50vh) translateX(-15px) scale(1.3);
    }
    85% {
        opacity: 1;
        transform: translateY(15vh) translateX(8px) scale(0.8);
    }
    100% {
        transform: translateY(-5vh) translateX(0px) scale(0.1);
        opacity: 0;
    }
}

@keyframes sparkle {
    0% {
        transform: translateY(100vh) translateX(0px) scale(0) rotate(0deg);
        opacity: 0;
        box-shadow: 0 0 0px rgba(255, 255, 255, 0);
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) translateX(15px) scale(1.2) rotate(45deg);
        box-shadow: 0 0 8px rgba(255, 255, 255, 1);
    }
    30% {
        transform: translateY(70vh) translateX(-20px) scale(0.8) rotate(135deg);
        box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
    }
    60% {
        transform: translateY(40vh) translateX(25px) scale(1.5) rotate(225deg);
        box-shadow: 0 0 15px rgba(255, 255, 255, 1);
    }
    85% {
        opacity: 1;
        transform: translateY(15vh) translateX(-10px) scale(0.6) rotate(315deg);
        box-shadow: 0 0 6px rgba(255, 255, 255, 0.6);
    }
    100% {
        transform: translateY(-5vh) translateX(0px) scale(0) rotate(360deg);
        opacity: 0;
        box-shadow: 0 0 0px rgba(255, 255, 255, 0);
    }
}

@keyframes orbFloat {
    0% {
        transform: translateY(100vh) translateX(0px) scale(0.2);
        opacity: 0;
        filter: blur(3px) hue-rotate(0deg);
    }
    15% {
        opacity: 0.9;
        transform: translateY(85vh) translateX(30px) scale(0.8);
        filter: blur(1px) hue-rotate(60deg);
    }
    35% {
        transform: translateY(65vh) translateX(-40px) scale(1.3);
        filter: blur(0.5px) hue-rotate(120deg);
    }
    55% {
        transform: translateY(45vh) translateX(50px) scale(1.1);
        filter: blur(1px) hue-rotate(180deg);
    }
    75% {
        transform: translateY(25vh) translateX(-30px) scale(0.9);
        filter: blur(1.5px) hue-rotate(240deg);
    }
    90% {
        opacity: 0.9;
        transform: translateY(10vh) translateX(20px) scale(0.5);
        filter: blur(2px) hue-rotate(300deg);
    }
    100% {
        transform: translateY(-10vh) translateX(0px) scale(0.1);
        opacity: 0;
        filter: blur(3px) hue-rotate(360deg);
    }
}

/* Level Header */
.level-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.level-number {
    background: linear-gradient(145deg,
        rgba(255, 215, 0, 0.4),
        rgba(255, 193, 7, 0.3));
    color: #ffd700;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 700;
    border: 2px solid rgba(255, 215, 0, 0.5);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.level-title {
    font-size: 1.8rem;
    font-weight: 800;
    margin: 0.8rem 0;
    color: white;
    text-shadow:
        0 2px 10px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.level-description {
    font-size: 1.05rem;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 2rem;
    line-height: 1.6;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    font-weight: 400;
}

/* Level Badges */
.level-badges {
    display: flex;
    gap: 0.8rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.badge {
    display: inline-flex;
    align-items: center;
    gap: 0.6rem;
    padding: 0.6rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 700;
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5) inset;
}

.badge:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.difficulty-badge.easy {
    background: linear-gradient(145deg,
        rgba(76, 175, 80, 0.9),
        rgba(76, 175, 80, 0.8));
    color: #ffffff;
    border-color: rgba(76, 175, 80, 0.9);
    box-shadow:
        0 0 20px rgba(76, 175, 80, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5) inset;
}

.difficulty-badge.medium {
    background: linear-gradient(145deg,
        rgba(255, 193, 7, 0.9),
        rgba(255, 193, 7, 0.8));
    color: #ffffff;
    border-color: rgba(255, 193, 7, 0.9);
    box-shadow:
        0 0 20px rgba(255, 193, 7, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5) inset;
}

.difficulty-badge.hard {
    background: linear-gradient(145deg,
        rgba(244, 67, 54, 0.9),
        rgba(244, 67, 54, 0.8));
    color: #ffffff;
    border-color: rgba(244, 67, 54, 0.9);
    box-shadow:
        0 0 20px rgba(244, 67, 54, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5) inset;
}

.questions-badge {
    background: linear-gradient(145deg,
        rgba(33, 150, 243, 0.9),
        rgba(33, 150, 243, 0.8));
    color: #ffffff;
    border-color: rgba(33, 150, 243, 0.9);
    box-shadow:
        0 0 20px rgba(33, 150, 243, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5) inset;
}

.stars-badge {
    background: linear-gradient(145deg,
        rgba(255, 215, 0, 0.9),
        rgba(255, 215, 0, 0.8));
    color: #1a1a1a;
    border-color: rgba(255, 215, 0, 0.9);
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5) inset;
}

.attempts-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
    border-radius: 20px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.completion-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stars {
    display: flex;
    gap: 0.2rem;
}

.stars .fa-star {
    color: rgba(255, 255, 255, 0.3);
    font-size: 0.9rem;
}

.stars .fa-star.earned {
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.best-score {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.boss-badge {
    background: linear-gradient(45deg, #ff3838, #c62828);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 700;
    border: 1px solid #ffd700;
    box-shadow: 0 2px 10px rgba(255, 56, 56, 0.4);
}

.level-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.level-description {
    font-size: 1rem;
    margin: 0 0 1.5rem 0;
    opacity: 0.9;
    line-height: 1.4;
}

/* Level Stats Pills */
.level-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-pill {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    backdrop-filter: blur(10px);
}

.stat-pill.difficulty-easy {
    background: linear-gradient(145deg, #00c851, #007e33);
    border-color: rgba(0, 200, 81, 0.4);
}

.stat-pill.difficulty-medium {
    background: linear-gradient(145deg, #ffbb33, #ff8800);
    border-color: rgba(255, 187, 51, 0.4);
}

.stat-pill.difficulty-hard {
    background: linear-gradient(145deg, #ff3838, #c62828);
    border-color: rgba(255, 56, 56, 0.4);
}

.stat-pill.unlock-pill {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    color: rgba(255, 255, 255, 0.7);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Completion Stats */
.completion-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.stars {
    display: flex;
    gap: 0.25rem;
}

.stars i {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.3);
}

.stars i.earned {
    color: #ffd700;
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.4);
}

.best-score {
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.9;
}

/* Keep some animations for the new design */
@keyframes pulseGlow {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 0 40px rgba(255, 215, 0, 0.7),
            0 15px 40px rgba(0, 0, 0, 0.4),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 0 60px rgba(255, 215, 0, 1),
            0 15px 40px rgba(0, 0, 0, 0.4),
            0 0 0 1px rgba(255, 255, 255, 0.3) inset;
    }
}

@keyframes availableGlow {
    0%, 100% {
        box-shadow:
            0 0 30px rgba(255, 255, 255, 0.4),
            0 15px 40px rgba(0, 0, 0, 0.4),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        border-color: rgba(255, 255, 255, 1);
    }
    50% {
        box-shadow:
            0 0 40px rgba(255, 255, 255, 0.6),
            0 15px 40px rgba(0, 0, 0, 0.4),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset;
        border-color: rgba(255, 255, 255, 1);
    }
}





@keyframes snakeFlow {
    0%, 100% {
        opacity: 0.8;
        filter: brightness(1);
    }
    25% {
        opacity: 1;
        filter: brightness(1.2);
    }
    50% {
        opacity: 0.9;
        filter: brightness(1.1);
    }
    75% {
        opacity: 1;
        filter: brightness(1.3);
    }
}

/* Removed old level-icon styles - using new play-button styles instead */

/* Removed old level-info styles - using new level-card styles instead */

.stars {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars i {
    color: #ffd700;
    font-size: 1.5rem;
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.4);
    transition: all 0.3s ease;
}

.stars i.earned {
    animation: starTwinkle 2s ease-in-out infinite;
}

@keyframes starTwinkle {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.stars i:not(.earned) {
    color: rgba(255, 255, 255, 0.4);
    text-shadow: none;
}

.best-score {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #ffd700;
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.attempts {
    font-size: 0.95rem;
    font-weight: 500;
    opacity: 0.9;
}

.attempt-info {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    padding: 0.75rem 1rem;
    border-radius: 15px;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.path-connector {
    position: absolute;
    bottom: -4rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4rem;
    background: linear-gradient(to bottom,
        rgba(255, 255, 255, 0.6),
        rgba(255, 255, 255, 0.3),
        rgba(255, 255, 255, 0.1)
    );
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(15px);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    margin: 3% auto;
    padding: 0;
    border-radius: 25px;
    width: 90%;
    max-width: 650px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
    color: white;
    padding: 2rem 2.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 800;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.close {
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    padding: 0.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.modal-body {
    padding: 2.5rem;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

/* Enhanced Animations */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7); }
    70% { box-shadow: 0 0 0 15px rgba(255, 255, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
}

@keyframes starsPulse {
    0% {
        transform: scale(1);
        color: inherit;
    }
    50% {
        transform: scale(1.1);
        color: #ffd700;
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }
    100% {
        transform: scale(1);
        color: inherit;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .mission-header {
        padding: 0.4rem 0;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 0 1rem;
    }

    .back-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .mission-title h1 {
        font-size: 1.5rem;
    }

    .mission-title p {
        font-size: 0.85rem;
    }

    .mission-stats {
        justify-content: center;
        gap: 0.75rem;
        width: 100%;
    }

    .stat-item {
        min-width: 80px;
        padding: 1rem 0.75rem;
        flex: 1;
        max-width: 100px;
    }

    .stat-item i {
        font-size: 1.5rem;
    }

    .stat-item span {
        font-size: 1.2rem;
    }

    .stat-item small {
        font-size: 0.8rem;
    }

    .mission-main {
        padding: 0.75rem 1rem;
    }

    .mission-level-card {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .play-button {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .level-card {
        padding: 1rem;
    }

    .level-title {
        font-size: 1.3rem;
    }

    .level-description {
        font-size: 0.9rem;
    }

    .level-stats {
        justify-content: center;
    }

    .stat-pill {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 0.5rem;
    }

    .mission-title h1 {
        font-size: 1.3rem;
    }

    .mission-stats {
        gap: 0.5rem;
    }

    .stat-item {
        min-width: 70px;
        padding: 0.75rem 0.5rem;
    }

    .back-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
    }

    .back-btn span {
        display: none;
    }

    .mission-main {
        padding: 0.5rem;
    }

    .play-button {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .level-card {
        padding: 0.75rem;
    }

    .level-title {
        font-size: 1.2rem;
    }

    .level-description {
        font-size: 0.85rem;
    }

    .stat-pill {
        font-size: 0.75rem;
        padding: 0.3rem 0.6rem;
    }
}

    .mission-level {
        margin: 0 !important;
        max-width: 100%;
        padding: 1.5rem;
        gap: 1rem;
    }

    .mission-level.right {
        flex-direction: row;
    }

    .level-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .level-info h4 {
        font-size: 1.25rem;
    }

    .level-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .modal-header {
        padding: 1rem 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }
}

/* Real-time Progress Update Animations */
@keyframes levelPulse {
    0% {
        transform: scale(1);
        color: white;
    }
    50% {
        transform: scale(1.2);
        color: #4CAF50;
        text-shadow: 0 0 10px rgba(76, 175, 80, 0.8);
    }
    100% {
        transform: scale(1);
        color: white;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
