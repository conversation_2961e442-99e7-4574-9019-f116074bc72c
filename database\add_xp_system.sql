-- Add XP and Level System to Students Table
-- AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba

-- Add XP and level columns to students table (one at a time to avoid conflicts)
ALTER TABLE students ADD COLUMN xp_points INT DEFAULT 0;
ALTER TABLE students ADD COLUMN current_level INT DEFAULT 1;
ALTER TABLE students ADD COLUMN streak_days INT DEFAULT 0;
ALTER TABLE students ADD COLUMN last_quiz_date DATE NULL;
ALTER TABLE students ADD COLUMN total_quizzes_completed INT DEFAULT 0;
ALTER TABLE students ADD COLUMN best_streak INT DEFAULT 0;

-- Add XP tracking to quiz_sessions table (one at a time to avoid conflicts)
ALTER TABLE quiz_sessions ADD COLUMN xp_earned INT DEFAULT 0;
ALTER TABLE quiz_sessions ADD COLUMN difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'easy';

-- Create achievements table
CREATE TABLE IF NOT EXISTS achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    requirement_type ENUM('xp', 'streak', 'level', 'quizzes', 'score') NOT NULL,
    requirement_value INT NOT NULL,
    xp_reward INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Create student_achievements table (without foreign keys first)
CREATE TABLE IF NOT EXISTS student_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    achievement_id INT NOT NULL,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Add foreign keys and unique constraint separately
ALTER TABLE student_achievements
ADD CONSTRAINT fk_student_achievements_student
FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE;

ALTER TABLE student_achievements
ADD CONSTRAINT fk_student_achievements_achievement
FOREIGN KEY (achievement_id) REFERENCES achievements(id) ON DELETE CASCADE;

ALTER TABLE student_achievements
ADD CONSTRAINT unique_student_achievement
UNIQUE KEY (student_id, achievement_id);

-- Insert default achievements (with ignore to prevent duplicates)
INSERT IGNORE INTO achievements (name, description, icon, requirement_type, requirement_value, xp_reward) VALUES
('First Steps', 'Complete your first quiz', '🎯', 'quizzes', 1, 10),
('Getting Started', 'Earn your first 50 XP', '⭐', 'xp', 50, 5),
('Rising Star', 'Reach Level 5', '🌟', 'level', 5, 25),
('Dedicated Learner', 'Complete 10 quizzes', '📚', 'quizzes', 10, 20),
('Week Warrior', 'Maintain a 7-day streak', '🔥', 'streak', 7, 30),
('Perfect Score', 'Get 100% on any quiz', '💯', 'score', 100, 15),
('XP Master', 'Earn 1000 XP total', '💎', 'xp', 1000, 50),
('Streak Master', 'Maintain a 30-day streak', '🏆', 'streak', 30, 100),
('Level 10 Hero', 'Reach Level 10', '👑', 'level', 10, 75),
('Quiz Champion', 'Complete 50 quizzes', '🏅', 'quizzes', 50, 100);

-- Update existing students with default values first
UPDATE students
SET xp_points = COALESCE(xp_points, 0),
    current_level = COALESCE(current_level, 1),
    streak_days = COALESCE(streak_days, 0),
    total_quizzes_completed = COALESCE(total_quizzes_completed, 0);

-- Update quiz completion counts for existing students
UPDATE students s
SET total_quizzes_completed = (
    SELECT COUNT(*)
    FROM quiz_sessions qs
    WHERE qs.student_id = s.id AND qs.status = 'completed'
)
WHERE s.total_quizzes_completed = 0;

-- Update existing quiz sessions with default difficulty
UPDATE quiz_sessions
SET difficulty = COALESCE(difficulty, 'medium');

-- Drop view if exists and create leaderboard view
DROP VIEW IF EXISTS student_leaderboard;
CREATE VIEW student_leaderboard AS
SELECT
    s.id,
    s.first_name,
    s.last_name,
    s.student_id,
    d.name as department_name,
    al.level_name,
    COALESCE(s.xp_points, 0) as xp_points,
    COALESCE(s.current_level, 1) as current_level,
    COALESCE(s.streak_days, 0) as streak_days,
    COALESCE(s.total_quizzes_completed, 0) as total_quizzes_completed,
    COALESCE((SELECT COUNT(*) FROM student_achievements WHERE student_id = s.id), 0) as achievements_count,
    COALESCE((SELECT AVG(score_percentage) FROM quiz_sessions WHERE student_id = s.id AND status = 'completed'), 0) as avg_score
FROM students s
JOIN departments d ON s.department_id = d.id
JOIN academic_levels al ON s.academic_level_id = al.id
WHERE s.status = 'active'
ORDER BY s.xp_points DESC, s.current_level DESC;

-- Note: Achievement checking will be handled in PHP code instead of stored procedures
-- This is more compatible across different database systems

-- Create indexes for better performance
-- Note: These will be handled by the PHP script to avoid duplicate index errors
