/**
 * Interactive Quiz CSS
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.interactive-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.interactive-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.back-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 12px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.student-info h2 {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
}

.student-info p {
    color: #718096;
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 25px;
}

.xp-display {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    padding: 10px 16px;
    border-radius: 25px;
    font-weight: 600;
    color: #8b5a00;
}

.xp-icon {
    font-size: 18px;
}

.level-display {
    display: flex;
    align-items: center;
    gap: 12px;
}

.level-circle {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 18px;
}

.level-progress {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.progress-bar {
    width: 100px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.progress-text {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
}

.streak-display {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    padding: 10px 16px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
}

.streak-icon {
    font-size: 18px;
}

/* Main Content */
.interactive-main {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Continue Quiz Section */
.continue-quiz-section {
    display: flex;
    justify-content: center;
}

.continue-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
}

.continue-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.continue-card h3 {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 12px;
}

.continue-card p {
    color: #718096;
    font-size: 16px;
    margin-bottom: 30px;
}

.continue-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Difficulty Section */
.difficulty-section {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.difficulty-section h3 {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 8px;
    text-align: center;
}

.difficulty-section > p {
    color: #718096;
    text-align: center;
    margin-bottom: 30px;
    font-size: 16px;
}

.difficulty-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.difficulty-card {
    background: #f8f9fa;
    border: 3px solid transparent;
    border-radius: 16px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.difficulty-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.difficulty-card.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea10, #764ba210);
}

.difficulty-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.difficulty-card h4 {
    font-size: 20px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 8px;
}

.difficulty-card p {
    color: #718096;
    margin-bottom: 16px;
}

.xp-reward {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b5a00;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
    display: inline-block;
}

/* Quiz Options Section */
.quiz-options-section {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.quiz-options-section h3 {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 30px;
}

.quiz-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #718096;
    font-weight: 500;
}

.info-item i {
    color: #667eea;
}

.start-quiz-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.btn {
    border: none;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
}

.btn-large {
    padding: 16px 32px;
    font-size: 18px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn-start {
    position: relative;
    overflow: hidden;
}

.btn-sparkle {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 12px;
    animation: sparkle 2s infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.7; }
}

.encouragement-text {
    color: #718096;
    font-size: 16px;
    font-weight: 500;
}

/* Achievements Section */
.achievements-section {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.achievements-section h3 {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 30px;
    text-align: center;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.achievement-card {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.achievement-card.unlocked {
    background: linear-gradient(135deg, #48bb7810, #38a16910);
    border: 2px solid #48bb78;
}

.achievement-card.locked {
    opacity: 0.5;
    filter: grayscale(100%);
}

.achievement-icon {
    font-size: 40px;
    margin-bottom: 12px;
}

.achievement-card h4 {
    font-size: 16px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
}

.achievement-card p {
    color: #718096;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .interactive-container {
        padding: 15px;
    }
    
    .interactive-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .header-right {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .difficulty-cards {
        grid-template-columns: 1fr;
    }
    
    .quiz-info {
        flex-direction: column;
        gap: 20px;
    }
    
    .achievements-grid {
        grid-template-columns: 1fr;
    }
}
