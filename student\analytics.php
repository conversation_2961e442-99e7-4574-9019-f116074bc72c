<?php
/**
 * Student Learning Analytics Dashboard
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

$studentId = $_SESSION['user_id'];

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $studentId]);

// Get comprehensive analytics data
$analytics = [
    // Time-based performance
    'daily_performance' => fetchAll("
        SELECT DATE(completed_at) as date, 
               AVG(score_percentage) as avg_score,
               COUNT(*) as quiz_count,
               SUM(xp_earned) as total_xp
        FROM quiz_sessions 
        WHERE student_id = :id AND status = 'completed' 
        AND completed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(completed_at)
        ORDER BY date DESC
    ", ['id' => $studentId]),
    
    // Weekly trends
    'weekly_trends' => fetchAll("
        SELECT YEARWEEK(completed_at) as week,
               AVG(score_percentage) as avg_score,
               COUNT(*) as quiz_count,
               MIN(completed_at) as week_start
        FROM quiz_sessions 
        WHERE student_id = :id AND status = 'completed'
        AND completed_at >= DATE_SUB(NOW(), INTERVAL 12 WEEK)
        GROUP BY YEARWEEK(completed_at)
        ORDER BY week DESC
    ", ['id' => $studentId]),
    
    // Performance by difficulty
    'difficulty_performance' => fetchAll("
        SELECT difficulty,
               COUNT(*) as quiz_count,
               AVG(score_percentage) as avg_score,
               MAX(score_percentage) as best_score,
               AVG(time_taken) as avg_time
        FROM quiz_sessions 
        WHERE student_id = :id AND status = 'completed'
        GROUP BY difficulty
        ORDER BY 
            CASE difficulty 
                WHEN 'easy' THEN 1 
                WHEN 'medium' THEN 2 
                WHEN 'hard' THEN 3 
            END
    ", ['id' => $studentId]),
    
    // Learning patterns
    'hourly_patterns' => fetchAll("
        SELECT HOUR(completed_at) as hour,
               COUNT(*) as quiz_count,
               AVG(score_percentage) as avg_score
        FROM quiz_sessions 
        WHERE student_id = :id AND status = 'completed'
        GROUP BY HOUR(completed_at)
        ORDER BY hour
    ", ['id' => $studentId]),
    
    // Subject mastery
    'subject_mastery' => fetchAll("
        SELECT d.name as subject,
               COUNT(*) as quiz_count,
               AVG(qs.score_percentage) as avg_score,
               MAX(qs.score_percentage) as best_score,
               MIN(qs.score_percentage) as worst_score,
               STDDEV(qs.score_percentage) as consistency
        FROM quiz_sessions qs
        JOIN departments d ON qs.department_id = d.id
        WHERE qs.student_id = :id AND qs.status = 'completed'
        GROUP BY d.id, d.name
        HAVING quiz_count >= 3
        ORDER BY avg_score DESC
    ", ['id' => $studentId]),
    
    // Recent improvement trends
    'improvement_trend' => fetchAll("
        SELECT score_percentage, completed_at,
               ROW_NUMBER() OVER (ORDER BY completed_at) as quiz_number
        FROM quiz_sessions 
        WHERE student_id = :id AND status = 'completed'
        ORDER BY completed_at DESC
        LIMIT 20
    ", ['id' => $studentId])
];

// Calculate learning insights
$insights = [];

// Study time insights
if (!empty($analytics['hourly_patterns'])) {
    $bestHours = array_filter($analytics['hourly_patterns'], function($h) {
        return $h['avg_score'] >= 80 && $h['quiz_count'] >= 2;
    });
    if (!empty($bestHours)) {
        $topHour = array_reduce($bestHours, function($carry, $item) {
            return (!$carry || $item['avg_score'] > $carry['avg_score']) ? $item : $carry;
        });
        $insights[] = [
            'type' => 'time',
            'title' => 'Optimal Study Time',
            'message' => "You perform best around " . date('g A', mktime($topHour['hour'], 0)) . " with an average score of " . number_format($topHour['avg_score'], 1) . "%"
        ];
    }
}

// Consistency insights
if (!empty($analytics['subject_mastery'])) {
    $mostConsistent = array_reduce($analytics['subject_mastery'], function($carry, $item) {
        return (!$carry || $item['consistency'] < $carry['consistency']) ? $item : $carry;
    });
    if ($mostConsistent['consistency'] < 15) {
        $insights[] = [
            'type' => 'consistency',
            'title' => 'Most Consistent Subject',
            'message' => "You're most consistent in {$mostConsistent['subject']} with steady performance across quizzes"
        ];
    }
}

// Improvement insights
if (count($analytics['improvement_trend']) >= 10) {
    $recent5 = array_slice($analytics['improvement_trend'], 0, 5);
    $previous5 = array_slice($analytics['improvement_trend'], 5, 5);
    $recentAvg = array_sum(array_column($recent5, 'score_percentage')) / 5;
    $previousAvg = array_sum(array_column($previous5, 'score_percentage')) / 5;
    
    if ($recentAvg > $previousAvg + 5) {
        $insights[] = [
            'type' => 'improvement',
            'title' => 'Strong Improvement Trend',
            'message' => "Your recent performance has improved by " . number_format($recentAvg - $previousAvg, 1) . "% compared to earlier attempts"
        ];
    }
}

$pageTitle = "Learning Analytics";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Student-specific styling */
        .sidebar-header h3 { color: #28a745; }
        .nav-item.active { background: linear-gradient(135deg, #28a745, #20c997); }
        .btn-primary { background: linear-gradient(135deg, #28a745, #20c997); }
        
        .analytics-container {
            display: grid;
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .analytics-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .card-title {
            color: #2c3e50;
            margin: 0;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .insight-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #28a745;
        }
        
        .insight-icon {
            width: 40px;
            height: 40px;
            background: #28a745;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }
        
        .insight-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .insight-message {
            color: #6c757d;
            line-height: 1.5;
        }
        
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .summary-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .summary-value {
            font-size: 24px;
            font-weight: 700;
            color: #28a745;
            display: block;
        }
        
        .summary-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .performance-table th,
        .performance-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .performance-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .score-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Student Portal</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="quick-game.php" class="nav-item">
                    <i class="fas fa-bolt"></i>
                    <span>Quick Game</span>
                </a>
                <a href="endless-game.php" class="nav-item">
                    <i class="fas fa-infinity"></i>
                    <span>Endless Mode</span>
                </a>
                <a href="mission-mode.php" class="nav-item">
                    <i class="fas fa-map"></i>
                    <span>Mission Mode</span>
                </a>
                <a href="results.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>My Results</span>
                </a>
                <a href="analytics.php" class="nav-item active">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="notifications.php" class="nav-item">
                    <i class="fas fa-bell"></i>
                    <span>Notifications</span>
                </a>
                <a href="profile.php" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
                <a href="../index.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Go Back to Site</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1><i class="fas fa-chart-bar"></i> Learning Analytics</h1>
                    <p>Deep insights into your learning patterns and performance</p>
                </div>
            </header>

            <!-- Learning Insights -->
            <?php if (!empty($insights)): ?>
            <div class="insights-grid">
                <?php foreach ($insights as $insight): ?>
                <div class="insight-card">
                    <div class="insight-icon">
                        <?php
                        switch ($insight['type']) {
                            case 'time':
                                echo '<i class="fas fa-clock"></i>';
                                break;
                            case 'consistency':
                                echo '<i class="fas fa-chart-line"></i>';
                                break;
                            case 'improvement':
                                echo '<i class="fas fa-trending-up"></i>';
                                break;
                            default:
                                echo '<i class="fas fa-lightbulb"></i>';
                        }
                        ?>
                    </div>
                    <div class="insight-title"><?php echo htmlspecialchars($insight['title']); ?></div>
                    <div class="insight-message"><?php echo htmlspecialchars($insight['message']); ?></div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <div class="analytics-container">
                <!-- Performance by Difficulty -->
                <?php if (!empty($analytics['difficulty_performance'])): ?>
                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-layer-group"></i>
                            Performance by Difficulty
                        </h3>
                    </div>
                    
                    <div class="stats-summary">
                        <?php foreach ($analytics['difficulty_performance'] as $diff): ?>
                        <div class="summary-item">
                            <span class="summary-value"><?php echo number_format($diff['avg_score'], 1); ?>%</span>
                            <span class="summary-label"><?php echo ucfirst($diff['difficulty']); ?> Average</span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <table class="performance-table">
                        <thead>
                            <tr>
                                <th>Difficulty</th>
                                <th>Quizzes</th>
                                <th>Average Score</th>
                                <th>Best Score</th>
                                <th>Avg Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($analytics['difficulty_performance'] as $diff): ?>
                            <tr>
                                <td>
                                    <span class="difficulty-badge <?php echo $diff['difficulty']; ?>">
                                        <?php echo ucfirst($diff['difficulty']); ?>
                                    </span>
                                </td>
                                <td><?php echo $diff['quiz_count']; ?></td>
                                <td>
                                    <?php echo number_format($diff['avg_score'], 1); ?>%
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: <?php echo min($diff['avg_score'], 100); ?>%"></div>
                                    </div>
                                </td>
                                <td><?php echo number_format($diff['best_score'], 1); ?>%</td>
                                <td><?php echo $diff['avg_time'] ? gmdate("i:s", $diff['avg_time'] * 60) : 'N/A'; ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
