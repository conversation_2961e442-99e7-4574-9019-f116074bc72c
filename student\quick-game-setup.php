<?php
/**
 * Quick Game Setup for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get student information
$student = fetchOne("SELECT * FROM students WHERE id = :id", ['id' => $_SESSION['user_id']]);

if (!$student) {
    header('Location: login.php');
    exit();
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Game Setup - Knowledge Arena</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .setup-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            min-height: calc(100vh - 40px);
            display: flex;
            flex-direction: column;
        }

        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 40px;
            text-align: center;
            flex-shrink: 0;
        }

        .setup-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .setup-header p {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }

        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-button:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: translateY(-2px);
        }

        .setup-content {
            padding: 30px 40px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .content-sections {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
        }

        .section {
            margin-bottom: 15px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .difficulty-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .difficulty-card {
            background: white;
            border: 3px solid #e2e8f0;
            border-radius: 15px;
            padding: 20px 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .difficulty-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .difficulty-card:hover::before,
        .difficulty-card.selected::before {
            transform: scaleX(1);
        }

        .difficulty-card.easy {
            --card-color: #10b981;
        }

        .difficulty-card.medium {
            --card-color: #f59e0b;
        }

        .difficulty-card.hard {
            --card-color: #ef4444;
        }

        .difficulty-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            border-color: var(--card-color);
        }

        .difficulty-card.selected {
            border-color: var(--card-color);
            background: linear-gradient(135deg, var(--card-color), var(--card-color));
            color: white;
        }

        .difficulty-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 20px;
            background: var(--card-color);
            color: white;
        }

        .difficulty-card.selected .difficulty-icon {
            background: rgba(255,255,255,0.2);
        }

        .difficulty-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .difficulty-desc {
            font-size: 0.85rem;
            opacity: 0.7;
        }

        .questions-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 25px;
        }

        .question-card {
            background: white;
            border: 3px solid #e2e8f0;
            border-radius: 15px;
            padding: 18px 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .question-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .question-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .question-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 3px;
        }

        .question-card.selected .question-number {
            color: white;
        }

        .start-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-top: auto;
        }

        .start-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .start-button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }

        .start-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .start-button:hover:not(:disabled)::before {
            left: 100%;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <a href="dashboard.php" class="back-button">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Dashboard
        </a>

        <div class="setup-container">
            <div class="setup-header">
                <h1>
                    <i class="fas fa-bolt"></i>
                    Quick Game Setup
                </h1>
                <p>Choose your difficulty level and number of questions for a quick knowledge challenge!</p>
            </div>

            <div class="setup-content">
                <div class="content-sections">
                    <!-- Difficulty Level Section -->
                    <div class="section">
                        <h2 class="section-title">
                            <i class="fas fa-chart-line"></i>
                            Difficulty Level
                        </h2>
                        <div class="difficulty-grid">
                            <div class="difficulty-card easy" onclick="selectDifficulty('easy')" data-difficulty="easy">
                                <div class="difficulty-icon">
                                    <i class="fas fa-seedling"></i>
                                </div>
                                <div class="difficulty-title">Easy</div>
                                <div class="difficulty-desc">Basic questions</div>
                            </div>
                            <div class="difficulty-card medium" onclick="selectDifficulty('medium')" data-difficulty="medium">
                                <div class="difficulty-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="difficulty-title">Medium</div>
                                <div class="difficulty-desc">Moderate challenge</div>
                            </div>
                            <div class="difficulty-card hard" onclick="selectDifficulty('hard')" data-difficulty="hard">
                                <div class="difficulty-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="difficulty-title">Hard</div>
                                <div class="difficulty-desc">Expert level</div>
                            </div>
                        </div>
                    </div>

                    <!-- Number of Questions Section -->
                    <div class="section">
                        <h2 class="section-title">
                            <i class="fas fa-question-circle"></i>
                            Number of Questions
                        </h2>
                        <div class="questions-grid">
                            <div class="question-card" onclick="selectQuestions(5)" data-questions="5">
                                <div class="question-number">5</div>
                            </div>
                            <div class="question-card selected" onclick="selectQuestions(10)" data-questions="10">
                                <div class="question-number">10</div>
                            </div>
                            <div class="question-card" onclick="selectQuestions(15)" data-questions="15">
                                <div class="question-number">15</div>
                            </div>
                            <div class="question-card" onclick="selectQuestions(20)" data-questions="20">
                                <div class="question-number">20</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Start Button -->
                <button class="start-button" onclick="startQuickGame()" disabled>
                    <i class="fas fa-play me-2"></i>
                    Start Quick Game
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let selectedDifficulty = null;
        let selectedQuestions = 10; // Default

        function selectDifficulty(difficulty) {
            // Remove previous selection
            document.querySelectorAll('.difficulty-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selection to clicked card
            document.querySelector(`[data-difficulty="${difficulty}"]`).classList.add('selected');
            selectedDifficulty = difficulty;

            updateStartButton();
        }

        function selectQuestions(count) {
            // Remove previous selection
            document.querySelectorAll('.question-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selection to clicked card
            document.querySelector(`[data-questions="${count}"]`).classList.add('selected');
            selectedQuestions = count;

            updateStartButton();
        }

        function updateStartButton() {
            const startButton = document.querySelector('.start-button');
            if (selectedDifficulty && selectedQuestions) {
                startButton.disabled = false;
                startButton.innerHTML = '<i class="fas fa-play me-2"></i>Start Quick Game';
            } else {
                startButton.disabled = true;
                startButton.innerHTML = '<i class="fas fa-play me-2"></i>Select Difficulty & Questions';
            }
        }

        function startQuickGame() {
            if (selectedDifficulty && selectedQuestions) {
                // Add loading state
                const startButton = document.querySelector('.start-button');
                startButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting Game...';
                startButton.disabled = true;

                // Navigate to game
                setTimeout(() => {
                    window.location.href = `quick-game.php?difficulty=${selectedDifficulty}&questions=${selectedQuestions}`;
                }, 500);
            }
        }

        // Initialize with default selection
        updateStartButton();

        // Add smooth animations on page load
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.difficulty-card, .question-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
