/* Authentication Styles for AI-Powered LMS */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow-x: hidden;
}

.auth-container {
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 10;
    animation: slideUp 0.8s ease-out;
}

/* Enhanced container for registration */
.auth-container:has(.register-card) {
    max-width: 800px;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.auth-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-20px, -20px) rotate(360deg); }
}

.logo-container {
    position: relative;
    z-index: 1;
    margin-bottom: 20px;
}

.logo {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.logo:hover {
    transform: scale(1.05) rotate(5deg);
    border-color: rgba(255, 255, 255, 0.6);
}

.auth-header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
}

.auth-header p {
    font-size: 0.95rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.school-name {
    font-weight: 600;
    font-size: 1rem !important;
    margin-top: 8px;
    opacity: 1 !important;
}

.auth-body {
    padding: 40px 30px;
}

.alert {
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.alert-error {
    background: linear-gradient(135deg, #fef2f2 0%, #fde8e8 100%);
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-success {
    background: linear-gradient(135deg, #f0fdf4 0%, #e6ffed 100%);
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.form-group label i {
    color: #6366f1;
    width: 16px;
}

.form-group input {
    padding: 16px 18px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
    font-family: inherit;
}

.form-group input:focus {
    outline: none;
    border-color: #6366f1;
    background: white;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.form-group select {
    padding: 16px 18px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
    font-family: inherit;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.form-group select:focus {
    outline: none;
    border-color: #6366f1;
    background: white;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.toggle-password:hover {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: -8px 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #6b7280;
    user-select: none;
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: #6366f1;
    border-color: #6366f1;
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.btn {
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-align: center;
    font-family: inherit;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-login {
    width: 100%;
    padding: 18px;
    font-size: 1.1rem;
}

.auth-links {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    gap: 16px;
}

.link-secondary {
    color: #6b7280;
    text-decoration: none;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 8px;
}

.link-secondary:hover {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.1);
}

.auth-footer {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    padding: 24px 30px;
    text-align: center;
    color: #6b7280;
    font-size: 0.85rem;
    border-top: 1px solid #e5e7eb;
}

.security-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;
    color: #16a34a;
    font-weight: 500;
}

.security-info i {
    color: #16a34a;
}

.background-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: floatCircle 15s infinite ease-in-out;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: -5%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: -5%;
    animation-delay: 5s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 10s;
}

@keyframes floatCircle {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.input-help {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 6px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.input-help i {
    color: #9ca3af;
}

.forgot-password {
    color: #6366f1;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #4f46e5;
    text-decoration: underline;
}

.auth-divider {
    text-align: center;
    margin: 24px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
}

.auth-divider span {
    background: white;
    padding: 0 16px;
    color: #6b7280;
    font-size: 0.9rem;
    position: relative;
}

.btn-secondary {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #374151;
    border: 2px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-register-link {
    width: 100%;
    padding: 16px;
    font-size: 1rem;
    margin-bottom: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        margin: 10px;
    }

    .auth-header {
        padding: 30px 20px;
    }

    .auth-body {
        padding: 30px 20px;
    }

    .auth-footer {
        padding: 20px;
    }

    .logo {
        width: 60px;
        height: 60px;
    }

    .auth-header h1 {
        font-size: 1.5rem;
    }

    .auth-links {
        flex-direction: column;
        gap: 12px;
    }

    .link-secondary {
        justify-content: center;
    }
}
