<?php
/**
 * Authentication Functions
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_type']);
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return isLoggedIn() && $_SESSION['user_type'] === 'admin';
}

/**
 * Check if user is student
 */
function isStudent() {
    return isLoggedIn() && $_SESSION['user_type'] === 'student';
}

/**
 * Require login for specific user type (conditional declaration to avoid conflicts)
 */
if (!function_exists('requireLogin')) {
    function requireLogin($userType = null) {
        if (!isLoggedIn()) {
            header('Location: ../login.php');
            exit;
        }

        if ($userType && $_SESSION['user_type'] !== $userType) {
            header('Location: ../login.php');
            exit;
        }
    }
}

/**
 * Get current user ID
 */
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get current user type
 */
function getCurrentUserType() {
    return $_SESSION['user_type'] ?? null;
}

/**
 * Get current user info
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    require_once __DIR__ . '/database.php';
    
    $userId = getCurrentUserId();
    $userType = getCurrentUserType();
    
    if ($userType === 'admin') {
        return fetchOne("SELECT * FROM admins WHERE id = :id", ['id' => $userId]);
    } elseif ($userType === 'student') {
        return fetchOne("SELECT * FROM students WHERE id = :id", ['id' => $userId]);
    }
    
    return null;
}

/**
 * Login user
 */
function loginUser($userId, $userType) {
    $_SESSION['user_id'] = $userId;
    $_SESSION['user_type'] = $userType;
    $_SESSION['login_time'] = time();
}

/**
 * Logout user
 */
function logoutUser() {
    session_unset();
    session_destroy();
}

/**
 * Check if session is valid (not expired)
 */
function isSessionValid() {
    if (!isLoggedIn()) {
        return false;
    }
    
    // Check if session is older than 24 hours
    $loginTime = $_SESSION['login_time'] ?? 0;
    $sessionTimeout = 24 * 60 * 60; // 24 hours in seconds
    
    if (time() - $loginTime > $sessionTimeout) {
        logoutUser();
        return false;
    }
    
    return true;
}

/**
 * Redirect based on user type
 */
function redirectToDashboard() {
    if (isAdmin()) {
        header('Location: admin/dashboard.php');
    } elseif (isStudent()) {
        header('Location: student/dashboard.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check if user has permission
 */
function hasPermission($permission) {
    // For now, admins have all permissions
    if (isAdmin()) {
        return true;
    }
    
    // Students have limited permissions
    if (isStudent()) {
        $allowedPermissions = ['view_own_profile', 'take_quiz', 'view_own_results'];
        return in_array($permission, $allowedPermissions);
    }
    
    return false;
}
?>
