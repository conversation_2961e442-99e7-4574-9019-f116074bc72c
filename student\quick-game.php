<?php
/**
 * Quick Game Mode for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get game parameters
$difficulty = $_GET['difficulty'] ?? 'easy';
$questionCount = (int)($_GET['questions'] ?? 10);

// Validate parameters
if (!in_array($difficulty, ['easy', 'medium', 'hard'])) {
    $difficulty = 'easy';
}
if ($questionCount < 5 || $questionCount > 50) {
    $questionCount = 10;
}

// Get student information
$student = fetchOne("SELECT * FROM students WHERE id = :id", ['id' => $_SESSION['user_id']]);

if (!$student) {
    header('Location: login.php');
    exit();
}

// Create new game session
$sessionId = insertRecord('game_sessions', [
    'student_id' => $student['id'],
    'game_mode' => 'quick',
    'difficulty_level' => $difficulty,
    'questions_count' => $questionCount,
    'status' => 'active'
]);

if (!$sessionId) {
    die('Failed to create game session');
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Game - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link rel="stylesheet" href="../assets/css/quick-game.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body>
    <div class="quick-game-container">
        <!-- Game Header -->
        <header class="game-header">
            <div class="header-content">
                <div class="game-info">
                    <h1><i class="fas fa-bolt"></i> Quick Game</h1>
                    <div class="difficulty-badge <?php echo $difficulty; ?>">
                        <i class="fas fa-<?php echo $difficulty === 'easy' ? 'seedling' : ($difficulty === 'medium' ? 'fire' : 'bolt'); ?>"></i>
                        <?php echo ucfirst($difficulty); ?>
                    </div>
                </div>
                
                <div class="game-stats">
                    <div class="stat-item">
                        <i class="fas fa-question-circle"></i>
                        <span id="current-question">1</span> / <span id="total-questions"><?php echo $questionCount; ?></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span id="current-score">0</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span id="timer">30</span>s
                    </div>
                </div>
                
                <div class="game-actions">
                    <button class="btn-icon" onclick="pauseGame()">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="btn-icon" onclick="quitGame()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
            </div>
        </header>

        <!-- Game Content -->
        <main class="game-main">
            <!-- Loading Screen -->
            <div class="loading-screen" id="loading-screen">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <h2>Generating Questions...</h2>
                    <p>Preparing your <?php echo ucfirst($difficulty); ?> level challenge</p>
                    <div class="loading-progress">
                        <div class="loading-bar" id="loading-bar"></div>
                    </div>
                </div>
            </div>

            <!-- Question Container -->
            <div class="question-container" id="question-container" style="display: none;">
                <div class="question-card animate__animated">
                    <div class="question-header">
                        <div class="question-number">
                            Question <span id="question-num">1</span>
                        </div>
                        <div class="question-timer">
                            <div class="timer-circle">
                                <span id="question-timer">30</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="question-content">
                        <h2 id="question-text">Loading question...</h2>
                    </div>
                    
                    <div class="options-container">
                        <button class="option-btn" data-option="A" onclick="selectAnswer('A')">
                            <span class="option-letter">A</span>
                            <span class="option-text" id="option-a">Loading...</span>
                        </button>
                        <button class="option-btn" data-option="B" onclick="selectAnswer('B')">
                            <span class="option-letter">B</span>
                            <span class="option-text" id="option-b">Loading...</span>
                        </button>
                        <button class="option-btn" data-option="C" onclick="selectAnswer('C')">
                            <span class="option-letter">C</span>
                            <span class="option-text" id="option-c">Loading...</span>
                        </button>
                        <button class="option-btn" data-option="D" onclick="selectAnswer('D')">
                            <span class="option-letter">D</span>
                            <span class="option-text" id="option-d">Loading...</span>
                        </button>
                    </div>
                    
                    <div class="question-actions">
                        <button class="btn-submit" id="submit-btn" onclick="submitAnswer()" disabled>
                            <i class="fas fa-check"></i>
                            Submit Answer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Result Screen -->
            <div class="result-screen" id="result-screen" style="display: none;">
                <div class="result-content animate__animated animate__bounceIn">
                    <div class="result-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h1>Congratulations!</h1>
                    <h2>You Are Amazing! 🎉</h2>
                    
                    <div class="final-stats">
                        <div class="stat-card">
                            <div class="stat-value" id="final-score">0</div>
                            <div class="stat-label">Points Earned</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="correct-count">0</div>
                            <div class="stat-label">Correct Answers</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="accuracy-percent">0%</div>
                            <div class="stat-label">Accuracy</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="time-taken">0s</div>
                            <div class="stat-label">Time Taken</div>
                        </div>
                    </div>
                    
                    <div class="result-actions">
                        <button class="btn-primary" onclick="playAgain()">
                            <i class="fas fa-redo"></i>
                            Play Again
                        </button>
                        <button class="btn-secondary" onclick="backToDashboard()">
                            <i class="fas fa-home"></i>
                            Dashboard
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Game Data -->
    <script>
        const gameConfig = {
            sessionId: <?php echo $sessionId; ?>,
            difficulty: '<?php echo $difficulty; ?>',
            totalQuestions: <?php echo $questionCount; ?>,
            studentId: <?php echo $student['id']; ?>,
            departmentId: <?php echo $student['department_id']; ?>,
            academicLevelId: <?php echo $student['academic_level_id']; ?>
        };
    </script>
    
    <script src="../assets/js/quick-game.js"></script>
</body>
</html>
