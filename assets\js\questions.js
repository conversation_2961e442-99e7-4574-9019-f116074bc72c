/**
 * AI Questions Management JavaScript
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeQuestionsPage();
});

function initializeQuestionsPage() {
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize question preview
    initializeQuestionPreview();
}

function initializeFormValidation() {
    const form = document.getElementById('generateForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateGenerateForm()) {
                e.preventDefault();
            } else {
                // Show loading state
                showGenerationProgress();
            }
        });
    }
}

function validateGenerateForm() {
    let isValid = true;
    
    const subjectId = document.getElementById('subject_id').value;
    const difficulty = document.getElementById('difficulty').value;
    const count = document.getElementById('count').value;
    
    // Clear previous errors
    clearFormErrors();
    
    if (!subjectId) {
        showFieldError('subject_id', 'Please select a subject');
        isValid = false;
    }
    
    if (!difficulty) {
        showFieldError('difficulty', 'Please select a difficulty level');
        isValid = false;
    }
    
    if (!count || count < 1 || count > 100) {
        showFieldError('count', 'Please enter a valid number of questions (1-100)');
        isValid = false;
    }
    
    return isValid;
}

function showFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const formGroup = field.closest('.form-group');
    
    let errorElement = formGroup.querySelector('.field-error');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        formGroup.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    field.classList.add('error');
}

function clearFormErrors() {
    const errorElements = document.querySelectorAll('.field-error');
    errorElements.forEach(element => element.remove());
    
    const errorFields = document.querySelectorAll('.error');
    errorFields.forEach(field => field.classList.remove('error'));
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + G for generate questions
        if (e.ctrlKey && e.key === 'g') {
            e.preventDefault();
            openGenerateModal();
        }
        
        // Escape to close modal
        if (e.key === 'Escape') {
            closeGenerateModal();
        }
    });
}

function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function initializeQuestionPreview() {
    // Add click handlers to question cards for preview
    const questionCards = document.querySelectorAll('.question-card');
    questionCards.forEach(card => {
        card.addEventListener('click', function(e) {
            if (!e.target.closest('.question-actions')) {
                const questionId = this.dataset.questionId;
                if (questionId) {
                    viewQuestion(questionId);
                }
            }
        });
    });
}

function showTooltip(event) {
    const element = event.target.closest('[title]');
    const title = element.getAttribute('title');
    
    if (title) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = title;
        tooltip.style.position = 'absolute';
        tooltip.style.zIndex = '1000';
        tooltip.style.backgroundColor = '#333';
        tooltip.style.color = 'white';
        tooltip.style.padding = '5px 10px';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.whiteSpace = 'nowrap';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        
        element._tooltip = tooltip;
    }
}

function hideTooltip(event) {
    const element = event.target.closest('[title]');
    if (element._tooltip) {
        document.body.removeChild(element._tooltip);
        element._tooltip = null;
    }
}

function openGenerateModal() {
    const modal = document.getElementById('generateModal');
    const form = document.getElementById('generateForm');
    
    // Reset form
    form.reset();
    clearFormErrors();
    
    // Set default values
    document.getElementById('count').value = 20;
    
    // Show modal
    modal.style.display = 'block';
    
    // Focus on subject selection
    setTimeout(() => {
        document.getElementById('subject_id').focus();
    }, 100);
}

function closeGenerateModal() {
    const modal = document.getElementById('generateModal');
    modal.style.display = 'none';
    
    // Clear form
    document.getElementById('generateForm').reset();
    clearFormErrors();
    
    // Hide any progress indicators
    hideGenerationProgress();
}

function showGenerationProgress() {
    const submitButton = document.querySelector('#generateForm button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    submitButton.disabled = true;
    submitButton.innerHTML = `
        <i class="fas fa-spinner fa-spin"></i>
        Generating Questions...
    `;
    
    // Store original text for restoration
    submitButton._originalText = originalText;
    
    // Show progress message
    const progressDiv = document.createElement('div');
    progressDiv.id = 'generationProgress';
    progressDiv.className = 'generation-progress';
    progressDiv.innerHTML = `
        <div class="progress-content">
            <div class="progress-spinner">
                <i class="fas fa-robot fa-spin"></i>
            </div>
            <h4>AI is generating questions...</h4>
            <p>This may take a few moments depending on the number of questions requested.</p>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>
    `;
    
    const modalBody = document.querySelector('#generateModal .modal-body');
    modalBody.appendChild(progressDiv);
    
    // Simulate progress (since we don't have real-time feedback)
    animateProgress();
}

function hideGenerationProgress() {
    const submitButton = document.querySelector('#generateForm button[type="submit"]');
    const progressDiv = document.getElementById('generationProgress');
    
    if (submitButton && submitButton._originalText) {
        submitButton.disabled = false;
        submitButton.innerHTML = submitButton._originalText;
    }
    
    if (progressDiv) {
        progressDiv.remove();
    }
}

function animateProgress() {
    const progressFill = document.querySelector('.progress-fill');
    if (!progressFill) return;
    
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress > 90) progress = 90; // Don't complete until actual completion
        
        progressFill.style.width = progress + '%';
        
        if (progress >= 90) {
            clearInterval(interval);
        }
    }, 500);
}

function viewQuestion(questionId) {
    fetch(`ajax/get-question.php?id=${questionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showQuestionModal(data.question);
            } else {
                showErrorMessage('Error loading question details');
            }
        })
        .catch(error => {
            showErrorMessage('Error loading question: ' + error.message);
        });
}

function showQuestionModal(question) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Question Details</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="question-details">
                    <div class="question-meta">
                        <span class="department-tag">${question.department_name} - ${question.level_name}</span>
                        <span class="difficulty-badge ${question.difficulty}">${question.difficulty}</span>
                    </div>
                    
                    <div class="question-text">
                        <h4>Question:</h4>
                        <p>${question.question_text}</p>
                    </div>
                    
                    <div class="question-options">
                        <h4>Options:</h4>
                        <div class="option ${question.correct_answer === 'A' ? 'correct' : ''}">
                            <span class="option-letter">A</span>
                            <span>${question.option_a}</span>
                        </div>
                        <div class="option ${question.correct_answer === 'B' ? 'correct' : ''}">
                            <span class="option-letter">B</span>
                            <span>${question.option_b}</span>
                        </div>
                        <div class="option ${question.correct_answer === 'C' ? 'correct' : ''}">
                            <span class="option-letter">C</span>
                            <span>${question.option_c}</span>
                        </div>
                        <div class="option ${question.correct_answer === 'D' ? 'correct' : ''}">
                            <span class="option-letter">D</span>
                            <span>${question.option_d}</span>
                        </div>
                    </div>
                    
                    ${question.explanation ? `
                        <div class="question-explanation">
                            <h4>Explanation:</h4>
                            <p>${question.explanation}</p>
                        </div>
                    ` : ''}
                    
                    <div class="question-info">
                        <small>Generated: ${new Date(question.created_at).toLocaleString()}</small>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function deleteQuestion(questionId) {
    if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
        fetch('ajax/delete-question.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${questionId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove question card from DOM
                const questionCard = document.querySelector(`[data-question-id="${questionId}"]`);
                if (questionCard) {
                    questionCard.remove();
                }
                showSuccessMessage('Question deleted successfully');
            } else {
                showErrorMessage(data.error || 'Error deleting question');
            }
        })
        .catch(error => {
            showErrorMessage('Error deleting question: ' + error.message);
        });
    }
}

function regenerateQuestions(subjectId, difficulty) {
    if (confirm(`Are you sure you want to regenerate all ${difficulty} questions for this subject? This will delete existing questions and generate new ones.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="regenerate_questions">
            <input type="hidden" name="subject_id" value="${subjectId}">
            <input type="hidden" name="difficulty" value="${difficulty}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}

function showErrorMessage(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-error';
    alert.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        ${message}
    `;
    
    const mainContent = document.querySelector('.main-content');
    const header = mainContent.querySelector('.content-header');
    
    mainContent.insertBefore(alert, header.nextSibling);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

function showSuccessMessage(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success';
    alert.innerHTML = `
        <i class="fas fa-check-circle"></i>
        ${message}
    `;
    
    const mainContent = document.querySelector('.main-content');
    const header = mainContent.querySelector('.content-header');
    
    mainContent.insertBefore(alert, header.nextSibling);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('generateModal');
    if (e.target === modal) {
        closeGenerateModal();
    }
});

// Export functions for global access
window.openGenerateModal = openGenerateModal;
window.closeGenerateModal = closeGenerateModal;
window.viewQuestion = viewQuestion;
window.deleteQuestion = deleteQuestion;
window.regenerateQuestions = regenerateQuestions;
