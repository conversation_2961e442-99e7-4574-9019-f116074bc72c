# 🚀 LMS Installation Guide
## AI-Powered Learning Management System

This guide will help you install and set up the LMS system on any computer with XAMPP or similar web server environment.

## 📋 Prerequisites

Before starting the installation, ensure you have:

- **XAMPP** (or WAMP/LAMP) installed and running
- **Web browser** (Chrome, Firefox, Safari, or Edge)
- **Basic computer knowledge** for file operations

## 🛠️ Step-by-Step Installation

### Step 1: Download and Extract Files

1. **Download the LMS package** to your computer
2. **Extract all files** to your web server directory:
   - **For XAMPP**: Extract to `C:\xampp\htdocs\LMS\`
   - **For WAMP**: Extract to `C:\wamp64\www\LMS\`
   - **For LAMP**: Extract to `/var/www/html/LMS/`

### Step 2: Start Your Web Server

1. **Open XAMPP Control Panel**
2. **Start Apache** service (click "Start" button)
3. **Start MySQL** service (click "Start" button)
4. **Verify services are running** (should show green "Running" status)

### Step 3: Run the Auto-Installer

1. **Open your web browser**
2. **Navigate to**: `http://localhost/LMS/importdb.php`
3. **Follow the installation wizard**:

#### Installation Steps:
- ✅ **Check Requirements**: System will verify PHP and MySQL
- ✅ **Create Database**: Automatically creates `lms_database`
- ✅ **Create Tables**: Sets up all required database tables
- ✅ **Insert Sample Data**: Adds departments, levels, and sample students
- ✅ **Setup Admin Account**: Create your administrator account

### Step 4: Create Admin Account

When prompted, fill in your administrator details:

- **First Name**: Your first name
- **Last Name**: Your last name  
- **Username**: Choose a unique username (e.g., admin)
- **Email**: Your email address
- **Password**: Strong password (minimum 8 characters)
- **Confirm Password**: Re-enter your password

**Password Requirements**:
- At least 8 characters long
- Include uppercase and lowercase letters
- Include numbers
- Include special characters (!@#$%^&*)

### Step 5: Complete Installation

1. **Click "Create Admin Account"**
2. **Wait for confirmation** message
3. **Note down your login credentials**
4. **Click "Login to Admin Panel"**

## 🎯 Post-Installation Setup

### Add Sample Questions

1. **Navigate to**: `http://localhost/LMS/admin/seed-questions.php`
2. **Click "Seed Questions"** to populate the question database
3. **Wait for completion** (adds 200+ questions across departments)

### Test Student Login

Use the sample student account:
- **Email**: `<EMAIL>`
- **Password**: `student123`

### Verify System

1. **Login as admin** to access the admin panel
2. **Login as student** to test the quiz system
3. **Take a sample quiz** to verify question generation
4. **Check results** to ensure scoring works correctly

## 🔧 Configuration Options

### Database Settings

If you need to change database settings, edit `config/database.php`:

```php
define('DB_HOST', 'localhost');     // Database host
define('DB_NAME', 'lms_database');  // Database name
define('DB_USER', 'root');          // Database username
define('DB_PASS', '');              // Database password
```

### AI API Configuration (Optional)

For enhanced question generation, you can add API keys in `student/quiz.php`:

```php
// Google Gemini API Key (optional)
$geminiApiKey = 'your-api-key-here';

// OpenAI API Key (optional)  
$openaiApiKey = 'your-api-key-here';
```

**Note**: The system works perfectly without API keys using the local question bank.

## 🏫 System Access

### Admin Panel
- **URL**: `http://localhost/LMS/login.php`
- **Login with**: Your admin credentials
- **Features**: Manage students, questions, view reports

### Student Portal  
- **URL**: `http://localhost/LMS/login.php`
- **Login with**: Student credentials
- **Features**: Take quizzes, view results, track progress

## 👥 User Management

### Adding New Students

1. **Login as admin**
2. **Go to "Students" section**
3. **Click "Add New Student"**
4. **Fill in student details**:
   - Student ID (e.g., CS/ND1/001)
   - Name and contact information
   - Department and academic level
   - Email and password

### Adding New Departments

1. **Login as admin**
2. **Go to "Departments" section**
3. **Add department details**:
   - Department name
   - Department code
   - Description

## 🧪 Testing the System

### Test Quiz Generation

1. **Login as a student**
2. **Click "Take Quiz"**
3. **Verify questions appear** for the student's department
4. **Complete the quiz**
5. **Check results page** for detailed feedback

### Test Admin Functions

1. **Login as admin**
2. **View student list**
3. **Check quiz results**
4. **Add new questions**
5. **Generate reports**

## 🔍 Troubleshooting

### Common Issues and Solutions

#### "Database Connection Failed"
- **Check**: XAMPP MySQL service is running
- **Verify**: Database credentials in `config/database.php`
- **Solution**: Restart MySQL service

#### "Questions Not Generating"
- **Run**: `http://localhost/LMS/admin/seed-questions.php`
- **Check**: Student has department and level assigned
- **Verify**: Questions exist for that department

#### "Login Not Working"
- **Clear**: Browser cache and cookies
- **Check**: Username/email and password are correct
- **Verify**: User account is active

#### "Permission Denied Errors"
- **Check**: File permissions on web server
- **Ensure**: Web server can write to directories
- **Solution**: Set appropriate folder permissions

### Debug Mode

To enable detailed error messages, add to `config/database.php`:

```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## 🔒 Security Recommendations

### After Installation

1. **Delete or secure** `importdb.php` file
2. **Change default passwords** for all accounts
3. **Regular backups** of the database
4. **Monitor system logs** for unusual activity
5. **Keep software updated**

### Backup Database

1. **Open phpMyAdmin**: `http://localhost/phpmyadmin`
2. **Select** `lms_database`
3. **Click "Export"**
4. **Download** the SQL file
5. **Store safely** for recovery

## 📞 Support and Maintenance

### Regular Maintenance

- **Weekly**: Check system logs
- **Monthly**: Backup database
- **Quarterly**: Review user accounts
- **Annually**: Update system components

### Getting Help

1. **Check this guide** for common solutions
2. **Review system logs** for error messages
3. **Test with sample accounts** to isolate issues
4. **Contact system administrator** for advanced support

## 🎉 Congratulations!

Your AI-Powered LMS is now installed and ready to use! 

### Next Steps:
1. **Add your students** to the system
2. **Customize questions** for your departments
3. **Train users** on the system
4. **Monitor usage** and gather feedback
5. **Enjoy enhanced learning** with AI-powered quizzes!

---

**Installation Complete** ✅  
**System Ready** 🚀  
**Happy Learning!** 📚
