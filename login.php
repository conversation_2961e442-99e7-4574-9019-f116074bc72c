<?php
/**
 * Main Login Page - Redirects to appropriate login
 * AI-Powered LMS for Ogbonnaya Onu Polytechnic, Aba
 */

// Check if setup is complete
if (!file_exists(__DIR__ . '/.setup_complete')) {
    header('Location: importdb.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AI-Powered LMS</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }

        .login-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .login-content {
            padding: 40px;
        }

        .login-options {
            display: grid;
            gap: 20px;
        }

        .login-option {
            display: block;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            text-align: center;
        }

        .login-option:hover {
            border-color: #28a745;
            background: #f8fff9;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.1);
        }

        .login-option i {
            font-size: 32px;
            color: #28a745;
            margin-bottom: 15px;
        }

        .login-option h3 {
            font-size: 18px;
            margin-bottom: 8px;
        }

        .login-option p {
            color: #666;
            font-size: 14px;
        }

        .system-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            text-align: center;
        }

        .system-info h4 {
            color: #28a745;
            margin-bottom: 10px;
        }

        .system-info p {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-graduation-cap"></i> AI-Powered LMS</h1>
            <p>Ogbonnaya Onu Polytechnic, Aba</p>
        </div>

        <div class="login-content">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">Choose Login Type</h2>
            
            <div class="login-options">
                <a href="admin/login.php" class="login-option">
                    <i class="fas fa-user-shield"></i>
                    <h3>Administrator Login</h3>
                    <p>Access admin panel to manage the system</p>
                </a>

                <a href="student/login.php" class="login-option">
                    <i class="fas fa-user-graduate"></i>
                    <h3>Student Login</h3>
                    <p>Access your learning dashboard and take quizzes</p>
                </a>
            </div>

            <div class="system-info">
                <h4><i class="fas fa-info-circle"></i> System Information</h4>
                <p>LMS successfully installed and configured</p>
                <p>Setup completed: <?php echo file_exists(__DIR__ . '/.setup_complete') ? file_get_contents(__DIR__ . '/.setup_complete') : 'Unknown'; ?></p>
            </div>
        </div>
    </div>
</body>
</html>
