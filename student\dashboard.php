<?php
/**
 * Gamified Learning Dashboard - Knowledge Arena
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    LEFT JOIN departments d ON s.department_id = d.id
    LEFT JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

if (!$student) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Get Quick Game statistics
$quickGameStats = fetchOne("
    SELECT COUNT(*) as total_games,
           COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_games,
           COALESCE(AVG(CASE WHEN status = 'completed' THEN points_earned END), 0) as avg_score,
           COALESCE(MAX(CASE WHEN status = 'completed' THEN points_earned END), 0) as best_score
    FROM game_sessions
    WHERE student_id = :id AND game_mode = 'quick'
", ['id' => $student['id']]);

// Get Endless Mode statistics
$endlessStats = fetchOne("
    SELECT COUNT(*) as total_attempts,
           COALESCE(MAX(points_earned), 0) as best_score,
           COALESCE(MAX(questions_answered), 0) as longest_run
    FROM game_sessions
    WHERE student_id = :id AND game_mode = 'endless' AND status = 'completed'
", ['id' => $student['id']]);

// Get Mission Mode statistics
$missionStats = fetchOne("
    SELECT COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_missions,
           COUNT(*) as total_attempts,
           COALESCE(MAX(level_number), 0) as highest_level,
           COALESCE(SUM(stars_earned), 0) as total_stars
    FROM student_mission_progress smp
    LEFT JOIN mission_levels ml ON smp.level_id = ml.id
    WHERE smp.student_id = :id
", ['id' => $student['id']]);

// Ensure we have valid data with defaults
$gameStats = [
    'quick_games' => isset($quickGameStats['completed_games']) ? (int)$quickGameStats['completed_games'] : 0,
    'quick_best_score' => isset($quickGameStats['best_score']) ? (int)$quickGameStats['best_score'] : 0,
    'endless_attempts' => isset($endlessStats['total_attempts']) ? (int)$endlessStats['total_attempts'] : 0,
    'endless_best_score' => isset($endlessStats['best_score']) ? (int)$endlessStats['best_score'] : 0,
    'endless_longest_run' => isset($endlessStats['longest_run']) ? (int)$endlessStats['longest_run'] : 0,
    'missions_completed' => isset($missionStats['completed_missions']) ? (int)$missionStats['completed_missions'] : 0,
    'missions_highest_level' => isset($missionStats['highest_level']) ? (int)$missionStats['highest_level'] : 0,
    'missions_total_stars' => isset($missionStats['total_stars']) ? (int)$missionStats['total_stars'] : 0
];




$pageTitle = 'Dashboard - Knowledge Arena';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Gaming LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- FontAwesome with fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Fallback for FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        /* Gaming Theme Styling */
        :root {
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --success-color: #00b894;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --info-color: #74b9ff;
            --dark-color: #2d3436;
            --light-color: #ddd6fe;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Inter', sans-serif;
        }

        .sidebar-header h3 {
            color: var(--primary-color);
            font-weight: 800;
        }

        .nav-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);
        }

        /* Game Mode Cards */
        .game-modes-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 40px 0;
        }

        .game-mode-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 450px;
        }

        .game-mode-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .game-mode-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .game-mode-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 35px;
            color: white;
            margin-bottom: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 8px 25px rgba(108, 92, 231, 0.3);
        }

        .game-mode-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 10px;
        }

        .game-mode-description {
            color: #6c757d;
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .game-mode-features {
            list-style: none;
            padding: 0;
            margin-bottom: 25px;
            flex-grow: 1;
        }

        .game-mode-features li {
            padding: 8px 0;
            color: #495057;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .game-mode-features li i {
            color: var(--success-color);
            margin-right: 10px;
            width: 16px;
        }

        .game-mode-button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            width: 100%;
            justify-content: center;
            position: relative;
            z-index: 10;
        }

        .game-mode-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 92, 231, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Best Scores Cards */
        .best-scores-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 40px 0;
        }

        .score-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            border: 3px solid transparent;
        }

        .score-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: var(--card-gradient);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .score-card:hover::before {
            transform: scaleX(1);
        }

        .score-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px var(--card-shadow);
            border-color: var(--card-border);
        }

        .quick-score {
            --card-gradient: linear-gradient(135deg, #667eea, #764ba2);
            --card-shadow: rgba(102, 126, 234, 0.3);
            --card-border: #667eea;
        }

        .endless-score {
            --card-gradient: linear-gradient(135deg, #f093fb, #f5576c);
            --card-shadow: rgba(245, 87, 108, 0.3);
            --card-border: #f5576c;
        }

        .mission-score {
            --card-gradient: linear-gradient(135deg, #4facfe, #00f2fe);
            --card-shadow: rgba(79, 172, 254, 0.3);
            --card-border: #4facfe;
        }

        .score-icon {
            width: 80px;
            height: 80px;
            background: var(--card-gradient);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            color: white;
            font-size: 32px;
            transition: all 0.4s ease;
            box-shadow: 0 10px 25px var(--card-shadow);
        }

        .score-card:hover .score-icon {
            transform: scale(1.15) rotate(10deg);
            box-shadow: 0 15px 35px var(--card-shadow);
        }

        .gaming-stat-icon i {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
            font-style: normal !important;
            font-variant: normal !important;
            text-rendering: auto !important;
            line-height: 1 !important;
        }

        /* FontAwesome Icon Fix - Comprehensive */
        .fas, .far, .fab,
        i.fas, i.far, i.fab,
        [class^="fa-"], [class*=" fa-"] {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
            font-style: normal !important;
            font-variant: normal !important;
            text-rendering: auto !important;
            line-height: 1 !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        .far, i.far {
            font-weight: 400 !important;
        }

        .fab, i.fab {
            font-family: "Font Awesome 6 Brands" !important;
            font-weight: 400 !important;
        }

        /* Ensure icons don't inherit body font */
        * [class^="fa-"]::before,
        * [class*=" fa-"]::before {
            font-family: "Font Awesome 6 Free" !important;
        }

        .score-value {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 15px;
            background: var(--card-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
        }

        .score-label {
            font-size: 1.1rem;
            color: #4a5568;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        @media (max-width: 768px) {
            .best-scores-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .score-value {
                font-size: 2.5rem;
            }

            .score-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }

            .game-modes-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .game-mode-card {
                min-height: auto;
            }
        }

        /* XP Progress Bar */
        .xp-progress {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .xp-progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .xp-progress-bar {
            background: #e9ecef;
            height: 12px;
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .xp-progress-fill {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            height: 100%;
            border-radius: 6px;
            transition: width 0.5s ease;
            position: relative;
        }

        .xp-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Achievement Badges */
        .achievements-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .achievement-badge {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .achievement-badge.unlocked {
            background: linear-gradient(135deg, var(--success-color), #00cec9);
            color: white;
            transform: scale(1.05);
        }

        .achievement-badge.locked {
            background: #f8f9fa;
            color: #6c757d;
            opacity: 0.6;
        }

        .achievement-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .achievement-name {
            font-size: 12px;
            font-weight: 600;
        }

        /* Refresh Button */
        .refresh-btn {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #5a4fcf;
            transform: translateY(-2px);
        }

        .refresh-btn i {
            transition: transform 0.3s ease;
        }

        .refresh-btn:hover i {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Knowledge Arena</h3>
                    <p>Gaming LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="javascript:void(0)" onclick="launchQuickGame()" class="nav-item">
                    <i class="fas fa-bolt"></i>
                    <span>Quick Game</span>
                </a>
                <a href="javascript:void(0)" onclick="launchEndlessMode()" class="nav-item">
                    <i class="fas fa-infinity"></i>
                    <span>Endless Mode</span>
                </a>
                <a href="javascript:void(0)" onclick="launchMissionMode()" class="nav-item">
                    <i class="fas fa-map"></i>
                    <span>Mission Mode</span>
                </a>

                <a href="profile.php" class="nav-item">
                    <i class="fas fa-user-circle"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
                <a href="../index.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Go Back to Site</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-ninja"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['department_name']); ?></p>
                        <p><?php echo htmlspecialchars($student['level_name']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>🎮 Welcome Back, <?php echo htmlspecialchars($student['first_name']); ?>!</h1>
                    <p>Choose your learning adventure: Quick challenges, endless practice, or mission-based progression!</p>
                </div>
                <div class="header-right">
                    <button onclick="location.reload()" class="refresh-btn" title="Refresh Statistics">
                        <i class="fas fa-sync-alt"></i>
                        Refresh Stats
                    </button>
                </div>
            </header>

            <!-- Best Scores Statistics -->
            <div class="best-scores-grid">
                <div class="score-card quick-score">
                    <div class="score-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="score-value"><?php echo number_format($gameStats['quick_best_score']); ?></div>
                    <div class="score-label">Quick Game Best Score</div>
                </div>

                <div class="score-card endless-score">
                    <div class="score-icon">
                        <i class="fas fa-infinity"></i>
                    </div>
                    <div class="score-value"><?php echo number_format($gameStats['endless_best_score']); ?></div>
                    <div class="score-label">Endless Mode Best Score</div>
                </div>

                <div class="score-card mission-score">
                    <div class="score-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="score-value"><?php echo number_format($gameStats['missions_total_stars']); ?></div>
                    <div class="score-label">Mission Stars Earned</div>
                </div>
            </div>

            <!-- Game Modes -->
            <div class="game-modes-grid">
                <!-- Quick Game Mode -->
                <div class="game-mode-card quick-game-card">
                    <div class="game-mode-icon">
                        <div class="icon-wrapper quick">
                            <i class="fas fa-bolt"></i>
                        </div>
                    </div>
                    <h3 class="game-mode-title">⚡ Quick Game</h3>
                    <p class="game-mode-description">Choose your difficulty and number of questions for a quick knowledge challenge!</p>
                    <ul class="game-mode-features">
                        <li><i class="fas fa-check-circle"></i> Select difficulty level</li>
                        <li><i class="fas fa-check-circle"></i> Choose question count</li>
                        <li><i class="fas fa-check-circle"></i> Instant results & XP</li>
                        <li><i class="fas fa-check-circle"></i> Perfect for quick practice</li>
                    </ul>
                    <button class="game-mode-button quick" onclick="launchQuickGame()" type="button">
                        <i class="fas fa-play"></i>
                        Start Quick Game
                    </button>
                </div>

                <!-- Endless Mode -->
                <div class="game-mode-card endless-mode-card">
                    <div class="game-mode-icon">
                        <div class="icon-wrapper endless">
                            <i class="fas fa-infinity"></i>
                        </div>
                    </div>
                    <h3 class="game-mode-title">♾️ Endless Mode</h3>
                    <p class="game-mode-description">Test your limits with infinite questions! Keep going until you run out of lives.</p>
                    <ul class="game-mode-features">
                        <li><i class="fas fa-check-circle"></i> Infinite questions</li>
                        <li><i class="fas fa-check-circle"></i> 3 lives system</li>
                        <li><i class="fas fa-check-circle"></i> Increasing difficulty</li>
                        <li><i class="fas fa-check-circle"></i> High score tracking</li>
                    </ul>
                    <button class="game-mode-button endless" onclick="launchEndlessMode()" type="button">
                        <i class="fas fa-rocket"></i>
                        Enter Endless Mode
                    </button>
                </div>

                <!-- Mission Mode -->
                <div class="game-mode-card mission-mode-card">
                    <div class="game-mode-icon">
                        <div class="icon-wrapper mission">
                            <i class="fas fa-map"></i>
                        </div>
                    </div>
                    <h3 class="game-mode-title">🗺️ Mission Mode</h3>
                    <p class="game-mode-description">Embark on an adventure through knowledge maps! Unlock new levels and earn rewards.</p>
                    <ul class="game-mode-features">
                        <li><i class="fas fa-check-circle"></i> Map-based progression</li>
                        <li><i class="fas fa-check-circle"></i> Unlock new levels</li>
                        <li><i class="fas fa-check-circle"></i> Earn coins & rewards</li>
                        <li><i class="fas fa-check-circle"></i> Increasing difficulty</li>
                    </ul>
                    <button class="game-mode-button mission" onclick="launchMissionMode()" type="button">
                        <i class="fas fa-compass"></i>
                        Start Mission
                    </button>
                </div>
            </div>




        </main>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(108, 92, 231, 0.9); z-index: 9999; align-items: center; justify-content: center; flex-direction: column;">
        <div style="color: white; text-align: center;">
            <i class="fas fa-gamepad fa-3x fa-spin" style="margin-bottom: 20px;"></i>
            <h3>Loading Game...</h3>
            <p>Preparing your gaming experience!</p>
        </div>
    </div>


    <script>
        // Gaming dashboard specific functions
        function showLoadingAndRedirect(url) {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'flex';

                // Simulate loading time for better UX
                setTimeout(() => {
                    window.location.href = url;
                }, 1500);
            } else {
                // Fallback if overlay doesn't exist
                window.location.href = url;
            }
        }

        // Combined DOMContentLoaded event listener
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure loading overlay is hidden
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }

            // XP Animation on page load
            const xpFill = document.querySelector('.xp-progress-fill');
            if (xpFill) {
                const targetWidth = xpFill.style.width;
                xpFill.style.width = '0%';
                setTimeout(() => {
                    xpFill.style.width = targetWidth;
                }, 500);
            }

            // Animate score values
            const statValues = document.querySelectorAll('.score-value');
            statValues.forEach((stat, index) => {
                const finalValue = parseInt(stat.textContent.replace(/,/g, ''));
                stat.textContent = '0';

                setTimeout(() => {
                    animateNumber(stat, 0, finalValue, 1000);
                }, index * 200);
            });
        });

        function animateNumber(element, start, end, duration) {
            const range = end - start;
            const increment = range / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= end) {
                    current = end;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 16);
        }

        // Achievement unlock animation
        function unlockAchievement(achievementName) {
            // This would be called when an achievement is unlocked
            const achievement = document.querySelector(`[data-achievement="${achievementName}"]`);
            if (achievement) {
                achievement.classList.add('unlocked');
                achievement.style.animation = 'bounce 0.6s ease-in-out';
            }
        }

        // Enhanced Game Launch Functions with Professional Animations
        function launchQuickGame() {
            showEnhancedLoadingScreen({
                title: 'Quick Game',
                message: 'Preparing your lightning-fast challenge...',
                icon: 'fas fa-bolt',
                color: '#6c5ce7',
                particles: 'lightning',
                duration: 2500
            }, () => {
                window.location.href = 'quick-game-setup.php';
            });
        }

        function launchEndlessMode() {
            showEnhancedLoadingScreen({
                title: 'Endless Mode',
                message: 'Entering the infinite knowledge realm...',
                icon: 'fas fa-infinity',
                color: '#0984e3',
                particles: 'stars',
                duration: 2800
            }, () => {
                window.location.href = 'endless-game.php';
            });
        }

        function launchMissionMode() {
            showEnhancedLoadingScreen({
                title: 'Mission Mode',
                message: 'Loading your adventure map...',
                icon: 'fas fa-map',
                color: '#e17055',
                particles: 'compass',
                duration: 2600
            }, () => {
                window.location.href = 'mission-mode.php';
            });
        }

        // Enhanced Professional Loading Screen Function
        function showEnhancedLoadingScreen(config, callback) {
            // Create enhanced loading overlay
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'enhanced-loading-overlay';
            loadingOverlay.style.background = `linear-gradient(135deg, ${config.color}dd, ${config.color}aa)`;

            loadingOverlay.innerHTML = `
                <div class="loading-particles" id="loadingParticles"></div>
                <div class="enhanced-loading-content">
                    <div class="loading-logo">
                        <div class="logo-circle" style="border-color: ${config.color};">
                            <i class="${config.icon} loading-main-icon"></i>
                        </div>
                        <div class="logo-pulse" style="border-color: ${config.color};"></div>
                    </div>
                    <h2 class="loading-title">${config.title}</h2>
                    <p class="loading-message">${config.message}</p>
                    <div class="loading-progress-container">
                        <div class="loading-progress-bar">
                            <div class="loading-progress-fill" style="background: ${config.color};"></div>
                        </div>
                        <div class="loading-percentage">0%</div>
                    </div>
                    <div class="loading-dots">
                        <span></span><span></span><span></span>
                    </div>
                </div>
            `;

            document.body.appendChild(loadingOverlay);

            // Create particles based on type
            createLoadingParticles(config.particles, config.color);

            // Animate entrance with sound effect simulation
            setTimeout(() => {
                loadingOverlay.classList.add('show');

                // Create visual "sound wave" effect
                createSoundWaveEffect(loadingOverlay, config.color);
            }, 50);

            // Animate progress bar
            const progressBar = loadingOverlay.querySelector('.loading-progress-fill');
            const percentage = loadingOverlay.querySelector('.loading-percentage');
            let progress = 0;

            const progressInterval = setInterval(() => {
                progress += Math.random() * 8 + 2;
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';
                percentage.textContent = Math.floor(progress) + '%';

                if (progress >= 100) {
                    clearInterval(progressInterval);

                    // Complete animation with screen effect
                    setTimeout(() => {
                        loadingOverlay.classList.add('complete');

                        // Add subtle screen shake effect
                        document.body.style.animation = 'screenShake 0.3s ease-in-out';

                        setTimeout(() => {
                            document.body.style.animation = '';
                            document.body.removeChild(loadingOverlay);
                            if (callback) callback();
                        }, 500);
                    }, 300);
                }
            }, 80);
        }

        // Create animated particles for loading screen
        function createLoadingParticles(type, color) {
            const container = document.getElementById('loadingParticles');
            const particleCount = 25;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = `particle particle-${type}`;

                // Random positioning and movement
                const startX = Math.random() * 100;
                const startY = Math.random() * 100;
                const endX = Math.random() * 100;
                const endY = Math.random() * 100;

                particle.style.left = startX + '%';
                particle.style.top = startY + '%';
                particle.style.animationDelay = Math.random() * 2 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 3) + 's';

                // Create particle content based on type
                if (type === 'lightning') {
                    const icons = ['⚡', '🔥', '💫', '⭐'];
                    particle.innerHTML = icons[Math.floor(Math.random() * icons.length)];
                    particle.style.color = color;
                    particle.style.fontSize = (Math.random() * 10 + 15) + 'px';
                } else if (type === 'stars') {
                    const icons = ['✨', '⭐', '🌟', '💫', '🔮'];
                    particle.innerHTML = icons[Math.floor(Math.random() * icons.length)];
                    particle.style.color = '#fff';
                    particle.style.fontSize = (Math.random() * 8 + 12) + 'px';
                } else if (type === 'compass') {
                    const icons = ['🧭', '🗺️', '🏔️', '🎯', '🚀'];
                    particle.innerHTML = icons[Math.floor(Math.random() * icons.length)];
                    particle.style.fontSize = (Math.random() * 8 + 14) + 'px';
                }

                // Add custom CSS variables for animation
                particle.style.setProperty('--end-x', endX + '%');
                particle.style.setProperty('--end-y', endY + '%');

                container.appendChild(particle);

                // Remove particle after animation
                setTimeout(() => {
                    if (container.contains(particle)) {
                        container.removeChild(particle);
                    }
                }, 8000);
            }
        }

        // Add button click effects
        function addButtonClickEffect(button) {
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = '';
            }, 150);
        }

        // Create visual sound wave effect
        function createSoundWaveEffect(container, color) {
            const waveContainer = document.createElement('div');
            waveContainer.className = 'sound-waves';
            waveContainer.innerHTML = `
                <div class="sound-wave" style="border-color: ${color};"></div>
                <div class="sound-wave" style="border-color: ${color}; animation-delay: 0.2s;"></div>
                <div class="sound-wave" style="border-color: ${color}; animation-delay: 0.4s;"></div>
            `;
            container.appendChild(waveContainer);

            // Remove after animation
            setTimeout(() => {
                if (container.contains(waveContainer)) {
                    container.removeChild(waveContainer);
                }
            }, 2000);
        }

        // Enhanced button interactions
        document.addEventListener('DOMContentLoaded', function() {
            const gameButtons = document.querySelectorAll('.game-mode-button');
            gameButtons.forEach(button => {
                button.addEventListener('click', function() {
                    addButtonClickEffect(this);
                });
            });
        });

        // Add some CSS animations and styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes bounce {
                0%, 20%, 60%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                80% { transform: translateY(-5px); }
            }

            .gaming-stat-card:hover {
                animation: pulse 0.3s ease-in-out;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            /* Enhanced Game Mode Cards */
            .game-mode-card {
                transition: all 0.3s ease;
            }

            .game-mode-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            }

            .icon-wrapper {
                width: 60px;
                height: 60px;
                border-radius: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 15px;
                font-size: 24px;
                color: white;
                transition: all 0.3s ease;
            }

            .icon-wrapper.quick {
                background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            }

            .icon-wrapper.endless {
                background: linear-gradient(135deg, #0984e3, #74b9ff);
            }

            .icon-wrapper.mission {
                background: linear-gradient(135deg, #e17055, #fdcb6e);
            }

            .game-mode-card:hover .icon-wrapper {
                transform: scale(1.1) rotate(5deg);
            }

            .game-mode-features li i {
                color: #00b894;
                margin-right: 8px;
            }

            .game-mode-button {
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .game-mode-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }

            .game-mode-button.quick {
                background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            }

            .game-mode-button.endless {
                background: linear-gradient(135deg, #0984e3, #74b9ff);
            }

            .game-mode-button.mission {
                background: linear-gradient(135deg, #e17055, #fdcb6e);
            }

            /* Enhanced Professional Loading Screen Styles */
            .enhanced-loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #6c5ce7dd, #6c5ce7aa);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                opacity: 0;
                transform: scale(1.1);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
            }

            .enhanced-loading-overlay.show {
                opacity: 1;
                transform: scale(1);
            }

            .enhanced-loading-overlay.complete {
                opacity: 0;
                transform: scale(0.9);
            }

            .loading-particles {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                overflow: hidden;
            }

            .particle {
                position: absolute;
                font-size: 20px;
                animation: particleFloat 6s ease-in-out infinite;
                opacity: 0.8;
                pointer-events: none;
                text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            }

            .particle-lightning {
                animation: lightningFloat 4s ease-in-out infinite;
            }

            .particle-stars {
                animation: starFloat 5s ease-in-out infinite;
            }

            .particle-compass {
                animation: compassFloat 6s ease-in-out infinite;
            }

            .enhanced-loading-content {
                text-align: center;
                color: white;
                max-width: 400px;
                position: relative;
                z-index: 2;
            }

            .loading-logo {
                position: relative;
                margin-bottom: 30px;
                display: inline-block;
            }

            .logo-circle {
                width: 80px;
                height: 80px;
                border: 3px solid #6c5ce7;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                position: relative;
                z-index: 2;
                animation: logoSpin 3s linear infinite;
            }

            .loading-main-icon {
                font-size: 32px;
                color: white;
                animation: iconPulse 2s ease-in-out infinite;
            }

            .logo-pulse {
                position: absolute;
                top: -10px;
                left: -10px;
                right: -10px;
                bottom: -10px;
                border: 2px solid #6c5ce7;
                border-radius: 50%;
                animation: logoPulse 2s ease-in-out infinite;
                opacity: 0.6;
            }

            .loading-title {
                font-size: 28px;
                font-weight: 700;
                margin-bottom: 10px;
                text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
                animation: titleGlow 2s ease-in-out infinite alternate;
            }

            .loading-message {
                font-size: 16px;
                margin-bottom: 30px;
                opacity: 0.9;
                font-weight: 400;
                animation: messageFloat 3s ease-in-out infinite;
            }

            .loading-progress-container {
                margin-bottom: 20px;
            }

            .loading-progress-bar {
                width: 100%;
                height: 6px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                overflow: hidden;
                margin-bottom: 10px;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .loading-progress-fill {
                height: 100%;
                background: #6c5ce7;
                border-radius: 10px;
                transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                width: 0%;
                box-shadow: 0 0 10px rgba(108, 92, 231, 0.5);
                position: relative;
            }

            .loading-progress-fill::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
                animation: progressShine 1.5s ease-in-out infinite;
            }

            .loading-percentage {
                font-size: 14px;
                font-weight: 600;
                opacity: 0.8;
            }

            .loading-dots {
                display: flex;
                justify-content: center;
                gap: 8px;
            }

            .loading-dots span {
                width: 8px;
                height: 8px;
                background: white;
                border-radius: 50%;
                animation: dotBounce 1.4s ease-in-out infinite both;
            }

            .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
            .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
            .loading-dots span:nth-child(3) { animation-delay: 0s; }

            /* Sound Wave Effect */
            .sound-waves {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                pointer-events: none;
            }

            .sound-wave {
                position: absolute;
                top: 50%;
                left: 50%;
                width: 100px;
                height: 100px;
                border: 2px solid #6c5ce7;
                border-radius: 50%;
                transform: translate(-50%, -50%);
                animation: soundWaveExpand 2s ease-out;
                opacity: 0;
            }

            /* Enhanced Loading Animations */
            @keyframes particleFloat {
                0% {
                    transform: translate(0, 0) rotate(0deg) scale(0.8);
                    opacity: 0;
                }
                10% {
                    opacity: 0.8;
                }
                50% {
                    transform: translate(var(--end-x, 50%), var(--end-y, -50%)) rotate(180deg) scale(1.2);
                    opacity: 1;
                }
                90% {
                    opacity: 0.6;
                }
                100% {
                    transform: translate(var(--end-x, 100%), var(--end-y, -100%)) rotate(360deg) scale(0.5);
                    opacity: 0;
                }
            }

            @keyframes lightningFloat {
                0%, 100% {
                    transform: translateY(0px) rotate(0deg) scale(1);
                    opacity: 0.6;
                    filter: brightness(1);
                }
                25% {
                    transform: translateY(-30px) rotate(90deg) scale(1.3);
                    opacity: 1;
                    filter: brightness(1.5);
                }
                50% {
                    transform: translateY(-60px) rotate(180deg) scale(0.8);
                    opacity: 0.8;
                    filter: brightness(1.2);
                }
                75% {
                    transform: translateY(-30px) rotate(270deg) scale(1.1);
                    opacity: 0.9;
                    filter: brightness(1.3);
                }
            }

            @keyframes starFloat {
                0%, 100% {
                    transform: translateY(0px) rotate(0deg) scale(0.8);
                    opacity: 0.5;
                }
                33% {
                    transform: translateY(-40px) rotate(120deg) scale(1.2);
                    opacity: 1;
                }
                66% {
                    transform: translateY(-20px) rotate(240deg) scale(1);
                    opacity: 0.8;
                }
            }

            @keyframes compassFloat {
                0%, 100% {
                    transform: translateX(0px) rotate(0deg) scale(1);
                    opacity: 0.7;
                }
                25% {
                    transform: translateX(30px) rotate(90deg) scale(1.1);
                    opacity: 0.9;
                }
                50% {
                    transform: translateX(0px) rotate(180deg) scale(0.9);
                    opacity: 1;
                }
                75% {
                    transform: translateX(-30px) rotate(270deg) scale(1.1);
                    opacity: 0.8;
                }
            }

            @keyframes logoSpin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            @keyframes logoPulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 0.6;
                }
                50% {
                    transform: scale(1.1);
                    opacity: 0.3;
                }
            }

            @keyframes iconPulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 1;
                }
                50% {
                    transform: scale(1.1);
                    opacity: 0.8;
                }
            }

            @keyframes titleGlow {
                0% { text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); }
                100% { text-shadow: 0 2px 20px rgba(255, 255, 255, 0.3); }
            }

            @keyframes messageFloat {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-5px); }
            }

            @keyframes progressShine {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }

            @keyframes dotBounce {
                0%, 80%, 100% {
                    transform: scale(0);
                    opacity: 0.5;
                }
                40% {
                    transform: scale(1);
                    opacity: 1;
                }
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            @keyframes screenShake {
                0%, 100% { transform: translateX(0); }
                10% { transform: translateX(-2px); }
                20% { transform: translateX(2px); }
                30% { transform: translateX(-2px); }
                40% { transform: translateX(2px); }
                50% { transform: translateX(-1px); }
                60% { transform: translateX(1px); }
                70% { transform: translateX(-1px); }
                80% { transform: translateX(1px); }
                90% { transform: translateX(0); }
            }

            @keyframes soundWaveExpand {
                0% {
                    transform: translate(-50%, -50%) scale(0.5);
                    opacity: 0.8;
                }
                50% {
                    opacity: 0.4;
                }
                100% {
                    transform: translate(-50%, -50%) scale(3);
                    opacity: 0;
                }
            }

            /* Additional Gaming UI Enhancements */
            .gaming-stats-grid {
                animation: slideInUp 0.6s ease-out;
            }

            .game-modes-grid {
                animation: slideInUp 0.8s ease-out;
            }

            .achievements-section {
                animation: slideInUp 1s ease-out;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* Sidebar enhancements */
            .nav-item {
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .nav-item::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
                transition: left 0.5s ease;
            }

            .nav-item:hover::before {
                left: 100%;
            }

            .nav-item:hover {
                transform: translateX(5px);
                background: rgba(255,255,255,0.1);
            }

            /* Gaming stat cards glow effect */
            .gaming-stat-card {
                position: relative;
                overflow: hidden;
            }

            .gaming-stat-card::after {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
                transform: rotate(45deg);
                transition: all 0.6s ease;
                opacity: 0;
            }

            .gaming-stat-card:hover::after {
                animation: shine 0.6s ease-in-out;
            }

            @keyframes shine {
                0% {
                    opacity: 0;
                    transform: rotate(45deg) translateX(-100%);
                }
                50% {
                    opacity: 1;
                }
                100% {
                    opacity: 0;
                    transform: rotate(45deg) translateX(100%);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
