/**
 * Registration JavaScript for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeRegistration();

    // Aggressively hide the "correct errors" message
    setInterval(() => {
        const errorAlerts = document.querySelectorAll('.alert-error');
        errorAlerts.forEach(alert => {
            if (alert.textContent.includes('Please correct the errors in the form before submitting')) {
                alert.style.display = 'none';
                alert.remove();
            }
        });
    }, 500);
});

function initializeRegistration() {
    const form = document.querySelector('.register-form');
    const departmentSelect = document.getElementById('department_id');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');

    // Reset any stuck loading states
    const submitBtn = document.querySelector('.btn-register');
    if (submitBtn && submitBtn.disabled) {
        setButtonLoading(submitBtn, false);
    }

    // Clear any existing error alerts and prevent new ones
    const errorAlerts = document.querySelectorAll('.alert-error');
    errorAlerts.forEach(alert => {
        // Only remove JavaScript-generated errors, not PHP errors
        if (!alert.textContent.includes('Please fill in all required fields') &&
            !alert.textContent.includes('already exists') &&
            !alert.textContent.includes('Password') &&
            !alert.textContent.includes('email')) {
            alert.remove();
        }
    });

    // Hide the specific "correct errors" message
    setTimeout(() => {
        const errorAlerts = document.querySelectorAll('.alert-error');
        errorAlerts.forEach(alert => {
            if (alert.textContent.includes('Please correct the errors in the form before submitting')) {
                alert.style.display = 'none';
            }
        });
    }, 100);
    
    // Completely remove JavaScript form handling
    if (form) {
        console.log('Form found, but no JavaScript handling - pure PHP submission');
    }
    
    // Matriculation number input handler
    const matriculationInput = document.getElementById('matriculation_no');
    if (matriculationInput) {
        matriculationInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
            validateMatriculationNumber();
        });

        matriculationInput.addEventListener('blur', validateMatriculationNumber);
    }
    
    // Password strength checker
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            validatePasswordMatch();
        });
    }
    
    // Confirm password validation
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validatePasswordMatch);
    }
    
    // Security questions validation
    initializeSecurityQuestions();
    
    // Add real-time validation to all inputs
    addRealTimeValidation();
    
    // Initialize form validation
}

function validateMatriculationNumber() {
    const matriculationInput = document.getElementById('matriculation_no');

    if (matriculationInput) {
        const value = matriculationInput.value.trim();

        if (value.length > 0 && value.length < 6) {
            matriculationInput.style.borderColor = '#f59e0b';
            matriculationInput.style.backgroundColor = '#fffbeb';
        } else {
            matriculationInput.style.borderColor = '';
            matriculationInput.style.backgroundColor = '';
        }
    }
}

function checkPasswordStrength(password) {
    const strengthIndicator = document.getElementById('password-strength');
    if (!strengthIndicator) return;
    
    let strength = 0;
    let feedback = '';
    
    // Length checks
    if (password.length >= 8) strength += 1;
    if (password.length >= 12) strength += 1;
    
    // Character variety checks
    if (/[a-z]/.test(password)) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    
    // Remove existing classes
    strengthIndicator.classList.remove('weak', 'medium', 'strong');
    
    if (password.length === 0) {
        strengthIndicator.style.width = '0%';
        strengthIndicator.style.background = '#e5e7eb';
        return;
    }
    
    if (strength <= 2) {
        strengthIndicator.classList.add('weak');
        feedback = 'Weak password - add more characters and variety';
    } else if (strength <= 4) {
        strengthIndicator.classList.add('medium');
        feedback = 'Medium strength - consider adding special characters';
    } else {
        strengthIndicator.classList.add('strong');
        feedback = 'Strong password!';
    }
    
    strengthIndicator.setAttribute('title', feedback);
}

function validatePasswordMatch() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    if (!password || !confirmPassword) return;
    
    const isMatch = password.value === confirmPassword.value;
    const isEmpty = confirmPassword.value === '';
    
    clearFieldValidation(confirmPassword);
    
    if (!isEmpty) {
        if (isMatch) {
            setFieldValid(confirmPassword, 'Passwords match');
        } else {
            setFieldInvalid(confirmPassword, 'Passwords do not match');
        }
    }
}

function initializeSecurityQuestions() {
    const questionSelects = [
        document.getElementById('security_question_1'),
        document.getElementById('security_question_2'),
        document.getElementById('security_question_3')
    ];
    
    questionSelects.forEach((select, index) => {
        if (select) {
            select.addEventListener('change', function() {
                validateSecurityQuestions();
            });
        }
    });
    
    // Add validation to security answers
    for (let i = 1; i <= 3; i++) {
        const answerInput = document.getElementById(`security_answer_${i}`);
        if (answerInput) {
            answerInput.addEventListener('input', function() {
                validateSecurityAnswer(this, i);
            });
        }
    }
}

function validateSecurityQuestions() {
    const questionSelects = [
        document.getElementById('security_question_1'),
        document.getElementById('security_question_2'),
        document.getElementById('security_question_3')
    ];
    
    const selectedValues = questionSelects.map(select => select ? select.value : '');
    const uniqueValues = [...new Set(selectedValues.filter(val => val !== ''))];
    
    questionSelects.forEach((select, index) => {
        if (select && select.value) {
            clearFieldValidation(select);
            
            const duplicateCount = selectedValues.filter(val => val === select.value).length;
            if (duplicateCount > 1) {
                setFieldInvalid(select, 'Please select different questions');
            } else {
                setFieldValid(select);
            }
        }
    });
    
    return uniqueValues.length === 3;
}

function validateSecurityAnswer(input, questionNumber) {
    const answer = input.value.trim();
    
    clearFieldValidation(input);
    
    if (answer.length === 0) {
        return;
    } else if (answer.length < 2) {
        setFieldInvalid(input, 'Answer too short');
    } else {
        setFieldValid(input);
    }
}

function addRealTimeValidation() {
    // Email validation
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            validateEmail(this);
        });
    }
    
    // Name validation
    const firstNameInput = document.getElementById('first_name');
    const lastNameInput = document.getElementById('last_name');
    
    [firstNameInput, lastNameInput].forEach(input => {
        if (input) {
            input.addEventListener('blur', function() {
                validateName(this);
            });
        }
    });
    
    // Phone validation
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('blur', function() {
            validatePhone(this);
        });
    }
}

function validateEmail(input) {
    const email = input.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    clearFieldValidation(input);
    
    if (email === '') {
        return;
    } else if (!emailRegex.test(email)) {
        setFieldInvalid(input, 'Please enter a valid email address');
    } else {
        setFieldValid(input);
    }
}

function validateName(input) {
    const name = input.value.trim();
    
    clearFieldValidation(input);
    
    if (name === '') {
        return;
    } else if (name.length < 2) {
        setFieldInvalid(input, 'Name must be at least 2 characters');
    } else if (!/^[a-zA-Z\s'-]+$/.test(name)) {
        setFieldInvalid(input, 'Name contains invalid characters');
    } else {
        setFieldValid(input);
    }
}

function validatePhone(input) {
    const phone = input.value.trim();
    
    clearFieldValidation(input);
    
    if (phone === '') {
        return; // Phone is optional
    } else if (!/^[\d\s\-\+\(\)]+$/.test(phone)) {
        setFieldInvalid(input, 'Please enter a valid phone number');
    } else if (phone.replace(/\D/g, '').length < 10) {
        setFieldInvalid(input, 'Phone number too short');
    } else {
        setFieldValid(input);
    }
}

function setFieldValid(field, message = '') {
    field.classList.remove('invalid');
    field.classList.add('valid');
    
    let validationMsg = field.parentNode.querySelector('.validation-message');
    if (validationMsg) {
        validationMsg.remove();
    }
    
    if (message) {
        validationMsg = document.createElement('div');
        validationMsg.className = 'validation-message success';
        validationMsg.textContent = message;
        field.parentNode.appendChild(validationMsg);
    }
}

function setFieldInvalid(field, message) {
    field.classList.remove('valid');
    field.classList.add('invalid');
    
    let validationMsg = field.parentNode.querySelector('.validation-message');
    if (validationMsg) {
        validationMsg.remove();
    }
    
    validationMsg = document.createElement('div');
    validationMsg.className = 'validation-message error';
    validationMsg.textContent = message;
    field.parentNode.appendChild(validationMsg);
}

function clearFieldValidation(field) {
    field.classList.remove('valid', 'invalid');
    
    const validationMsg = field.parentNode.querySelector('.validation-message');
    if (validationMsg) {
        validationMsg.remove();
    }
}

// Progress functions completely removed

function handleFormSubmission(e) {
    // Remove all JavaScript validation - let PHP handle everything
    console.log('Form submitted - PHP will handle all validation');

    // Show loading state immediately
    const submitBtn = document.querySelector('.btn-register');
    if (submitBtn) {
        setButtonLoading(submitBtn, true);

        // Add timeout to reset button if form takes too long
        setTimeout(() => {
            if (submitBtn.disabled) {
                setButtonLoading(submitBtn, false);
                showError('Registration is taking longer than expected. Please try again.');
            }
        }, 15000); // 15 second timeout
    }

    // Allow form to submit normally - no preventDefault
    return true;
}

function validateRegistrationForm(e) {
    // Disabled - PHP handles all validation now
    console.log('JavaScript validation disabled - PHP will handle validation');
    return true;
}

function showError(message) {
    // Disabled - no more JavaScript error messages
    console.log('Error message blocked:', message);
}

function setButtonLoading(button, isLoading) {
    if (isLoading) {
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
        button.disabled = true;
        button.style.opacity = '0.8';

        // Add visual feedback to the form
        const form = document.querySelector('.register-form');
        if (form) {
            form.style.opacity = '0.7';
            form.style.pointerEvents = 'none';
        }
    } else {
        button.innerHTML = button.dataset.originalText || 'Create Account';
        button.disabled = false;
        button.style.opacity = '1';

        // Remove visual feedback from the form
        const form = document.querySelector('.register-form');
        if (form) {
            form.style.opacity = '1';
            form.style.pointerEvents = 'auto';
        }
    }
}
