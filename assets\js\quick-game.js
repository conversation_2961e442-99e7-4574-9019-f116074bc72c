/**
 * Quick Game JavaScript for AI-Powered LMS
 * Handles game logic, question generation, and scoring
 */

// Game state variables
let currentQuestion = 0;
let score = 0;
let correctAnswers = 0;
let selectedAnswer = null;
let questions = [];
let gameStartTime = null;
let questionStartTime = null;
let questionTimer = null;
let timePerQuestion = 30; // seconds
let isPaused = false;

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeGame();
});

async function initializeGame() {
    try {
        // Show loading screen
        showLoadingScreen();
        
        // Generate questions using AI
        await generateQuestions();
        
        // Start the game
        startGame();
        
    } catch (error) {
        console.error('Failed to initialize game:', error);
        showError('Failed to load game. Please try again.');
    }
}

function showLoadingScreen() {
    const loadingBar = document.getElementById('loading-bar');
    let progress = 0;
    
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;
        
        loadingBar.style.width = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
        }
    }, 200);
}

async function generateQuestions() {
    try {
        const response = await fetch('../api/generate-game-questions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: gameConfig.sessionId,
                difficulty: gameConfig.difficulty,
                question_count: gameConfig.totalQuestions,
                department_id: gameConfig.departmentId,
                academic_level_id: gameConfig.academicLevelId
            })
        });

        const data = await response.json();

        if (data.success && data.questions && data.questions.length > 0) {
            questions = data.questions;
        } else {
            throw new Error(data.message || 'No questions received from API');
        }

    } catch (error) {
        console.error('Error generating questions:', error);
        // Fallback to sample questions for demo
        questions = generateSampleQuestions();
    }
}

function generateSampleQuestions() {
    const sampleQuestions = [];
    const topics = ['Mathematics', 'Science', 'History', 'Geography', 'Literature'];
    
    for (let i = 0; i < gameConfig.totalQuestions; i++) {
        const topic = topics[Math.floor(Math.random() * topics.length)];
        sampleQuestions.push({
            id: i + 1,
            question: `Sample ${topic} question ${i + 1}. What is the correct answer?`,
            option_a: 'Option A - First choice',
            option_b: 'Option B - Second choice',
            option_c: 'Option C - Third choice',
            option_d: 'Option D - Fourth choice',
            correct_answer: ['A', 'B', 'C', 'D'][Math.floor(Math.random() * 4)],
            difficulty: gameConfig.difficulty
        });
    }
    
    return sampleQuestions;
}

function startGame() {
    // Hide loading screen and show question container
    document.getElementById('loading-screen').style.display = 'none';
    document.getElementById('question-container').style.display = 'block';
    
    // Initialize game state
    gameStartTime = Date.now();
    currentQuestion = 0;
    score = 0;
    correctAnswers = 0;
    
    // Load first question
    loadQuestion();
}

function loadQuestion() {
    if (currentQuestion >= questions.length) {
        endGame();
        return;
    }
    
    const question = questions[currentQuestion];
    selectedAnswer = null;
    questionStartTime = Date.now();
    
    // Update UI
    document.getElementById('current-question').textContent = currentQuestion + 1;
    document.getElementById('question-num').textContent = currentQuestion + 1;
    document.getElementById('question-text').textContent = question.question;
    document.getElementById('option-a').textContent = question.option_a;
    document.getElementById('option-b').textContent = question.option_b;
    document.getElementById('option-c').textContent = question.option_c;
    document.getElementById('option-d').textContent = question.option_d;
    
    // Reset selected answer
    selectedAnswer = null;

    // Reset option buttons
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
        btn.classList.remove('selected', 'correct', 'incorrect');
        btn.disabled = false;
        btn.style.transform = 'scale(1)'; // Reset any scaling
        btn.style.backgroundColor = ''; // Reset background color
        btn.style.color = ''; // Reset text color
        btn.style.borderColor = ''; // Reset border color
        btn.style.boxShadow = ''; // Reset box shadow
    });

    // Disable submit button and remove pulse animation
    const submitBtn = document.getElementById('submit-btn');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.classList.remove('pulse-animation');
    }
    
    // Update progress bar
    const progress = ((currentQuestion) / gameConfig.totalQuestions) * 100;
    document.getElementById('progress-fill').style.width = progress + '%';
    
    // Start question timer
    startQuestionTimer();
    
    // Add entrance animation
    const questionCard = document.querySelector('.question-card');
    questionCard.classList.remove('animate__fadeInUp');
    questionCard.classList.add('animate__fadeInUp');
}

function startQuestionTimer() {
    let timeLeft = timePerQuestion;
    document.getElementById('question-timer').textContent = timeLeft;
    document.getElementById('timer').textContent = timeLeft;

    questionTimer = setInterval(() => {
        // Don't update timer if game is paused
        if (isPaused) {
            return;
        }

        timeLeft--;
        document.getElementById('question-timer').textContent = timeLeft;
        document.getElementById('timer').textContent = timeLeft;

        // Add warning animation when time is low
        if (timeLeft <= 10) {
            document.getElementById('timer').classList.add('timer-warning');
            document.getElementById('question-timer').classList.add('timer-warning');
        }

        if (timeLeft <= 0) {
            clearInterval(questionTimer);
            showTimeExceededModal();
        }
    }, 1000);
}

function selectAnswer(option) {
    // Don't allow selection if game is paused
    if (isPaused) {
        return;
    }

    // Don't allow selection if answer has already been submitted
    if (document.getElementById('submit-btn').disabled === false && selectedAnswer !== null && selectedAnswer !== option) {
        // This is a change of selection, which should be allowed
    }

    // Allow changing selection - users can pick different answers
    selectedAnswer = option;

    // Update UI - remove selected class from all buttons first
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
        btn.classList.remove('selected');
        btn.style.transform = 'scale(1)'; // Reset any scaling
        btn.style.backgroundColor = ''; // Reset background color
        btn.style.color = ''; // Reset text color
        btn.style.borderColor = ''; // Reset border color
        btn.style.boxShadow = ''; // Reset box shadow
    });

    // Add selected class to the clicked option
    const selectedBtn = document.querySelector(`.option-btn[data-option="${option}"]`);
    if (selectedBtn) {
        selectedBtn.classList.add('selected');

        // Add selection animation
        selectedBtn.style.transform = 'scale(1.05)';
        setTimeout(() => {
            selectedBtn.style.transform = 'scale(1)';
        }, 200);

        // Create ripple effect
        createRippleEffect(selectedBtn);
    }

    // Play selection sound
    playSound('tick');

    // Enable submit button with pulse animation
    const submitBtn = document.getElementById('submit-btn');
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.classList.add('pulse-animation');
    }

    // Add haptic feedback if supported
    if (navigator.vibrate) {
        navigator.vibrate(50);
    }
}

function createRippleEffect(element) {
    const ripple = document.createElement('div');
    ripple.className = 'ripple-effect';

    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple 0.6s linear;
        top: 50%;
        left: 50%;
        margin-left: -${size/2}px;
        margin-top: -${size/2}px;
        pointer-events: none;
        z-index: 10;
    `;

    element.style.position = 'relative';
    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

function submitAnswer() {
    if (!selectedAnswer) return;
    
    clearInterval(questionTimer);
    
    const question = questions[currentQuestion];
    const isCorrect = selectedAnswer === question.correct_answer;
    const timeTaken = Math.round((Date.now() - questionStartTime) / 1000);
    
    // Calculate points based on difficulty and time
    let points = 0;
    if (isCorrect) {
        const basePoints = gameConfig.difficulty === 'easy' ? 10 : 
                          gameConfig.difficulty === 'medium' ? 15 : 20;
        const timeBonus = Math.max(0, timePerQuestion - timeTaken);
        points = basePoints + timeBonus;
        correctAnswers++;
    }
    
    score += points;
    
    // Update score display
    document.getElementById('current-score').textContent = score;
    
    // Show correct/incorrect feedback
    showAnswerFeedback(isCorrect, question.correct_answer);
    
    // Save answer to database
    saveAnswer(question.id, selectedAnswer, isCorrect, points, timeTaken);
    
    // Move to next question after delay
    setTimeout(() => {
        currentQuestion++;
        loadQuestion();
    }, 2000);
}

function showAnswerFeedback(isCorrect, correctAnswer) {
    const optionBtns = document.querySelectorAll('.option-btn');

    optionBtns.forEach(btn => {
        btn.disabled = true;

        if (btn.dataset.option === correctAnswer) {
            btn.classList.add('correct');
            // Add success animation
            btn.style.animation = 'correctAnswer 0.6s ease-in-out';
        } else if (btn.dataset.option === selectedAnswer && !isCorrect) {
            btn.classList.add('incorrect');
            // Add error animation
            btn.style.animation = 'incorrectAnswer 0.6s ease-in-out';
        }
    });

    // Play appropriate sound
    playSound(isCorrect ? 'correct' : 'incorrect');

    // Add haptic feedback
    if (navigator.vibrate) {
        navigator.vibrate(isCorrect ? [100] : [100, 50, 100]);
    }

    // Show enhanced feedback animation
    if (isCorrect) {
        const points = score - (correctAnswers - 1) * 10;
        showFloatingText(`+${points} Points!`, 'success');
        showStreakEffect();
        updateScoreWithAnimation(points);
    } else {
        showFloatingText('Incorrect!', 'error');
        showScreenShake();
    }

    // Update progress with animation
    updateProgressWithAnimation();
}

function showStreakEffect() {
    // Create streak particles
    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'streak-particle';
        particle.style.cssText = `
            position: fixed;
            width: 4px;
            height: 4px;
            background: #10b981;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            animation: streakParticle ${1 + Math.random()}s ease-out forwards;
            z-index: 1000;
            pointer-events: none;
        `;

        document.body.appendChild(particle);

        setTimeout(() => {
            particle.remove();
        }, 2000);
    }
}

function showScreenShake() {
    const gameContainer = document.querySelector('.game-container') || document.body;
    gameContainer.style.animation = 'screenShake 0.5s ease-in-out';

    setTimeout(() => {
        gameContainer.style.animation = '';
    }, 500);
}

function updateScoreWithAnimation(points) {
    const scoreElement = document.getElementById('current-score');
    if (scoreElement) {
        scoreElement.style.animation = 'scoreUpdate 0.5s ease-in-out';
        setTimeout(() => {
            scoreElement.style.animation = '';
        }, 500);
    }
}

function updateProgressWithAnimation() {
    const progressFill = document.getElementById('progress-fill');
    if (progressFill) {
        const progress = ((currentQuestion + 1) / gameConfig.totalQuestions) * 100;
        progressFill.style.transition = 'width 0.5s ease-in-out';
        progressFill.style.width = progress + '%';
    }
}

function showFloatingText(text, type) {
    const floatingText = document.createElement('div');
    floatingText.className = `floating-text ${type}`;
    floatingText.textContent = text;
    floatingText.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        font-weight: bold;
        color: ${type === 'success' ? '#10b981' : '#ef4444'};
        z-index: 1000;
        pointer-events: none;
        animation: floatUp 2s ease-out forwards;
    `;
    
    document.body.appendChild(floatingText);
    
    setTimeout(() => {
        floatingText.remove();
    }, 2000);
}

function showTimeExceededModal() {
    clearInterval(questionTimer);

    // Remove any existing modal first
    const existingModal = document.getElementById('time-exceeded-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create time exceeded modal
    const modal = document.createElement('div');
    modal.id = 'time-exceeded-modal';
    modal.className = 'time-exceeded-modal';
    modal.innerHTML = `
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <div class="time-exceeded-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h2>Time Exceeded!</h2>
                <p>Please try again or exit.</p>
            </div>
            <div class="modal-buttons">
                <button class="btn btn-primary" onclick="tryAgain()">
                    <i class="fas fa-redo"></i> Try Again
                </button>
                <button class="btn btn-secondary" onclick="quitGame()">
                    <i class="fas fa-times"></i> Exit
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add entrance animation
    setTimeout(() => {
        modal.classList.add('show');
    }, 100);
}

function tryAgain() {
    // Remove the modal
    const modal = document.getElementById('time-exceeded-modal');
    if (modal) {
        modal.remove();
    }

    // Reset timer warning styles
    document.getElementById('timer').classList.remove('timer-warning');
    document.getElementById('question-timer').classList.remove('timer-warning');

    // Reset selected answer
    selectedAnswer = null;

    // Reset pause state
    isPaused = false;

    // Reset pause button
    const pauseBtn = document.querySelector('button[onclick="pauseGame()"]');
    if (pauseBtn) {
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
        pauseBtn.title = 'Pause Game';
    }

    // Reload the current question
    loadQuestion();
}

async function saveAnswer(questionId, answer, isCorrect, points, timeTaken) {
    try {
        await fetch('../api/save-game-answer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: gameConfig.sessionId,
                question_id: questionId,
                answer: answer,
                is_correct: isCorrect,
                points: points,
                time_taken: timeTaken
            })
        });
    } catch (error) {
        console.error('Failed to save answer:', error);
    }
}

function endGame() {
    clearInterval(questionTimer);
    
    const totalTime = Math.round((Date.now() - gameStartTime) / 1000);
    const accuracy = Math.round((correctAnswers / gameConfig.totalQuestions) * 100);
    
    // Update final stats
    document.getElementById('final-score').textContent = score;
    document.getElementById('correct-count').textContent = correctAnswers;
    document.getElementById('accuracy-percent').textContent = accuracy + '%';
    document.getElementById('time-taken').textContent = totalTime + 's';
    
    // Save game session
    saveGameSession(totalTime, accuracy);
    
    // Show result screen
    document.getElementById('question-container').style.display = 'none';
    document.getElementById('result-screen').style.display = 'block';
    
    // Trigger celebration animation
    setTimeout(() => {
        createConfetti();
    }, 500);
}

async function saveGameSession(totalTime, accuracy) {
    try {
        await fetch('../api/complete-game-session.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: gameConfig.sessionId,
                questions_answered: gameConfig.totalQuestions,
                correct_answers: correctAnswers,
                wrong_answers: gameConfig.totalQuestions - correctAnswers,
                points_earned: score,
                total_time: totalTime,
                accuracy: accuracy
            })
        });
    } catch (error) {
        console.error('Failed to save game session:', error);
    }
}

function createConfetti() {
    // Enhanced confetti effect with different shapes and animations
    const shapes = ['circle', 'square', 'triangle', 'star'];
    const colors = ['#f59e0b', '#10b981', '#3b82f6', '#ef4444', '#8b5cf6', '#ec4899'];

    for (let i = 0; i < 100; i++) {
        const confetti = document.createElement('div');
        const shape = shapes[Math.floor(Math.random() * shapes.length)];
        const color = colors[Math.floor(Math.random() * colors.length)];
        const size = Math.random() * 8 + 4;

        confetti.className = `confetti-${shape}`;
        confetti.style.cssText = `
            position: fixed;
            width: ${size}px;
            height: ${size}px;
            background: ${color};
            top: -20px;
            left: ${Math.random() * 100}%;
            animation: confettiFall ${2 + Math.random() * 4}s linear forwards,
                       confettiRotate ${1 + Math.random() * 2}s linear infinite;
            z-index: 1000;
            border-radius: ${shape === 'circle' ? '50%' : '0'};
            transform-origin: center;
        `;

        if (shape === 'triangle') {
            confetti.style.width = '0';
            confetti.style.height = '0';
            confetti.style.borderLeft = `${size/2}px solid transparent`;
            confetti.style.borderRight = `${size/2}px solid transparent`;
            confetti.style.borderBottom = `${size}px solid ${color}`;
            confetti.style.background = 'transparent';
        }

        document.body.appendChild(confetti);

        setTimeout(() => {
            confetti.remove();
        }, 6000);
    }

    // Add celebration sound effect
    playSound('celebration');
}

// Enhanced sound system
function playSound(type) {
    // Create audio context for sound effects
    if (!window.audioContext) {
        window.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }

    const ctx = window.audioContext;
    const oscillator = ctx.createOscillator();
    const gainNode = ctx.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(ctx.destination);

    switch(type) {
        case 'correct':
            oscillator.frequency.setValueAtTime(523.25, ctx.currentTime); // C5
            oscillator.frequency.setValueAtTime(659.25, ctx.currentTime + 0.1); // E5
            gainNode.gain.setValueAtTime(0.3, ctx.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3);
            oscillator.start(ctx.currentTime);
            oscillator.stop(ctx.currentTime + 0.3);
            break;

        case 'incorrect':
            oscillator.frequency.setValueAtTime(220, ctx.currentTime); // A3
            oscillator.frequency.setValueAtTime(196, ctx.currentTime + 0.1); // G3
            gainNode.gain.setValueAtTime(0.3, ctx.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.4);
            oscillator.start(ctx.currentTime);
            oscillator.stop(ctx.currentTime + 0.4);
            break;

        case 'celebration':
            // Play a celebratory melody
            const notes = [523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6
            notes.forEach((freq, index) => {
                const osc = ctx.createOscillator();
                const gain = ctx.createGain();
                osc.connect(gain);
                gain.connect(ctx.destination);

                osc.frequency.setValueAtTime(freq, ctx.currentTime + index * 0.15);
                gain.gain.setValueAtTime(0.2, ctx.currentTime + index * 0.15);
                gain.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + index * 0.15 + 0.3);

                osc.start(ctx.currentTime + index * 0.15);
                osc.stop(ctx.currentTime + index * 0.15 + 0.3);
            });
            break;

        case 'tick':
            oscillator.frequency.setValueAtTime(800, ctx.currentTime);
            gainNode.gain.setValueAtTime(0.1, ctx.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.1);
            oscillator.start(ctx.currentTime);
            oscillator.stop(ctx.currentTime + 0.1);
            break;
    }
}

// Game control functions
function pauseGame() {
    // Toggle pause state
    if (isPaused) {
        // Resume game
        isPaused = false;
        const pauseBtn = document.querySelector('button[onclick="pauseGame()"]');
        if (pauseBtn) {
            pauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            pauseBtn.title = 'Pause Game';
        }

        // Hide pause overlay
        const pauseOverlay = document.getElementById('pause-overlay');
        if (pauseOverlay) {
            pauseOverlay.remove();
        }

        playSound('tick');
    } else {
        // Pause game
        isPaused = true;
        const pauseBtn = document.querySelector('button[onclick="pauseGame()"]');
        if (pauseBtn) {
            pauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            pauseBtn.title = 'Resume Game';
        }

        // Show pause overlay
        showPauseOverlay();
        playSound('tick');
    }
}

function showPauseOverlay() {
    // Remove any existing pause overlay first
    const existingOverlay = document.getElementById('pause-overlay');
    if (existingOverlay) {
        existingOverlay.remove();
    }

    // Create new pause overlay
    const pauseOverlay = document.createElement('div');
    pauseOverlay.id = 'pause-overlay';
    pauseOverlay.className = 'pause-overlay';
    pauseOverlay.innerHTML = `
        <div class="pause-content">
            <div class="pause-icon">
                <i class="fas fa-pause-circle"></i>
            </div>
            <h2>Game Paused</h2>
            <p>Take a break! Click the play button to resume your game</p>
            <div class="pause-stats">
                <div class="stat-item">
                    <span class="stat-number">${currentQuestion + 1}</span>
                    <span class="stat-label">Current Question</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${score}</span>
                    <span class="stat-label">Current Score</span>
                </div>
            </div>
            <div class="pause-buttons">
                <button class="btn btn-primary" onclick="pauseGame()">
                    <i class="fas fa-play"></i> Resume
                </button>
                <button class="btn btn-secondary" onclick="quitGame()">
                    <i class="fas fa-times"></i> Quit Game
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(pauseOverlay);
    pauseOverlay.style.display = 'flex';
}

function quitGame() {
    if (confirm('Are you sure you want to quit the game? Your progress will be lost.')) {
        window.location.href = 'dashboard.php';
    }
}

function playAgain() {
    window.location.href = `quick-game.php?difficulty=${gameConfig.difficulty}&questions=${gameConfig.totalQuestions}`;
}

function backToDashboard() {
    window.location.href = 'dashboard.php';
}

function showError(message) {
    alert(message);
    window.location.href = 'dashboard.php';
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes floatUp {
        0% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
        100% {
            opacity: 0;
            transform: translate(-50%, -150%) scale(1.2);
        }
    }
    
    @keyframes confettiFall {
        0% {
            transform: translateY(-10px) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
