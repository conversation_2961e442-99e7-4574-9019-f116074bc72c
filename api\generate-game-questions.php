<?php
/**
 * Generate Game Questions API for AI-Powered LMS
 * Uses Gemini AI to generate questions for game modes
 */

require_once '../config/database.php';
require_once '../config/question-deduplication.php';
require_once '../config/gemini-api.php';

// Set JSON header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit();
}

// Validate required fields
$required_fields = ['session_id', 'difficulty', 'question_count', 'department_id', 'academic_level_id'];
foreach ($required_fields as $field) {
    if (!isset($input[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
        exit();
    }
}

$sessionId = (int)$input['session_id'];
$difficulty = $input['difficulty'];
$questionCount = (int)$input['question_count'];
$departmentId = (int)$input['department_id'];
$academicLevelId = (int)$input['academic_level_id'];

// Validate difficulty
if (!in_array($difficulty, ['easy', 'medium', 'hard'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid difficulty level']);
    exit();
}

// Validate question count
if ($questionCount < 1 || $questionCount > 50) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Question count must be between 1 and 50']);
    exit();
}

try {
    // Get student ID and game mode from game session
    $gameSession = fetchOne("SELECT student_id, game_mode FROM game_sessions WHERE id = :id", ['id' => $sessionId]);
    if (!$gameSession) {
        throw new Exception('Invalid game session');
    }
    $studentId = $gameSession['student_id'];
    $gameMode = $gameSession['game_mode'];

    // Get department and academic level info
    $department = fetchOne("SELECT id, name, code FROM departments WHERE id = :id", ['id' => $departmentId]);
    $academicLevel = fetchOne("SELECT id, level_name, level_code FROM academic_levels WHERE id = :id", ['id' => $academicLevelId]);

    if (!$department || !$academicLevel) {
        throw new Exception('Invalid department or academic level');
    }

    // Generate questions using Gemini AI with deduplication
    $questions = generateQuestionsWithAI($department, $academicLevel, $difficulty, $questionCount, $studentId);
    
    // Save questions to database and record in history
    $savedQuestions = [];
    foreach ($questions as $index => $question) {
        $questionId = insertRecord('game_questions', [
            'session_id' => $sessionId,
            'question_text' => $question['question'],
            'option_a' => $question['option_a'],
            'option_b' => $question['option_b'],
            'option_c' => $question['option_c'],
            'option_d' => $question['option_d'],
            'correct_answer' => $question['correct_answer'],
            'question_order' => $index + 1,
            'difficulty' => $difficulty
        ]);

        if ($questionId) {
            $question['id'] = $questionId;
            $savedQuestions[] = $question;

            // Record question in history to prevent future repetition
            recordQuestionAsked(
                $studentId,
                $question['question'],
                $department['id'],
                $academicLevel['id'],
                $difficulty,
                $gameMode
            );
        }
    }

    // Debug: Log the saved questions
    error_log("Saved questions count: " . count($savedQuestions));

    echo json_encode([
        'success' => true,
        'questions' => $savedQuestions,
        'message' => 'Questions generated successfully',
        'debug_count' => count($savedQuestions)
    ]);
    
} catch (Exception $e) {
    error_log("Game question generation error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to generate questions: ' . $e->getMessage()
    ]);
}

function generateQuestionsWithAI($department, $academicLevel, $difficulty, $questionCount, $studentId = null) {
    // Use the improved Gemini API function with deduplication
    try {
        $questions = generateQuestionsWithGemini($department, $academicLevel, $questionCount, '', $studentId, $difficulty);

        if (empty($questions)) {
            error_log("Gemini API returned empty questions, using fallback");
            return generateFallbackQuestions($department, $academicLevel, $difficulty, $questionCount);
        }

        return $questions;
    } catch (Exception $e) {
        error_log("Error in generateQuestionsWithAI: " . $e->getMessage());
        // Return fallback questions on error
        return generateFallbackQuestions($department, $academicLevel, $difficulty, $questionCount);
    }
}

function generateFallbackQuestions($department, $academicLevel, $difficulty, $count) {
    // Generate more realistic questions based on department
    $questionTemplates = getQuestionTemplatesByDepartment($department['name'], $academicLevel['level_name'], $difficulty);

    $fallbackQuestions = [];
    $templateCount = count($questionTemplates);

    for ($i = 0; $i < $count; $i++) {
        $template = $questionTemplates[$i % $templateCount];
        $fallbackQuestions[] = [
            'question' => $template['question'],
            'option_a' => $template['option_a'],
            'option_b' => $template['option_b'],
            'option_c' => $template['option_c'],
            'option_d' => $template['option_d'],
            'correct_answer' => $template['correct_answer']
        ];
    }

    return $fallbackQuestions;
}

function getQuestionTemplatesByDepartment($departmentName, $levelName, $difficulty) {
    $templates = [];

    // Convert to lowercase for easier matching
    $dept = strtolower($departmentName);
    $level = strtolower($levelName);

    // Computer Science questions
    if (strpos($dept, 'computer') !== false || strpos($dept, 'software') !== false || strpos($dept, 'information') !== false) {
        $templates = [
            [
                'question' => 'What does HTML stand for?',
                'option_a' => 'HyperText Markup Language',
                'option_b' => 'High Tech Modern Language',
                'option_c' => 'Home Tool Markup Language',
                'option_d' => 'Hyperlink and Text Markup Language',
                'correct_answer' => 'A'
            ],
            [
                'question' => 'Which programming language is known as the "mother of all languages"?',
                'option_a' => 'Python',
                'option_b' => 'C',
                'option_c' => 'Java',
                'option_d' => 'Assembly',
                'correct_answer' => 'B'
            ],
            [
                'question' => 'What is the time complexity of binary search?',
                'option_a' => 'O(n)',
                'option_b' => 'O(log n)',
                'option_c' => 'O(n²)',
                'option_d' => 'O(1)',
                'correct_answer' => 'B'
            ],
            [
                'question' => 'Which of the following is NOT a programming paradigm?',
                'option_a' => 'Object-Oriented',
                'option_b' => 'Functional',
                'option_c' => 'Procedural',
                'option_d' => 'Circular',
                'correct_answer' => 'D'
            ]
        ];
    }
    // Engineering questions
    else if (strpos($dept, 'engineering') !== false || strpos($dept, 'mechanical') !== false || strpos($dept, 'electrical') !== false) {
        $templates = [
            [
                'question' => 'What is the SI unit of force?',
                'option_a' => 'Joule',
                'option_b' => 'Newton',
                'option_c' => 'Watt',
                'option_d' => 'Pascal',
                'correct_answer' => 'B'
            ],
            [
                'question' => 'Which law states that the current through a conductor is directly proportional to the voltage?',
                'option_a' => 'Kirchhoff\'s Law',
                'option_b' => 'Faraday\'s Law',
                'option_c' => 'Ohm\'s Law',
                'option_d' => 'Lenz\'s Law',
                'correct_answer' => 'C'
            ],
            [
                'question' => 'What is the efficiency of an ideal heat engine operating between two reservoirs?',
                'option_a' => '100%',
                'option_b' => 'Depends on the Carnot cycle',
                'option_c' => '50%',
                'option_d' => 'Cannot be determined',
                'correct_answer' => 'B'
            ]
        ];
    }
    // Business/Management questions
    else if (strpos($dept, 'business') !== false || strpos($dept, 'management') !== false || strpos($dept, 'accounting') !== false) {
        $templates = [
            [
                'question' => 'What does ROI stand for in business?',
                'option_a' => 'Return on Investment',
                'option_b' => 'Rate of Interest',
                'option_c' => 'Risk of Investment',
                'option_d' => 'Revenue over Income',
                'correct_answer' => 'A'
            ],
            [
                'question' => 'Which of the following is a current asset?',
                'option_a' => 'Building',
                'option_b' => 'Equipment',
                'option_c' => 'Inventory',
                'option_d' => 'Land',
                'correct_answer' => 'C'
            ],
            [
                'question' => 'What is the primary goal of marketing?',
                'option_a' => 'Increase production',
                'option_b' => 'Satisfy customer needs',
                'option_c' => 'Reduce costs',
                'option_d' => 'Hire more employees',
                'correct_answer' => 'B'
            ]
        ];
    }
    // Science questions
    else if (strpos($dept, 'science') !== false || strpos($dept, 'biology') !== false || strpos($dept, 'chemistry') !== false || strpos($dept, 'physics') !== false) {
        $templates = [
            [
                'question' => 'What is the chemical symbol for gold?',
                'option_a' => 'Go',
                'option_b' => 'Gd',
                'option_c' => 'Au',
                'option_d' => 'Ag',
                'correct_answer' => 'C'
            ],
            [
                'question' => 'Which organelle is known as the powerhouse of the cell?',
                'option_a' => 'Nucleus',
                'option_b' => 'Mitochondria',
                'option_c' => 'Ribosome',
                'option_d' => 'Endoplasmic Reticulum',
                'correct_answer' => 'B'
            ],
            [
                'question' => 'What is the speed of light in vacuum?',
                'option_a' => '3 × 10⁸ m/s',
                'option_b' => '3 × 10⁶ m/s',
                'option_c' => '3 × 10¹⁰ m/s',
                'option_d' => '3 × 10⁴ m/s',
                'correct_answer' => 'A'
            ]
        ];
    }
    // Default general questions
    else {
        $templates = [
            [
                'question' => 'What is the capital of Nigeria?',
                'option_a' => 'Lagos',
                'option_b' => 'Abuja',
                'option_c' => 'Kano',
                'option_d' => 'Port Harcourt',
                'correct_answer' => 'B'
            ],
            [
                'question' => 'Which year did Nigeria gain independence?',
                'option_a' => '1960',
                'option_b' => '1963',
                'option_c' => '1959',
                'option_d' => '1961',
                'correct_answer' => 'A'
            ],
            [
                'question' => 'What does "www" stand for?',
                'option_a' => 'World Wide Web',
                'option_b' => 'World Wide Wait',
                'option_c' => 'World Wide Win',
                'option_d' => 'World Wide War',
                'correct_answer' => 'A'
            ]
        ];
    }

    return $templates;
}
?>
