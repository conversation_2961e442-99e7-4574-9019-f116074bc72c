/* Student Profile Redesign - Improved Layout and Responsiveness */
/* CRITICAL: This CSS file must override all existing styles */

/* Reset any conflicting styles from other CSS files */
* {
    box-sizing: border-box;
}

/* IMPORTANT: Override all existing styles to match test page exactly */
.main-content {
    margin-left: 280px !important;
    width: calc(100vw - 280px) !important;
    min-height: 100vh !important;
    background: #f8fafc !important;
    padding: 0 !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
    position: relative !important;
}

/* Ensure admin layout doesn't cause horizontal scroll */
.admin-layout {
    overflow-x: hidden !important;
    width: 100vw !important;
    box-sizing: border-box !important;
}

/* Force override any problematic existing styles */
.admin-layout .main-content {
    margin-left: 280px !important;
    width: calc(100vw - 280px) !important;
}

/* Override any problematic background gradients */
.content-header {
    background: white !important;
    border-bottom: 1px solid #e5e7eb !important;
    padding: 1.5rem 2rem !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    flex-wrap: wrap !important;
    gap: 1rem !important;
}

.content-header h1 {
    color: #1f2937 !important;
    font-size: 1.875rem !important;
    font-weight: 700 !important;
    margin: 0.5rem 0 0.25rem 0 !important;
}

.content-header p {
    color: #6b7280 !important;
    margin: 0 !important;
    font-size: 0.95rem !important;
}

.header-left {
    flex: 1 !important;
    min-width: 0 !important;
}

.header-right {
    flex-shrink: 0 !important;
}

.content {
    width: 100% !important;
    padding: 1.5rem !important;
    box-sizing: border-box !important;
    min-height: calc(100vh - 80px) !important;
    overflow-x: hidden !important;
}

.profile-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    box-sizing: border-box !important;
}

/* Improved content hierarchy */
.profile-header-section {
    order: 1;
}

.stats-section {
    order: 2;
}

.details-section {
    order: 3;
}

.activity-section {
    order: 4;
}

/* Section Titles */
.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.section-title i {
    color: #3b82f6;
    font-size: 1.1rem;
}

/* Improved visual hierarchy */
.profile-header-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.stats-section,
.details-section,
.activity-section {
    background: transparent;
}

/* Add subtle animations */
.profile-card-new,
.stat-card-new,
.details-card-new,
.activity-card-new {
    transition: all 0.3s ease;
}

.profile-card-new:hover,
.details-card-new:hover,
.activity-card-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Profile Header Section */
.profile-header-section {
    margin-bottom: 2rem !important;
}

/* Override any existing profile styles */
.profile-card,
.profile-header,
.profile-info,
.profile-meta,
.approval-status {
    display: none !important;
}

/* Hide old stats grid */
.stats-grid {
    display: none !important;
}

/* Hide old details card */
.details-card {
    display: none !important;
}

/* Hide old activity card */
.activity-card {
    display: none !important;
}

.profile-card-new {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    padding: 1.5rem !important;
    border: 1px solid #e5e7eb !important;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.profile-avatar-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.profile-avatar-new {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    flex-shrink: 0;
}

.profile-basic-info h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.student-id-display {
    font-size: 1rem;
    font-weight: 600;
    color: #3b82f6;
    margin: 0 0 0.25rem 0;
}

.department-display {
    font-size: 0.95rem;
    color: #6b7280;
    margin: 0;
}

.profile-status-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
}

.status-badge-new {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge-new.approved {
    background: #10b981;
    color: white;
}

.status-badge-new.pending {
    background: #f59e0b;
    color: white;
}

.contact-info p {
    margin: 0.25rem 0;
    color: #6b7280;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-info i {
    color: #9ca3af;
    width: 16px;
}

/* Stats Section */
.stats-section {
    margin-bottom: 2rem;
}

.stats-grid-new {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.stat-card-new {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon-new {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-card-new:nth-child(1) .stat-icon-new {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-card-new:nth-child(2) .stat-icon-new {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card-new:nth-child(3) .stat-icon-new {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card-new:nth-child(4) .stat-icon-new {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-content {
    flex: 1;
}

.stat-number-new {
    display: block;
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1;
}

.stat-label-new {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

/* Details Section */
.details-section {
    margin-bottom: 2rem;
}

.details-card-new {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
}

.details-grid-new {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.detail-item-new {
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.detail-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-size: 0.95rem;
    color: #1f2937;
    font-weight: 500;
}

/* Activity Section */
.activity-section {
    margin-bottom: 2rem;
}

.activity-card-new {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
}

.activity-list-new {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.activity-main {
    flex: 1;
}

.activity-date-new {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.activity-details-new {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.activity-score-new {
    flex-shrink: 0;
}

.score-badge-new {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
}

.score-badge-new.good {
    background: #10b981;
}

.score-badge-new.average {
    background: #f59e0b;
}

.score-badge-new.poor {
    background: #ef4444;
}

/* Text Contrast and Visibility Improvements */
.btn {
    color: #1f2937 !important;
    background: white !important;
    border: 1px solid #d1d5db !important;
}

.btn-primary {
    background: #3b82f6 !important;
    color: white !important;
    border: 1px solid #3b82f6 !important;
}

.btn-secondary {
    background: #6b7280 !important;
    color: white !important;
    border: 1px solid #6b7280 !important;
}

/* Ensure all text has proper contrast */
* {
    text-shadow: none !important;
}

/* Fix any remaining blue background text issues */
.profile-header {
    background: white !important;
    color: #1f2937 !important;
}

.profile-header h2,
.profile-header p,
.profile-header span {
    color: #1f2937 !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .stats-grid-new {
        grid-template-columns: repeat(2, 1fr);
    }

    .details-grid-new {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    /* Fix sidebar overlap issues */
    .sidebar {
        position: fixed;
        left: -280px;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        margin-left: 0 !important;
        width: 100vw !important;
        max-width: 100vw;
        overflow-x: hidden;
    }

    .content {
        padding: 0.5rem;
        width: 100%;
        box-sizing: border-box;
    }

    .profile-container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }

    .profile-avatar-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .profile-status-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .stats-grid-new {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .activity-item-new {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .activity-details-new {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Mobile text visibility fixes */
    .content-header {
        padding: 1rem !important;
        width: 100%;
        box-sizing: border-box;
    }

    .content-header h1 {
        font-size: 1.5rem !important;
        color: #1f2937 !important;
    }

    .content-header p {
        color: #6b7280 !important;
    }

    /* Ensure no content is cut off */
    .header-left,
    .header-right {
        width: 100%;
        max-width: 100%;
    }

    .header-right {
        margin-top: 1rem;
    }
}

@media (max-width: 480px) {
    .profile-card-new,
    .details-card-new,
    .activity-card-new {
        padding: 1rem;
        margin: 0 0 1rem 0;
    }

    .stat-card-new {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .profile-basic-info h2 {
        font-size: 1.5rem;
    }

    .stat-number-new {
        font-size: 1.5rem;
    }

    /* Ensure mobile sidebar doesn't interfere */
    .sidebar {
        z-index: 1000;
    }

    .main-content {
        position: relative;
        z-index: 1;
        padding: 0;
    }

    .content {
        padding: 0.25rem;
    }

    /* Make cards full width on very small screens */
    .profile-card-new,
    .details-card-new,
    .activity-card-new,
    .stat-card-new {
        width: 100%;
        box-sizing: border-box;
    }

    /* Adjust typography for small screens */
    .section-title {
        font-size: 1.1rem;
    }

    .detail-item-new {
        padding: 0.75rem;
    }

    .activity-item-new {
        padding: 0.75rem;
    }

    /* Ensure buttons are properly sized */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* Additional fixes for landscape mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .main-content {
        width: 100vw !important;
        margin-left: 0 !important;
    }

    .profile-avatar-section {
        flex-direction: row;
        text-align: left;
    }
}

/* Ensure no horizontal scrolling */
@media (max-width: 1200px) {
    .main-content {
        max-width: 100vw !important;
        overflow-x: hidden !important;
    }

    .profile-container {
        max-width: 100% !important;
        padding: 0 1rem !important;
    }
}

/* FINAL OVERRIDE: Ensure new layout is visible and old layout is hidden */
.profile-header-section,
.stats-section,
.details-section,
.activity-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.profile-card-new,
.stats-grid-new,
.details-card-new,
.activity-card-new {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.stats-grid-new {
    display: grid !important;
}

.profile-avatar-section,
.profile-status-section,
.activity-list-new {
    display: flex !important;
}

.details-grid-new {
    display: grid !important;
}

/* Force show all new elements */
.section-title,
.profile-card-new,
.stat-card-new,
.detail-item-new,
.activity-item-new {
    display: block !important;
    visibility: visible !important;
}

.profile-avatar-section,
.profile-status-section,
.stat-card-new,
.activity-item-new {
    display: flex !important;
}

.stats-grid-new,
.details-grid-new {
    display: grid !important;
}
