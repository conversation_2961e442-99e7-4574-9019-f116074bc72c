# AI-Powered Learning Management System
## Ogbonnaya Onu Polytechnic, Aba

### Project Overview
A comprehensive, gamified Learning Management System designed specifically for students of Ogbonnaya Onu Polytechnic. The system leverages artificial intelligence to generate personalized, department-specific multiple-choice questions and incorporates engaging gamification elements to enhance the learning experience.

### Features
- **Secure Authentication**: Separate admin and student portals with robust security
- **Department-Specific Learning**: Support for all 31 departments/courses
- **AI-Powered Questions**: Dynamic question generation based on department and academic level
- **Gamification**: Rewards, badges, streaks, and progress tracking
- **Admin Management**: Complete oversight of student registrations and system analytics
- **Responsive Design**: Professional, mobile-friendly interface

### Technology Stack
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP 8.0+
- **Database**: MySQL 8.0+
- **AI Engine**: Python with machine learning libraries
- **Server**: Apache (XAMPP compatible)

### Project Structure
```
LMS/
├── admin/                  # Admin panel files
├── api/                    # API endpoints
├── assets/                 # CSS, JS, and other assets
│   ├── css/
│   ├── js/
│   └── fonts/
├── config/                 # Configuration files
├── database/               # Database schema and migrations
├── images/                 # Image assets
├── includes/               # PHP includes and utilities
├── logs/                   # Application logs
├── python/                 # AI question generation scripts
├── student/                # Student portal files
├── uploads/                # User uploaded files
└── vendor/                 # Third-party libraries
```

### Installation Instructions
1. Copy the project to your XAMPP htdocs directory
2. Start Apache and MySQL services
3. Import database schema: `database/schema.sql`
4. Import initial data: `database/initial_data.sql`
5. Run admin setup: Navigate to `setup-admin.php`
6. Configure Python environment for AI features

### Academic Levels Supported
- ND1 (National Diploma Year 1)
- ND2 (National Diploma Year 2)
- HND1 (Higher National Diploma Year 1)
- HND2 (Higher National Diploma Year 2)

### Departments (31 Courses)
1. Computer Science
2. Civil Engineering
3. Mass Communication
4. Electrical/Electronic Engineering
5. Mechanical Engineering
6. Accountancy
7. Business Administration
8. Banking and Finance
9. Marketing
10. Public Administration
... and 21 more departments

### Security Features
- Password hashing with PHP's password_hash()
- Session management with secure cookies
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- CSRF protection for forms
- Three security questions for password recovery

### Gamification Elements
- **Points System**: Earn points for correct answers
- **Badges**: Achievement unlocks for milestones
- **Streaks**: Daily learning streak tracking
- **Leaderboards**: Department and level-based rankings
- **Progress Bars**: Visual learning progress indicators
- **Rewards**: Stars, emojis, and achievement celebrations

### Admin Features
- Student registration approval
- Reward and progress monitoring
- System analytics and reporting
- Content management
- User management

### Student Features
- Secure registration with department/level selection
- Personalized quiz generation
- Real-time progress tracking
- Achievement system
- Password recovery with security questions
- Responsive dashboard

### Development Team
Developed for Ogbonnaya Onu Polytechnic, Aba
Built with modern web technologies and AI integration

### License
Educational Use - Ogbonnaya Onu Polytechnic

### Support
For technical support and feature requests, contact the development team.
