<?php
/**
 * Send Message Handler
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

// Set JSON header first
header('Content-Type: application/json');

// Suppress any output before JSON
ob_start();

try {
    require_once '../../config/database.php';
    require_once '../../config/auth.php';
} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'Configuration error']);
    exit;
}

// Check if admin is logged in
if (!isLoggedIn() || !isAdmin()) {
    ob_clean();
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $adminId = $_SESSION['user_id'];
    $studentId = (int)$_POST['student_id'];
    $subject = trim($_POST['subject']);
    $message = trim($_POST['message']);

    // Validate input
    if (empty($studentId) || empty($subject) || empty($message)) {
        ob_clean();
        echo json_encode(['success' => false, 'message' => 'All fields are required']);
        exit;
    }

    // Check if student exists
    $student = fetchOne("SELECT id, first_name, last_name FROM students WHERE id = :id", ['id' => $studentId]);
    if (!$student) {
        ob_clean();
        echo json_encode(['success' => false, 'message' => 'Student not found']);
        exit;
    }

    // Start transaction
    beginTransaction();

    // Check if thread exists between admin and student
    $thread = fetchOne("
        SELECT id FROM message_threads 
        WHERE admin_id = :admin_id AND student_id = :student_id
    ", ['admin_id' => $adminId, 'student_id' => $studentId]);

    if (!$thread) {
        // Create new thread
        $threadId = insertRecord('message_threads', [
            'admin_id' => $adminId,
            'student_id' => $studentId,
            'subject' => $subject
        ]);
    } else {
        $threadId = $thread['id'];
    }

    // Insert message
    $messageId = insertRecord('messages', [
        'sender_id' => $adminId,
        'sender_type' => 'admin',
        'receiver_id' => $studentId,
        'receiver_type' => 'student',
        'thread_id' => $threadId,
        'subject' => $subject,
        'message' => $message
    ]);

    // Update thread with last message info
    executeQuery("
        UPDATE message_threads 
        SET last_message_id = :message_id,
            last_activity = NOW(),
            student_unread_count = student_unread_count + 1
        WHERE id = :thread_id
    ", ['message_id' => $messageId, 'thread_id' => $threadId]);

    commitTransaction();

    ob_clean();
    echo json_encode([
        'success' => true,
        'message' => 'Message sent successfully to ' . htmlspecialchars($student['first_name'] . ' ' . $student['last_name'])
    ]);

} catch (Exception $e) {
    rollbackTransaction();
    error_log("Send message error: " . $e->getMessage());
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'Failed to send message. Please try again.']);
}
?>
