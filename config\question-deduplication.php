<?php
/**
 * Question Deduplication System for AI-Powered LMS
 * Prevents duplicate questions from being shown to students
 */

require_once 'database.php';

/**
 * Generate a hash for a question to check for duplicates
 * 
 * @param string $questionText The question text
 * @return string SHA-256 hash of the question
 */
function generateQuestionHash($questionText) {
    // Normalize the question text for consistent hashing
    $normalized = strtolower(trim($questionText));
    // Remove extra spaces and normalize punctuation
    $normalized = preg_replace('/\s+/', ' ', $normalized);
    $normalized = preg_replace('/[^\w\s]/', '', $normalized);
    
    return hash('sha256', $normalized);
}

/**
 * Check if a question has been asked to a student before
 * 
 * @param int $studentId Student ID
 * @param string $questionText Question text to check
 * @return bool True if question was asked before, false otherwise
 */
function isQuestionAskedBefore($studentId, $questionText) {
    $questionHash = generateQuestionHash($questionText);
    
    $result = fetchOne("
        SELECT id FROM student_question_history 
        WHERE student_id = :student_id AND question_hash = :question_hash
    ", [
        'student_id' => $studentId,
        'question_hash' => $questionHash
    ]);
    
    return $result !== null;
}

/**
 * Get previously asked questions for a student to avoid repetition
 * 
 * @param int $studentId Student ID
 * @param int $departmentId Department ID
 * @param int $academicLevelId Academic level ID
 * @param string $difficulty Difficulty level
 * @param int $limit Maximum number of recent questions to return
 * @return array Array of previously asked questions
 */
function getPreviousQuestions($studentId, $departmentId, $academicLevelId, $difficulty, $limit = 50) {
    return fetchAll("
        SELECT question_text, created_at 
        FROM student_question_history 
        WHERE student_id = :student_id 
        AND department_id = :department_id 
        AND academic_level_id = :academic_level_id 
        AND difficulty = :difficulty
        ORDER BY created_at DESC 
        LIMIT :limit
    ", [
        'student_id' => $studentId,
        'department_id' => $departmentId,
        'academic_level_id' => $academicLevelId,
        'difficulty' => $difficulty,
        'limit' => $limit
    ]);
}

/**
 * Record a question as asked to prevent future repetition
 * 
 * @param int $studentId Student ID
 * @param string $questionText Question text
 * @param int $departmentId Department ID
 * @param int $academicLevelId Academic level ID
 * @param string $difficulty Difficulty level
 * @param string $gameMode Game mode (quick, endless, mission)
 * @return bool True if recorded successfully, false otherwise
 */
function recordQuestionAsked($studentId, $questionText, $departmentId, $academicLevelId, $difficulty, $gameMode = 'quick') {
    $questionHash = generateQuestionHash($questionText);
    
    try {
        // Use INSERT IGNORE to avoid duplicate key errors
        $result = executeQuery("
            INSERT IGNORE INTO student_question_history 
            (student_id, question_hash, question_text, department_id, academic_level_id, difficulty, game_mode) 
            VALUES (:student_id, :question_hash, :question_text, :department_id, :academic_level_id, :difficulty, :game_mode)
        ", [
            'student_id' => $studentId,
            'question_hash' => $questionHash,
            'question_text' => $questionText,
            'department_id' => $departmentId,
            'academic_level_id' => $academicLevelId,
            'difficulty' => $difficulty,
            'game_mode' => $gameMode
        ]);
        
        return $result !== false;
    } catch (Exception $e) {
        error_log("Error recording question: " . $e->getMessage());
        return false;
    }
}

/**
 * Filter out duplicate questions from a generated set
 * 
 * @param array $questions Array of generated questions
 * @param int $studentId Student ID
 * @return array Filtered array with duplicates removed
 */
function filterDuplicateQuestions($questions, $studentId) {
    $filteredQuestions = [];
    
    foreach ($questions as $question) {
        if (!isQuestionAskedBefore($studentId, $question['question'])) {
            $filteredQuestions[] = $question;
        }
    }
    
    return $filteredQuestions;
}

/**
 * Get count of questions asked to student by difficulty
 * 
 * @param int $studentId Student ID
 * @param string $difficulty Difficulty level
 * @return int Number of questions asked
 */
function getQuestionCount($studentId, $difficulty = null) {
    $sql = "SELECT COUNT(*) as count FROM student_question_history WHERE student_id = :student_id";
    $params = ['student_id' => $studentId];
    
    if ($difficulty) {
        $sql .= " AND difficulty = :difficulty";
        $params['difficulty'] = $difficulty;
    }
    
    $result = fetchOne($sql, $params);
    return $result ? (int)$result['count'] : 0;
}

/**
 * Clean up old question history (optional - for performance)
 * Removes questions older than specified days
 * 
 * @param int $days Number of days to keep history
 * @return bool True if cleanup successful
 */
function cleanupOldQuestions($days = 90) {
    try {
        executeQuery("
            DELETE FROM student_question_history 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)
        ", ['days' => $days]);
        
        return true;
    } catch (Exception $e) {
        error_log("Error cleaning up old questions: " . $e->getMessage());
        return false;
    }
}
?>
