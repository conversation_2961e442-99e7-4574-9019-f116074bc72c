<?php
/**
 * Mission Mode - Progressive Level-based Learning
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

if (!$student) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Get all mission levels with student progress
$missionLevels = fetchAll("
    SELECT
        ml.*,
        COALESCE(smp.status, 'locked') as student_status,
        COALESCE(smp.attempts, 0) as attempts,
        COALESCE(smp.best_score, 0) as best_score,
        COALESCE(smp.stars_earned, 0) as stars_earned,
        smp.completed_at
    FROM mission_levels ml
    LEFT JOIN student_mission_progress smp ON ml.id = smp.level_id AND smp.student_id = :student_id
    WHERE ml.is_active = 1
    ORDER BY ml.level_number
", ['student_id' => $student['id']]);

// Debug: Check if we have mission levels
if (empty($missionLevels)) {
    error_log("No mission levels found for student ID: " . $student['id']);
}

// Get total mission points for display
$totalMissionPoints = fetchOne("SELECT COALESCE(SUM(points_earned), 0) as total FROM game_sessions WHERE student_id = :id AND game_mode = 'mission' AND status = 'completed'", ['id' => $student['id']])['total'] ?? 0;

// NEW SIMPLE UNLOCK SYSTEM: Complete previous level to unlock next level
updateMissionUnlocks($student['id'], $missionLevels);

// Debug: Log current mission status
error_log("🔍 Mission Status Debug for student {$student['id']}:");
foreach ($missionLevels as $level) {
    error_log("  Level {$level['level_number']}: Status={$level['student_status']}, Stars={$level['stars_earned']}, Score={$level['best_score']}");
}

function updateMissionUnlocks($studentId, &$missionLevels) {
    $unlocksPerformed = false;

    // Level 1 is always available
    foreach ($missionLevels as &$level) {
        if ($level['level_number'] == 1 && $level['student_status'] === 'locked') {
            executeQuery("
                INSERT INTO student_mission_progress (student_id, level_id, status, unlocked_at, created_at)
                VALUES (:student_id, :level_id, 'available', NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                    status = 'available',
                    unlocked_at = COALESCE(unlocked_at, NOW()),
                    updated_at = NOW()
            ", [
                'student_id' => $studentId,
                'level_id' => $level['id']
            ]);
            $level['student_status'] = 'available';
            $unlocksPerformed = true;
            error_log("🔓 Auto-unlocked Level 1 for student $studentId");
        }
    }

    // For levels 2+: unlock if previous level is completed
    for ($i = 1; $i < count($missionLevels); $i++) {
        $currentLevel = &$missionLevels[$i];
        $previousLevel = $missionLevels[$i - 1];

        // If current level is locked AND previous level is completed, unlock it
        if ($currentLevel['student_status'] === 'locked' && $previousLevel['student_status'] === 'completed') {
            executeQuery("
                INSERT INTO student_mission_progress (student_id, level_id, status, unlocked_at, created_at)
                VALUES (:student_id, :level_id, 'available', NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                    status = CASE WHEN status = 'locked' THEN 'available' ELSE status END,
                    unlocked_at = CASE WHEN status = 'locked' THEN NOW() ELSE unlocked_at END,
                    updated_at = NOW()
            ", [
                'student_id' => $studentId,
                'level_id' => $currentLevel['id']
            ]);
            $currentLevel['student_status'] = 'available';
            $unlocksPerformed = true;
            error_log("🔓 Auto-unlocked Level {$currentLevel['level_number']} for student $studentId (previous level {$previousLevel['level_number']} completed)");
        }
    }

    if ($unlocksPerformed) {
        error_log("✅ Mission unlock check completed for student $studentId");
    }
}

// Get student's current mission statistics
$missionStats = [
    'levels_completed' => count(array_filter($missionLevels, function($l) { return $l['student_status'] === 'completed'; })),
    'total_levels' => count($missionLevels),
    'total_stars' => array_sum(array_column($missionLevels, 'stars_earned')),
    'current_level' => 1
];

// Find current level (first incomplete level)
foreach ($missionLevels as $level) {
    if ($level['student_status'] !== 'completed') {
        $missionStats['current_level'] = $level['level_number'];
        break;
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mission Mode - AI Learning Arena</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link rel="stylesheet" href="../assets/css/mission-mode.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- FontAwesome with fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Fallback for FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body class="mission-page">
    <!-- Floating Elements -->
    <div class="floating-elements" id="floatingElements"></div>

    <div class="mission-container">
        <!-- Header -->
        <header class="mission-header">
            <div class="header-content">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Dashboard</span>
                </button>
                
                <div class="mission-title">
                    <h1><i class="fas fa-map"></i> Mission Mode</h1>
                    <p>Conquer levels and unlock new challenges!</p>
                </div>
                
                <div class="mission-stats">
                    <div class="stat-item">
                        <i class="fas fa-flag"></i>
                        <span><?php echo $missionStats['current_level']; ?>/<?php echo $missionStats['total_levels']; ?></span>
                        <small>Levels</small>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span id="total-stars-display"><?php echo $missionStats['total_stars']; ?></span>
                        <small>Stars</small>
                    </div>
                </div>
            </div>
        </header>

        <!-- Mission Map -->
        <main class="mission-main">
            <div class="mission-map">
                <div class="map-path">
                    <?php if (empty($missionLevels)): ?>
                        <div style="text-align: center; color: white; padding: 50px;">
                            <h3>No Mission Levels Found</h3>
                            <p>Please contact administrator to set up mission levels.</p>
                            <p><a href="../admin/setup-mission-levels.php" style="color: #ffd700;">Setup Mission Levels</a></p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($missionLevels as $index => $level): ?>
                        <?php 
                        $statusClass = '';
                        $iconClass = 'fas fa-lock';
                        $clickable = false;
                        
                        switch ($level['student_status']) {
                            case 'completed':
                                $statusClass = 'completed';
                                $iconClass = 'fas fa-check-circle';
                                $clickable = true;
                                break;
                            case 'available':
                            case 'in_progress':
                                $statusClass = 'available';
                                $iconClass = $level['boss_level'] ? 'fas fa-dragon' : 'fas fa-play-circle';
                                $clickable = true;
                                break;
                            case 'locked':
                            default:
                                $statusClass = 'locked';
                                $iconClass = 'fas fa-lock';
                                break;
                        }
                        
                        $positionClass = ($index % 2 === 0) ? 'left' : 'right';
                        ?>
                        
                        <div class="mission-level-container">
                            <div class="mission-level-card <?php echo $statusClass; ?> <?php echo $clickable ? 'clickable' : ''; ?>"
                                 data-level="<?php echo $level['id']; ?>"
                                 <?php echo $clickable ? 'onclick="startMission(' . $level['id'] . ')"' : ''; ?>>

                                <!-- Play Button -->
                                <div class="play-button <?php echo $statusClass; ?>">
                                    <?php if ($level['student_status'] === 'locked'): ?>
                                        <i class="fas fa-lock"></i>
                                    <?php elseif ($level['student_status'] === 'completed'): ?>
                                        <i class="fas fa-check"></i>
                                    <?php else: ?>
                                        <i class="fas fa-play"></i>
                                    <?php endif; ?>
                                </div>

                                <!-- Level Card -->
                                <div class="level-card">
                                    <div class="level-header">
                                        <div class="level-number">LEVEL <?php echo $level['level_number']; ?></div>
                                        <?php if ($level['boss_level']): ?>
                                            <div class="boss-badge">BOSS</div>
                                        <?php endif; ?>
                                    </div>

                                    <h3 class="level-title"><?php echo htmlspecialchars($level['title']); ?></h3>
                                    <p class="level-description"><?php echo htmlspecialchars($level['description']); ?></p>

                                    <div class="level-badges">
                                        <span class="badge difficulty-badge <?php echo strtolower($level['difficulty']); ?>">
                                            <i class="fas fa-signal"></i>
                                            <?php echo ucfirst($level['difficulty']); ?>
                                        </span>
                                        <span class="badge questions-badge">
                                            <i class="fas fa-question-circle"></i>
                                            <?php echo $level['questions_required']; ?> Questions
                                        </span>
                                        <span class="badge stars-badge">
                                            <i class="fas fa-star"></i>
                                            <?php echo $level['points_reward']; ?> Points Reward
                                        </span>
                                    </div>

                                    <?php if ($level['student_status'] === 'locked'): ?>
                                        <div class="attempts-info">
                                            <i class="fas fa-sync-alt"></i>
                                            <?php if ($level['level_number'] == 1): ?>
                                                1 attempt
                                            <?php else: ?>
                                                1 attempt
                                            <?php endif; ?>
                                        </div>
                                    <?php elseif ($level['student_status'] === 'completed'): ?>
                                        <div class="completion-status">
                                            <div class="stars">
                                                <?php for ($i = 1; $i <= 3; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $level['stars_earned'] ? 'earned' : ''; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="attempts-info">
                                            <i class="fas fa-sync-alt"></i>
                                            1 attempt
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- Mission Start Modal -->
    <div id="missionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Start Mission</h3>
                <span class="close" onclick="closeMissionModal()">&times;</span>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Mission details will be loaded here -->
            </div>
        </div>
    </div>

    <script src="../assets/js/mission-mode.js"></script>

    <script>
        // Floating Elements Animation System
        class FloatingElementsManager {
            constructor() {
                this.container = document.getElementById('floatingElements');
                this.elements = [];
                this.init();
            }

            init() {
                this.createFloatingElements();
                this.startAnimation();
            }

            createFloatingElements() {
                // Create minimal initial light streaks
                for (let i = 0; i < 4; i++) {
                    setTimeout(() => this.createLightStreak(), i * 800);
                }

                // Create minimal initial glow dots
                for (let i = 0; i < 6; i++) {
                    setTimeout(() => this.createGlowDot(), i * 600);
                }

                // Create minimal initial particles
                for (let i = 0; i < 8; i++) {
                    setTimeout(() => this.createParticle(), i * 400);
                }

                // Create minimal initial energy orbs
                for (let i = 0; i < 2; i++) {
                    setTimeout(() => this.createEnergyOrb(), i * 2000);
                }
            }

            createLightStreak() {
                const streak = document.createElement('div');
                streak.className = 'light-streak';

                const startX = Math.random() * window.innerWidth;
                const duration = Math.random() * 2 + 3; // 3-5s (very fast)
                const delay = Math.random() * 0.5; // 0-0.5s delay

                streak.style.left = startX + 'px';
                streak.style.animationDuration = duration + 's';
                streak.style.animationDelay = delay + 's';

                this.container.appendChild(streak);
                this.elements.push(streak);
            }

            createGlowDot() {
                const dot = document.createElement('div');
                dot.className = 'glow-dot';

                const startX = Math.random() * window.innerWidth;
                const duration = Math.random() * 2 + 4; // 4-6s (fast)
                const delay = Math.random() * 0.8; // 0-0.8s delay

                dot.style.left = startX + 'px';
                dot.style.animationDuration = duration + 's';
                dot.style.animationDelay = delay + 's';

                this.container.appendChild(dot);
                this.elements.push(dot);
            }

            createParticle() {
                const particle = document.createElement('div');
                particle.className = 'particle';

                const startX = Math.random() * window.innerWidth;
                const duration = Math.random() * 3 + 4; // 4-7s (much faster)
                const delay = Math.random() * 1; // 0-1s delay

                particle.style.left = startX + 'px';
                particle.style.animationDuration = duration + 's';
                particle.style.animationDelay = delay + 's';

                this.container.appendChild(particle);
                this.elements.push(particle);
            }

            createEnergyOrb() {
                const orb = document.createElement('div');
                orb.className = 'energy-orb';

                const size = Math.random() * 35 + 25; // 25-60px
                const startX = Math.random() * window.innerWidth;
                const duration = Math.random() * 5 + 8; // 8-13s (much faster)
                const delay = Math.random() * 2; // 0-2s delay

                orb.style.width = size + 'px';
                orb.style.height = size + 'px';
                orb.style.left = startX + 'px';
                orb.style.animationDuration = duration + 's';
                orb.style.animationDelay = delay + 's';

                this.container.appendChild(orb);
                this.elements.push(orb);
            }

            startAnimation() {
                // Create subtle background elements occasionally
                setInterval(() => {
                    if (Math.random() < 0.2) { // 20% chance for light streaks
                        this.createLightStreak();
                    }
                    if (Math.random() < 0.3) { // 30% chance for glow dots
                        this.createGlowDot();
                    }
                    if (Math.random() < 0.4) { // 40% chance for particles
                        this.createParticle();
                    }
                    if (Math.random() < 0.1) { // 10% chance for energy orbs
                        this.createEnergyOrb();
                    }

                    // Clean up old elements
                    this.cleanupElements();
                }, 3000); // Every 3 seconds (much less frequent)
            }

            cleanupElements() {
                // Remove elements that have finished animating
                this.elements = this.elements.filter(element => {
                    if (element.offsetTop < -100) {
                        element.remove();
                        return false;
                    }
                    return true;
                });
            }
        }

        // Initialize floating elements when page loads
        document.addEventListener('DOMContentLoaded', function() {
            new FloatingElementsManager();

            // Update mission progress immediately when page loads
            setTimeout(() => {
                updateMissionProgress();
            }, 500); // Small delay to ensure page is fully loaded
        });
    </script>
</body>
</html>
