/* Quick Game Styles for AI-Powered LMS */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.quick-game-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Game Header */
.game-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.game-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.game-info h1 {
    font-size: 1.5rem;
    font-weight: 800;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.difficulty-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.difficulty-badge.easy {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.difficulty-badge.medium {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.difficulty-badge.hard {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.game-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.stat-item i {
    color: #f59e0b;
}

.game-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Progress Bar */
.progress-container {
    padding: 0 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #f59e0b, #d97706);
    width: 0%;
    transition: width 0.5s ease;
}

/* Main Game Area */
.game-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

/* Loading Screen */
.loading-screen {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

.loading-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.loading-content p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.loading-progress {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-bar {
    height: 100%;
    background: white;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* Question Container */
.question-container {
    width: 100%;
    max-width: 800px;
}

.question-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.question-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: #6b7280;
}

.timer-circle {
    width: 60px;
    height: 60px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #f59e0b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 700;
    color: #f59e0b;
    animation: rotate 1s linear infinite;
}

.question-content {
    margin-bottom: 2rem;
}

.question-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
}

.options-container {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.option-btn {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
    font-size: 1rem;
}

.option-btn:hover {
    border-color: #f59e0b;
    background: rgba(245, 158, 11, 0.05);
    transform: translateX(5px);
}

.option-btn.selected {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
    color: white !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

.option-btn.correct {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
    animation: correctAnswer 0.6s ease;
}

.option-btn.incorrect {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    animation: incorrectAnswer 0.6s ease;
}

.option-letter {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: #374151;
    flex-shrink: 0;
}

.option-btn.selected .option-letter {
    background: #1d4ed8 !important;
    color: white !important;
}

.option-btn.correct .option-letter {
    background: #10b981;
    color: white;
}

.option-btn.incorrect .option-letter {
    background: #ef4444;
    color: white;
}

.option-text {
    flex: 1;
    font-weight: 500;
    color: #374151;
}

.question-actions {
    text-align: center;
}

.btn-submit {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-submit:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

.btn-submit:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Result Screen */
.result-screen {
    width: 100%;
    max-width: 600px;
    text-align: center;
}

.result-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.result-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 3rem;
    color: white;
    animation: bounce 1s ease infinite alternate;
}

.result-content h1 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.result-content h2 {
    font-size: 1.5rem;
    color: #f59e0b;
    margin-bottom: 2rem;
}

.final-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(245, 158, 11, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: #f59e0b;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.result-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn-primary, .btn-secondary {
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes bounce {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

@keyframes correctAnswer {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes incorrectAnswer {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

/* Responsive Design */
/* Enhanced Interactive Animations */
@keyframes confettiFall {
    to {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}

@keyframes confettiRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes streakParticle {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translate(var(--random-x, 0), var(--random-y, 0)) scale(0);
        opacity: 0;
    }
}

@keyframes screenShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes scoreUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #10b981; }
    100% { transform: scale(1); }
}

@keyframes pulse-animation {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes floatUp {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -150%) scale(1);
    }
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes timerPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes loadingShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced Interactive Elements */
.option-btn {
    position: relative;
    overflow: hidden;
}



.option-btn.correct {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    border-color: #10b981 !important;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
}

.option-btn.incorrect {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    color: white !important;
    border-color: #ef4444 !important;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4) !important;
}

.pulse-animation {
    animation: pulse-animation 1s infinite;
}

.floating-text {
    animation: floatUp 2s ease-out forwards;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Inter', sans-serif;
    font-weight: 800;
    font-size: 2rem;
}

.timer-warning {
    animation: timerPulse 1s infinite;
    color: #ef4444 !important;
}

.progress-bar {
    position: relative;
    overflow: hidden;
}

/* Pause Overlay Styles */
.pause-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.pause-content {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.pause-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.pause-content h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.pause-content p {
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.pause-stats {
    display: flex;
    justify-content: space-around;
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 15px;
}

.pause-stats .stat-item {
    text-align: center;
}

.pause-stats .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.pause-stats .stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.pause-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.pause-buttons .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pause-buttons .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.pause-buttons .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.pause-buttons .btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
}

.pause-buttons .btn-secondary:hover {
    background: #e5e7eb;
    transform: translateY(-2px);
}

/* Time Exceeded Modal Styles */
.time-exceeded-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10001;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.time-exceeded-modal.show {
    opacity: 1;
}

.time-exceeded-modal .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.time-exceeded-modal .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.time-exceeded-icon {
    font-size: 4rem;
    color: #ef4444;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.time-exceeded-modal h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #ef4444;
    margin-bottom: 1rem;
}

.time-exceeded-modal p {
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.modal-buttons .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-buttons .btn-primary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.modal-buttons .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.modal-buttons .btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
}

.modal-buttons .btn-secondary:hover {
    background: #e5e7eb;
    transform: translateY(-2px);
}

/* Animations */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progressShimmer 2s infinite;
}

.loading-bar {
    position: relative;
    overflow: hidden;
}

.loading-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: loadingShimmer 1.5s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .game-stats {
        gap: 1rem;
    }

    .game-main {
        padding: 1rem;
    }

    .question-card {
        padding: 1.5rem;
    }

    .final-stats {
        grid-template-columns: 1fr;
    }

    .result-actions {
        flex-direction: column;
    }

    .floating-text {
        font-size: 1.5rem;
    }
}
