<?php
/**
 * Departments Management for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_department':
                    $name = trim($_POST['name']);
                    $code = trim($_POST['code']);
                    $description = trim($_POST['description']);
                    
                    if (empty($name) || empty($code)) {
                        throw new Exception('Department name and code are required');
                    }
                    
                    // Check if department code already exists
                    $existing = fetchOne("SELECT id FROM departments WHERE code = ?", [$code]);
                    if ($existing) {
                        throw new Exception('Department code already exists');
                    }

                    executeQuery("
                        INSERT INTO departments (name, code, description, created_at)
                        VALUES (?, ?, ?, NOW())
                    ", [$name, $code, $description]);
                    
                    $success = "Department added successfully!";
                    break;
                    
                case 'edit_department':
                    $id = (int)$_POST['id'];
                    $name = trim($_POST['name']);
                    $code = trim($_POST['code']);
                    $description = trim($_POST['description']);
                    
                    if (empty($name) || empty($code)) {
                        throw new Exception('Department name and code are required');
                    }
                    
                    // Check if department code already exists (excluding current department)
                    $existing = fetchOne("SELECT id FROM departments WHERE code = ? AND id != ?", [$code, $id]);
                    if ($existing) {
                        throw new Exception('Department code already exists');
                    }

                    executeQuery("
                        UPDATE departments
                        SET name = ?, code = ?, description = ?, updated_at = NOW()
                        WHERE id = ?
                    ", [$name, $code, $description, $id]);
                    
                    $success = "Department updated successfully!";
                    break;
                    
                case 'delete_department':
                    $id = (int)$_POST['id'];
                    
                    // Check if department has students
                    $studentCount = fetchOne("SELECT COUNT(*) as count FROM students WHERE department_id = ?", [$id])['count'];
                    if ($studentCount > 0) {
                        throw new Exception("Cannot delete department with existing students. Please transfer students first.");
                    }

                    // Check if department has subjects
                    $subjectCount = fetchOne("SELECT COUNT(*) as count FROM subjects WHERE department_id = ?", [$id])['count'];
                    if ($subjectCount > 0) {
                        throw new Exception("Cannot delete department with existing subjects. Please remove subjects first.");
                    }

                    executeQuery("DELETE FROM departments WHERE id = ?", [$id]);
                    $success = "Department deleted successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get departments with statistics
try {
    // First get basic departments
    $departments = fetchAll("SELECT * FROM departments ORDER BY name");

    // Debug information
    $debug_info = "Found " . count($departments) . " departments in array";

    // Then add statistics to each department
    if (!empty($departments)) {
        foreach ($departments as &$dept) {
            // Get student count
            try {
                $studentCount = fetchOne("SELECT COUNT(*) as count FROM students WHERE department_id = ?", [$dept['id']]);
                $dept['student_count'] = $studentCount ? $studentCount['count'] : 0;
            } catch (Exception $e) {
                $dept['student_count'] = 0;
            }

            // Get AI questions count
            try {
                $questionCount = fetchOne("SELECT COUNT(*) as count FROM questions WHERE department_id = ?", [$dept['id']]);
                $dept['question_count'] = $questionCount ? $questionCount['count'] : 0;
            } catch (Exception $e) {
                $dept['question_count'] = 0;
            }

            // Get approved students count
            try {
                $approvedCount = fetchOne("SELECT COUNT(*) as count FROM students WHERE department_id = ? AND status = 'active'", [$dept['id']]);
                $dept['approved_students'] = $approvedCount ? $approvedCount['count'] : 0;
            } catch (Exception $e) {
                $dept['approved_students'] = 0;
            }
        }
        unset($dept); // Break reference
    }
} catch (Exception $e) {
    $departments = [];
    $debug_info = "Error fetching departments: " . $e->getMessage();
}

// Ensure $departments is always an array
if (!is_array($departments)) {
    $departments = [];
}

// Debug information
$debugInfo = [
    'departments_count' => count($departments),
    'raw_departments' => fetchAll("SELECT * FROM departments LIMIT 3"),
    'error' => isset($error) ? $error : null
];

$pageTitle = 'Departments Management';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item active">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="game-analytics.php" class="nav-item">
                    <i class="fas fa-gamepad"></i>
                    <span>Game Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="admin-profile">
                    <div class="admin-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="admin-info">
                        <div class="admin-name">Administrator</div>
                        <div class="admin-role">System Admin</div>
                    </div>
                </div>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Departments Management</h1>
                    <p>Manage academic departments and their configurations</p>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" onclick="openAddDepartmentModal()">
                        <i class="fas fa-plus"></i>
                        Add New Department
                    </button>
                </div>
            </header>
            
            <!-- Messages -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- Debug Information (remove in production) -->
            <?php if (isset($_GET['debug'])): ?>
                <div style="background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; font-family: monospace; font-size: 12px;">
                    <h4>Debug Information:</h4>
                    <pre><?php echo htmlspecialchars(print_r($debugInfo, true)); ?></pre>
                </div>
            <?php endif; ?>

            <!-- Departments Grid -->
            <div class="departments-container">
                <?php if (empty($departments)): ?>
                    <div style="text-align: center; padding: 20px; background: #fff3cd; border-radius: 5px; margin-bottom: 20px;">
                        <p><strong>Debug:</strong> <?php echo isset($debug_info) ? $debug_info : 'Found ' . count($departments) . ' departments in array'; ?></p>
                        <p><a href="../debug-departments.php">Click here to see detailed debug information</a></p>
                    </div>
                <?php endif; ?>
                <?php if (empty($departments)): ?>
                    <div class="empty-state">
                        <i class="fas fa-building"></i>
                        <h3>No Departments Found</h3>
                        <p>Start by adding your first academic department</p>
                        <button class="btn btn-primary" onclick="openAddDepartmentModal()">
                            <i class="fas fa-plus"></i>
                            Add First Department
                        </button>
                    </div>
                <?php else: ?>
                    <!-- Departments List -->
                    <div class="departments-list">
                        <?php foreach ($departments as $index => $department): ?>
                            <div class="department-row" data-department-id="<?php echo $department['id']; ?>">
                                <div class="dept-avatar">
                                    <div class="avatar-circle" style="background: <?php echo ['#6366f1', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444', '#ec4899', '#84cc16'][$index % 8]; ?>">
                                        <?php echo strtoupper(substr($department['name'], 0, 2)); ?>
                                    </div>
                                </div>

                                <div class="dept-info">
                                    <h3 class="dept-name"><?php echo htmlspecialchars($department['name']); ?></h3>
                                    <span class="dept-code"><?php echo htmlspecialchars($department['code']); ?></span>
                                </div>

                                <div class="dept-stats">
                                    <div class="stat-item">
                                        <span class="stat-number"><?php echo $department['student_count']; ?></span>
                                        <span class="stat-label">Total Students</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number"><?php echo $department['approved_students']; ?></span>
                                        <span class="stat-label">Approved</span>
                                    </div>
                                </div>

                                <div class="dept-actions">
                                    <button class="action-btn edit-btn" onclick="editDepartment(<?php echo $department['id']; ?>)" title="Edit Department">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn delete-btn" onclick="deleteDepartment(<?php echo $department['id']; ?>, '<?php echo htmlspecialchars($department['name']); ?>')" title="Delete Department">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>

                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <!-- Add/Edit Department Modal -->
    <div id="departmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add New Department</h3>
                <span class="close" onclick="closeDepartmentModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="departmentForm" method="POST">
                    <input type="hidden" name="action" id="formAction" value="add_department">
                    <input type="hidden" name="id" id="departmentId">
                    
                    <div class="form-group">
                        <label for="name">Department Name *</label>
                        <input type="text" id="name" name="name" required placeholder="e.g., Computer Science">
                    </div>
                    
                    <div class="form-group">
                        <label for="code">Department Code *</label>
                        <input type="text" id="code" name="code" required placeholder="e.g., CSC" maxlength="10">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4" placeholder="Optional department description"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeDepartmentModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            <span id="submitText">Add Department</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
    /* Modern Departments List Styles */
    .departments-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 24px;
    }

    .department-row {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #f1f5f9;
        transition: all 0.3s ease;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .department-row:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: #e2e8f0;
    }



    .dept-avatar {
        flex-shrink: 0;
    }

    .avatar-circle {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 18px;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .dept-info {
        flex: 1;
        min-width: 0;
    }

    .dept-name {
        font-size: 18px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 4px 0;
        line-height: 1.3;
    }

    .dept-code {
        background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
        color: #475569;
        padding: 4px 12px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        display: inline-block;
    }

    .card-actions {
        display: flex;
        gap: 8px;
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .action-btn {
        width: 36px;
        height: 36px;
        border: none;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        backdrop-filter: blur(10px);
    }

    .edit-btn {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .edit-btn:hover {
        background: #3b82f6;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .delete-btn {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .delete-btn:hover {
        background: #ef4444;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    .card-body {
        padding: 0 24px 20px;
    }

    .dept-description {
        margin-bottom: 20px;
    }

    .description-text {
        color: #64748b;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
    }

    .no-description {
        color: #94a3b8;
        font-style: italic;
        font-size: 14px;
        margin: 0;
    }

    .dept-stats {
        display: flex;
        gap: 2rem;
        flex: 1;
        justify-content: center;
    }

    .dept-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .stat-item {
        text-align: center;
        padding: 0.75rem 1rem;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #f1f5f9;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        min-width: 100px;
    }

    .stat-icon {
        width: 36px;
        height: 36px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        flex-shrink: 0;
    }

    .stat-icon.students {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .stat-icon.approved {
        background: linear-gradient(135deg, #10b981, #047857);
    }

    .stat-icon.questions {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    }

    .stat-details {
        flex: 1;
        min-width: 0;
    }

    .stat-number {
        display: block;
        font-size: 18px;
        font-weight: 700;
        color: #1e293b;
        line-height: 1;
    }

    .stat-label {
        display: block;
        font-size: 11px;
        color: #64748b;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 2px;
    }

    .card-footer {
        padding: 16px 24px;
        background: #f8fafc;
        border-top: 1px solid #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .created-date {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #64748b;
        font-size: 13px;
        font-weight: 500;
    }

    .created-date i {
        color: #94a3b8;
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-badge.active {
        background: #dcfce7;
        color: #166534;
        border: 1px solid #bbf7d0;
    }

    .status-badge.inactive {
        background: #fef2f2;
        color: #991b1b;
        border: 1px solid #fecaca;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .department-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .dept-info {
            min-width: auto;
        }

        .dept-stats {
            justify-content: flex-start;
            gap: 1rem;
        }

        .dept-actions {
            align-self: flex-end;
        }
    }

    @media (max-width: 480px) {
        .departments-list {
            margin-top: 16px;
        }

        .department-row {
            padding: 1rem;
        }

        .dept-stats {
            flex-direction: column;
            gap: 0.5rem;
        }

        .stat-item {
            min-width: auto;
        }

        .stat-label {
            font-size: 10px;
        }
    }
    </style>

    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="../assets/js/departments.js"></script>
</body>
</html>
