<?php
/**
 * Database Configuration for Streamlined AI-Powered LMS
 * Auto-generated by setup.php - Simplified Admin Interface
 */

// Set timezone to Nigeria (West Africa Time)
date_default_timezone_set('Africa/Lagos');

// Database connection constants
define('DB_HOST', 'localhost');
define('DB_NAME', 'lms');
define('DB_USER', 'root');
define('DB_PASS', '');

// Database connection class
class Database {
    private static $instance = null;
    private $connection;

    private function __construct() {
        try {
            $this->connection = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );

            // Set MySQL timezone to match PHP timezone
            $this->connection->exec("SET time_zone = '+01:00'");
        } catch (PDOException $e) {
            die('Database connection failed: ' . $e->getMessage());
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->connection;
    }
}

// Utility functions
function getConnection() {
    return Database::getInstance()->getConnection();
}

function getPDO() {
    return Database::getInstance()->getConnection();
}

function executeQuery($sql, $params = []) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log('Database query error: ' . $e->getMessage());
        return false;
    }
}

function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    if ($stmt) {
        $result = $stmt->fetch();
        return $result !== false ? $result : [];
    }
    return [];
}

function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : [];
}

function insertRecord($table, $data) {
    $columns = implode(',', array_keys($data));
    $placeholders = ':' . implode(', :', array_keys($data));

    $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
    $stmt = executeQuery($sql, $data);

    if ($stmt) {
        return getConnection()->lastInsertId();
    }
    return false;
}

function beginTransaction() {
    return getConnection()->beginTransaction();
}

function commitTransaction() {
    return getConnection()->commit();
}

function rollbackTransaction() {
    return getConnection()->rollback();
}

// Session management
function startSecureSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

// Password hashing
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Input sanitization
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Secure hash generation (alias for hashPassword for consistency)
function generateSecureHash($password) {
    return hashPassword($password);
}

// Update record function
function updateRecord($table, $data, $where = '', $whereParams = []) {
    try {
        $setParts = [];
        foreach (array_keys($data) as $key) {
            $setParts[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setParts);

        $sql = "UPDATE {$table} SET {$setClause}";
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }

        $params = array_merge($data, $whereParams);
        $stmt = executeQuery($sql, $params);

        return $stmt !== false;
    } catch (Exception $e) {
        error_log('Update record error: ' . $e->getMessage());
        return false;
    }
}

// Generate session ID
function generateSessionId() {
    return bin2hex(random_bytes(32));
}

// Notification system functions
function createNotification($studentId, $type, $title, $message) {
    return insertRecord('notifications', [
        'student_id' => $studentId,
        'type' => $type,
        'title' => $title,
        'message' => $message,
        'is_read' => false
    ]);
}

function getUnreadNotificationCount($studentId) {
    $result = fetchOne("SELECT COUNT(*) as count FROM notifications WHERE student_id = :student_id AND is_read = 0",
        ['student_id' => $studentId]);
    return $result['count'] ?? 0;
}

// Require login function
function requireLogin($userType = null) {
    startSecureSession();

    if (!isset($_SESSION['user_type'])) {
        if ($userType === 'admin') {
            header('Location: ../admin/login.php');
        } else {
            header('Location: ../student/login.php');
        }
        exit();
    }

    if ($userType && $_SESSION['user_type'] !== $userType) {
        if ($userType === 'admin') {
            header('Location: ../admin/login.php');
        } else {
            header('Location: ../student/login.php');
        }
        exit();
    }
}
?>