<?php
/**
 * AJAX endpoint for refreshing dashboard statistics
 */

require_once '../../config/database.php';

header('Content-Type: application/json');
ob_start();

// Check if admin is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
    ob_clean();
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

try {
    // Get updated dashboard statistics
    $stats = [
        'total_students' => fetchOne("SELECT COUNT(*) as count FROM students")['count'] ?? 0,
        'approved_students' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 1")['count'] ?? 0,
        'pending_approvals' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 0")['count'] ?? 0,
        'total_departments' => fetchOne("SELECT COUNT(*) as count FROM departments")['count'] ?? 0,
        'total_games_today' => fetchOne("SELECT COUNT(*) as count FROM game_sessions WHERE DATE(created_at) = CURDATE()")['count'] ?? 0
    ];

    // Get additional real-time data
    $additionalData = [
        'recent_registrations_count' => fetchOne("SELECT COUNT(*) as count FROM students WHERE DATE(registration_date) = CURDATE()")['count'] ?? 0,
        'game_sessions_last_hour' => fetchOne("SELECT COUNT(*) as count FROM game_sessions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)")['count'] ?? 0,
        'avg_game_score_today' => fetchOne("SELECT AVG(points_earned) as avg_score FROM game_sessions WHERE DATE(created_at) = CURDATE() AND status = 'completed'")['avg_score'] ?? 0
    ];

    ob_clean();
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'additional' => $additionalData,
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    ob_clean();
    error_log("Stats refresh error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to refresh statistics',
        'timestamp' => time()
    ]);
}
?>
