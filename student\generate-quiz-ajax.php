<?php
/**
 * AJAX Quiz Generation with Progress Updates
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';
require_once '../config/gemini-api.php';

// Set JSON header
header('Content-Type: application/json');

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

$difficulty = $_POST['difficulty'] ?? 'easy';
$questionCount = (int)($_POST['question_count'] ?? 10);

// Validate inputs
$validDifficulties = ['easy', 'medium', 'hard'];
if (!in_array($difficulty, $validDifficulties)) {
    $difficulty = 'easy';
}

if ($questionCount < 1 || $questionCount > 20) {
    $questionCount = 10;
}

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $_SESSION['user_id']]);

if (!$student) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Student not found']);
    exit();
}

try {
    // Check for existing in-progress quiz
    $existingQuiz = fetchOne("
        SELECT * FROM quiz_sessions 
        WHERE student_id = :student_id AND status = 'in_progress'
        ORDER BY created_at DESC LIMIT 1
    ", ['student_id' => $student['id']]);

    if ($existingQuiz) {
        echo json_encode([
            'success' => false, 
            'error' => 'You have an existing quiz in progress',
            'redirect' => 'quick-game.php?quiz_id=' . $existingQuiz['id']
        ]);
        exit();
    }

    // Generate questions with progress updates
    $progressKey = 'quiz_generation_progress_' . session_id();
    $_SESSION[$progressKey] = [
        'step' => 1,
        'message' => 'Preparing AI generation parameters...',
        'progress' => 10
    ];

    // Step 1: Prepare generation parameters
    $department = fetchOne("SELECT * FROM departments WHERE id = :id", ['id' => $student['department_id']]);
    $academicLevel = fetchOne("SELECT * FROM academic_levels WHERE id = :id", ['id' => $student['academic_level_id']]);

    $_SESSION[$progressKey] = [
        'step' => 2,
        'message' => 'Connecting to Gemini AI...',
        'progress' => 30
    ];

    // Step 2: Generate questions using Gemini API
    $difficultyPrompt = "Generate questions at {$difficulty} difficulty level. ";
    $questions = generateQuestionsWithGemini($department, $academicLevel, $questionCount, $difficultyPrompt);

    if (!$questions || !is_array($questions)) {
        throw new Exception('Invalid response from AI generation');
    }

    // Add difficulty field to each question
    foreach ($questions as &$question) {
        $question['difficulty'] = $difficulty;
    }
    unset($question); // Break reference

    $_SESSION[$progressKey] = [
        'step' => 3,
        'message' => 'Processing generated questions...',
        'progress' => 70
    ];

    if (empty($questions)) {
        throw new Exception('Failed to generate questions with AI');
    }

    // Step 3: Create quiz session
    $quizSessionId = generateSessionId();
    
    executeQuery("
        INSERT INTO quiz_sessions (
            id, student_id, department_id, academic_level_id, 
            total_questions, status, difficulty, created_at
        ) VALUES (
            :id, :student_id, :department_id, :academic_level_id,
            :total_questions, 'in_progress', :difficulty, NOW()
        )
    ", [
        'id' => $quizSessionId,
        'student_id' => $student['id'],
        'department_id' => $student['department_id'],
        'academic_level_id' => $student['academic_level_id'],
        'total_questions' => count($questions),
        'difficulty' => $difficulty
    ]);

    // Store questions in session for better performance
    $_SESSION['quiz_questions_' . $quizSessionId] = $questions;
    
    // Create lightweight quiz_questions entries for compatibility
    foreach ($questions as $index => $question) {
        $tempQuestionId = 'temp_' . $quizSessionId . '_' . ($index + 1);
        
        executeQuery("
            INSERT INTO quiz_questions (
                quiz_session_id, question_id, question_number
            ) VALUES (
                :quiz_session_id, :question_id, :question_number
            )
        ", [
            'quiz_session_id' => $quizSessionId,
            'question_id' => $tempQuestionId,
            'question_number' => $index + 1
        ]);
    }

    $_SESSION[$progressKey] = [
        'step' => 4,
        'message' => 'Quiz ready! Redirecting...',
        'progress' => 100
    ];

    // Clean up progress data
    unset($_SESSION[$progressKey]);

    echo json_encode([
        'success' => true,
        'message' => 'Quiz generated successfully!',
        'quiz_id' => $quizSessionId,
        'redirect' => 'interactive-take.php?quiz_id=' . $quizSessionId,
        'questions_count' => count($questions)
    ]);

} catch (Exception $e) {
    error_log("Quiz generation error: " . $e->getMessage());
    
    // Clean up progress data
    if (isset($progressKey)) {
        unset($_SESSION[$progressKey]);
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'error' => 'Failed to generate quiz: ' . $e->getMessage()
    ]);
}

/**
 * Generate session ID
 */
function generateSessionId() {
    return 'quiz_' . uniqid() . '_' . time();
}
?>
