<?php
/**
 * API endpoint for fetching real-time student scores across all game modes
 * Returns student matriculation numbers and their scores/levels for Quick Game, Endless Mode, and Mission Mode
 */

require_once '../../config/database.php';

// Require admin login
requireLogin('admin');

header('Content-Type: application/json');

try {
    $mode = $_GET['mode'] ?? 'quick';
    
    if (!in_array($mode, ['quick', 'endless', 'mission'])) {
        throw new Exception('Invalid game mode');
    }
    
    $students = [];
    
    switch ($mode) {
        case 'quick':
            // Get Quick Game scores - best score per student
            $students = fetchAll("
                SELECT 
                    s.id,
                    s.first_name,
                    s.last_name,
                    s.student_id,
                    MAX(gs.points_earned) as best_score,
                    COUNT(gs.id) as games_played,
                    AVG(gs.correct_answers * 100.0 / gs.questions_answered) as avg_accuracy
                FROM students s
                LEFT JOIN game_sessions gs ON s.id = gs.student_id AND gs.game_mode = 'quick' AND gs.status = 'completed'
                WHERE s.is_approved = 1
                GROUP BY s.id, s.first_name, s.last_name, s.student_id
                HAVING games_played > 0
                ORDER BY best_score DESC, games_played DESC
                LIMIT 20
            ");
            break;
            
        case 'endless':
            // Get Endless Mode scores - highest score and level per student
            $students = fetchAll("
                SELECT 
                    s.id,
                    s.first_name,
                    s.last_name,
                    s.student_id,
                    MAX(gs.points_earned) as highest_score,
                    MAX(gs.level_reached) as current_level,
                    COUNT(gs.id) as games_played
                FROM students s
                LEFT JOIN game_sessions gs ON s.id = gs.student_id AND gs.game_mode = 'endless'
                WHERE s.is_approved = 1
                GROUP BY s.id, s.first_name, s.last_name, s.student_id
                HAVING games_played > 0
                ORDER BY highest_score DESC, current_level DESC
                LIMIT 20
            ");
            break;
            
        case 'mission':
            // Get Mission Mode progress - current level and completed missions
            $students = fetchAll("
                SELECT 
                    s.id,
                    s.first_name,
                    s.last_name,
                    s.student_id,
                    COALESCE(MAX(smp.level_id), 1) as current_level,
                    COUNT(CASE WHEN smp.status = 'completed' THEN 1 END) as missions_completed,
                    SUM(CASE WHEN smp.status = 'completed' THEN smp.best_score ELSE 0 END) as total_mission_score
                FROM students s
                LEFT JOIN student_mission_progress smp ON s.id = smp.student_id
                WHERE s.is_approved = 1
                GROUP BY s.id, s.first_name, s.last_name, s.student_id
                ORDER BY current_level DESC, missions_completed DESC, total_mission_score DESC
                LIMIT 20
            ");
            break;
    }
    
    // If no students found in game_sessions, get all approved students with zero scores
    if (empty($students)) {
        $students = fetchAll("
            SELECT 
                s.id,
                s.first_name,
                s.last_name,
                s.student_id,
                0 as best_score,
                0 as highest_score,
                1 as current_level,
                0 as games_played,
                0 as missions_completed,
                0 as total_mission_score,
                0 as avg_accuracy
            FROM students s
            WHERE s.is_approved = 1
            ORDER BY s.first_name, s.last_name
            LIMIT 20
        ");
    }
    
    echo json_encode([
        'success' => true,
        'mode' => $mode,
        'students' => $students,
        'count' => count($students),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Student scores API error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to load student scores: ' . $e->getMessage(),
        'students' => []
    ]);
}
?>
