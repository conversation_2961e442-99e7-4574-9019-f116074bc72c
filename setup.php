<?php
/**
 * LMS Setup Wizard
 * Professional Installation and Configuration Tool
 * Ogbonnaya Onu Polytechnic, Aba
 */

// Prevent direct access if already setup
// Allow setup to run again by checking for a force parameter or removing the flag file
if (file_exists('config/setup_complete.flag') && !isset($_GET['force'])) {
    // Show option to re-run setup or go to index
    if (!isset($_GET['confirm_reset'])) {
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Setup Already Complete</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
                .setup-card { background: rgba(255,255,255,0.95); backdrop-filter: blur(20px); border-radius: 24px; }
            </style>
        </head>
        <body class="d-flex align-items-center justify-content-center">
            <div class="setup-card p-5 text-center" style="max-width: 500px;">
                <h2 class="mb-3">Setup Already Complete</h2>
                <p class="mb-4">The LMS has already been set up. What would you like to do?</p>
                <div class="d-grid gap-2">
                    <a href="index.php" class="btn btn-primary">Go to Homepage</a>
                    <a href="admin/login.php" class="btn btn-success">Admin Login</a>
                    <a href="setup.php?confirm_reset=1" class="btn btn-warning">Re-run Setup (This will reset the database)</a>
                </div>
            </div>
        </body>
        </html>
        <?php
        exit();
    } else {
        // User confirmed reset, remove the flag file
        if (file_exists('config/setup_complete.flag')) {
            unlink('config/setup_complete.flag');
        }
        // Redirect to setup without the confirm parameter
        header('Location: setup.php');
        exit();
    }
}

// Initialize session
session_start();

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'test_connection':
            echo json_encode(testDatabaseConnection($_POST));
            break;
        case 'create_database':
            echo json_encode(createDatabase($_POST));
            break;
        case 'setup_tables':
            echo json_encode(setupTables());
            break;
        case 'create_admin':
            echo json_encode(createAdmin($_POST));
            break;
        case 'finalize_setup':
            echo json_encode(finalizeSetup());
            break;
    }
    exit();
}

function testDatabaseConnection($data) {
    try {
        $host = $data['db_host'] ?? 'localhost';
        $username = $data['db_username'] ?? 'root';
        $password = $data['db_password'] ?? '';
        
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        return ['success' => true, 'message' => 'Database connection successful!'];
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Connection failed: ' . $e->getMessage()];
    }
}

function createDatabase($data) {
    try {
        $host = $data['db_host'] ?? 'localhost';
        $username = $data['db_username'] ?? 'root';
        $password = $data['db_password'] ?? '';
        $dbname = 'lms';
        
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // Update config file
        $configContent = generateConfigFile($host, $dbname, $username, $password);
        file_put_contents('config/database.php', $configContent);
        
        // Store connection details in session
        $_SESSION['db_config'] = compact('host', 'dbname', 'username', 'password');
        
        return ['success' => true, 'message' => 'Database created successfully!'];
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database creation failed: ' . $e->getMessage()];
    }
}

function setupTables() {
    try {
        $config = $_SESSION['db_config'];
        $pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", 
                      $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Read and execute schema
        $schema = file_get_contents('database/schema.sql');
        $schema = str_replace('lms_ogbonnaya_onu', $config['dbname'], $schema);
        $schema = str_replace('USE lms_ogbonnaya_onu;', '', $schema);
        
        // Execute schema in parts
        $statements = explode(';', $schema);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // Execute initial data
        $initialData = file_get_contents('database/initial_data.sql');
        $initialData = str_replace('USE lms_ogbonnaya_onu;', '', $initialData);
        
        $statements = explode(';', $initialData);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // Execute game system
        $gameSystem = file_get_contents('sql/game_system.sql');
        $statements = explode(';', $gameSystem);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !strpos($statement, 'INSERT INTO student_achievements')) {
                $pdo->exec($statement);
            }
        }
        
        return ['success' => true, 'message' => 'Database tables created successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Table creation failed: ' . $e->getMessage()];
    }
}

function createAdmin($data) {
    try {
        $config = $_SESSION['db_config'];
        $pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", 
                      $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $username = $data['admin_username'];
        $email = $data['admin_email'];
        $password = password_hash($data['admin_password'], PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO admins (username, email, password_hash, is_setup) VALUES (?, ?, ?, 1)");
        $stmt->execute([$username, $email, $password]);
        
        return ['success' => true, 'message' => 'Admin account created successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Admin creation failed: ' . $e->getMessage()];
    }
}

function finalizeSetup() {
    try {
        // Create setup complete flag
        file_put_contents('config/setup_complete.flag', date('Y-m-d H:i:s'));
        
        // Clear session
        unset($_SESSION['db_config']);
        
        return ['success' => true, 'message' => 'Setup completed successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Setup finalization failed: ' . $e->getMessage()];
    }
}

function generateConfigFile($host, $dbname, $username, $password) {
    return "<?php
/**
 * Database Configuration for Streamlined AI-Powered LMS
 * Auto-generated by setup.php - Simplified Admin Interface
 */

// Set timezone to Nigeria (West Africa Time)
date_default_timezone_set('Africa/Lagos');

// Database connection constants
define('DB_HOST', '$host');
define('DB_NAME', '$dbname');
define('DB_USER', '$username');
define('DB_PASS', '$password');

// Database connection class
class Database {
    private static \$instance = null;
    private \$connection;

    private function __construct() {
        try {
            \$this->connection = new PDO(
                \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );

            // Set MySQL timezone to match PHP timezone
            \$this->connection->exec(\"SET time_zone = '+01:00'\");
        } catch (PDOException \$e) {
            die('Database connection failed: ' . \$e->getMessage());
        }
    }

    public static function getInstance() {
        if (self::\$instance === null) {
            self::\$instance = new self();
        }
        return self::\$instance;
    }

    public function getConnection() {
        return \$this->connection;
    }
}

// Utility functions
function getConnection() {
    return Database::getInstance()->getConnection();
}

function getPDO() {
    return Database::getInstance()->getConnection();
}

function executeQuery(\$sql, \$params = []) {
    try {
        \$pdo = getConnection();
        \$stmt = \$pdo->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt;
    } catch (PDOException \$e) {
        error_log('Database query error: ' . \$e->getMessage());
        return false;
    }
}

function fetchOne(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    if (\$stmt) {
        \$result = \$stmt->fetch();
        return \$result !== false ? \$result : [];
    }
    return [];
}

function fetchAll(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt ? \$stmt->fetchAll() : [];
}

function insertRecord(\$table, \$data) {
    \$columns = implode(',', array_keys(\$data));
    \$placeholders = ':' . implode(', :', array_keys(\$data));

    \$sql = \"INSERT INTO {\$table} ({\$columns}) VALUES ({\$placeholders})\";
    \$stmt = executeQuery(\$sql, \$data);

    if (\$stmt) {
        return getConnection()->lastInsertId();
    }
    return false;
}

function beginTransaction() {
    return getConnection()->beginTransaction();
}

function commitTransaction() {
    return getConnection()->commit();
}

function rollbackTransaction() {
    return getConnection()->rollback();
}

// Session management
function startSecureSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

// Password hashing
function hashPassword(\$password) {
    return password_hash(\$password, PASSWORD_DEFAULT);
}

function verifyPassword(\$password, \$hash) {
    return password_verify(\$password, \$hash);
}

// Input sanitization
function sanitizeInput(\$input) {
    if (is_array(\$input)) {
        return array_map('sanitizeInput', \$input);
    }
    return htmlspecialchars(trim(\$input), ENT_QUOTES, 'UTF-8');
}

// Secure hash generation (alias for hashPassword for consistency)
function generateSecureHash(\$password) {
    return hashPassword(\$password);
}

// Update record function
function updateRecord(\$table, \$data, \$where = '', \$whereParams = []) {
    try {
        \$setParts = [];
        foreach (array_keys(\$data) as \$key) {
            \$setParts[] = \"{\$key} = :{\$key}\";
        }
        \$setClause = implode(', ', \$setParts);

        \$sql = \"UPDATE {\$table} SET {\$setClause}\";
        if (!empty(\$where)) {
            \$sql .= \" WHERE {\$where}\";
        }

        \$params = array_merge(\$data, \$whereParams);
        \$stmt = executeQuery(\$sql, \$params);

        return \$stmt !== false;
    } catch (Exception \$e) {
        error_log('Update record error: ' . \$e->getMessage());
        return false;
    }
}

// Generate session ID
function generateSessionId() {
    return bin2hex(random_bytes(32));
}

// Notification system functions
function createNotification(\$studentId, \$type, \$title, \$message) {
    return insertRecord('notifications', [
        'student_id' => \$studentId,
        'type' => \$type,
        'title' => \$title,
        'message' => \$message,
        'is_read' => false
    ]);
}

function getUnreadNotificationCount(\$studentId) {
    \$result = fetchOne(\"SELECT COUNT(*) as count FROM notifications WHERE student_id = :student_id AND is_read = 0\",
        ['student_id' => \$studentId]);
    return \$result['count'] ?? 0;
}

// Require login function
function requireLogin(\$userType = null) {
    startSecureSession();

    if (!isset(\$_SESSION['user_type'])) {
        if (\$userType === 'admin') {
            header('Location: ../admin/login.php');
        } else {
            header('Location: ../student/login.php');
        }
        exit();
    }

    if (\$userType && \$_SESSION['user_type'] !== \$userType) {
        if (\$userType === 'admin') {
            header('Location: ../admin/login.php');
        } else {
            header('Location: ../student/login.php');
        }
        exit();
    }
}
?>";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LMS Setup Wizard - Ogbonnaya Onu Polytechnic</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-light: #6366f1;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --dark-text: #1e293b;
            --glass-bg: rgba(255, 255, 255, 0.95);
            --shadow-light: rgba(255, 255, 255, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            color: var(--dark-text);
            overflow-x: hidden;
        }

        .setup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            position: relative;
        }

        .setup-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
        }

        .setup-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.12),
                inset 0 1px 0 var(--shadow-light);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            max-height: 95vh;
            position: relative;
            z-index: 1;
        }

        .setup-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 25px 30px;
            text-align: center;
            position: relative;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .setup-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .setup-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .setup-header p {
            font-size: 0.95rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            font-weight: 400;
        }

        .setup-body {
            padding: 25px 30px;
            max-height: calc(95vh - 120px);
            overflow-y: auto;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 25px;
            padding: 0 20px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 8px;
            position: relative;
        }

        .step-number {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(226, 232, 240, 0.8);
            color: var(--secondary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
            z-index: 2;
        }

        .step.active .step-number {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            transform: scale(1.15);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .step.completed .step-number {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .step-connector {
            width: 50px;
            height: 3px;
            background: rgba(226, 232, 240, 0.6);
            margin: 0 8px;
            border-radius: 2px;
            transition: all 0.4s ease;
            position: relative;
            z-index: 1;
        }

        .step.completed + .step .step-connector {
            background: linear-gradient(90deg, var(--success-color), #059669);
        }

        .step-content {
            display: none;
            min-height: 400px;
        }

        .step-content.active {
            display: block;
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 6px;
            display: block;
            font-size: 14px;
        }

        .form-control {
            border: 2px solid rgba(226, 232, 240, 0.8);
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 15px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            width: 100%;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
            background: rgba(255, 255, 255, 0.95);
            outline: none;
        }

        .form-control:hover {
            border-color: rgba(79, 70, 229, 0.3);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.2);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .btn-outline-secondary {
            border: 2px solid rgba(100, 116, 139, 0.3);
            color: var(--secondary-color);
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
        }

        .btn-outline-secondary:hover {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .btn-lg {
            padding: 14px 28px;
            font-size: 15px;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 14px 18px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.15);
            color: #065f46;
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.15);
            color: #991b1b;
            border-left: 4px solid var(--danger-color);
        }

        .progress {
            height: 6px;
            border-radius: 10px;
            background: rgba(226, 232, 240, 0.6);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-bar {
            border-radius: 10px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }

        .feature-card {
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            padding: 18px;
            border-radius: 16px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(99, 102, 241, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(79, 70, 229, 0.15);
            border-color: rgba(79, 70, 229, 0.3);
        }

        .feature-icon {
            font-size: 2.2rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
        }

        .feature-card h5 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .feature-card p {
            font-size: 12px;
            color: var(--secondary-color);
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 30px 20px;
        }

        .spinner-border {
            width: 2.5rem;
            height: 2.5rem;
            border-width: 0.25em;
            border-color: var(--primary-color);
            border-right-color: transparent;
        }

        .completion-animation {
            text-align: center;
            padding: 30px 20px;
        }

        .completion-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, var(--success-color), #059669);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 16px;
            animation: bounceIn 1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes bounceIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .text-muted {
            font-size: 12px;
            color: var(--secondary-color);
            margin-top: 4px;
        }

        .row {
            margin: 0 -8px;
        }

        .col-md-6 {
            padding: 0 8px;
        }

        @media (max-width: 768px) {
            .setup-card {
                margin: 10px;
                max-height: 98vh;
            }

            .setup-header {
                padding: 20px 25px;
            }

            .setup-header h1 {
                font-size: 1.75rem;
            }

            .setup-body {
                padding: 20px 25px;
                max-height: calc(98vh - 100px);
            }

            .step-indicator {
                flex-wrap: wrap;
                gap: 8px;
                margin-bottom: 20px;
            }

            .step-connector {
                display: none;
            }

            .feature-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .step-content {
                min-height: 350px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        @media (max-width: 480px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }

            .setup-container {
                padding: 8px;
            }
        }

        /* Password Toggle Styles */
        .password-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .password-input-wrapper input {
            padding-right: 45px;
        }

        .password-toggle-btn {
            position: absolute;
            right: 10px;
            background: none;
            border: none;
            color: var(--secondary-color);
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.2s ease;
            z-index: 10;
        }

        .password-toggle-btn:hover {
            color: var(--primary-color);
            background-color: rgba(79, 70, 229, 0.1);
        }

        .password-toggle-btn:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        }

        .password-toggle-btn i {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <h1><i class="fas fa-graduation-cap"></i> LMS Setup Wizard</h1>
                <p>Ogbonnaya Onu Polytechnic, Aba - Learning Management System</p>
            </div>

            <div class="setup-body">
                <!-- Progress Bar -->
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: 20%" id="progressBar"></div>
                </div>

                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step active" data-step="1">
                        <div class="step-number">1</div>
                    </div>
                    <div class="step-connector"></div>
                    <div class="step" data-step="2">
                        <div class="step-number">2</div>
                    </div>
                    <div class="step-connector"></div>
                    <div class="step" data-step="3">
                        <div class="step-number">3</div>
                    </div>
                    <div class="step-connector"></div>
                    <div class="step" data-step="4">
                        <div class="step-number">4</div>
                    </div>
                    <div class="step-connector"></div>
                    <div class="step" data-step="5">
                        <div class="step-number">5</div>
                    </div>
                </div>

                <!-- Step 1: Welcome -->
                <div class="step-content active" id="step1">
                    <div class="text-center">
                        <h2 class="mb-3" style="font-size: 1.5rem; font-weight: 700;">Welcome to LMS Setup</h2>
                        <p class="mb-3" style="font-size: 0.95rem; color: var(--secondary-color);">This wizard will guide you through setting up your streamlined Learning Management System for Ogbonnaya Onu Polytechnic with AI-powered question generation.</p>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <h5>Database Setup</h5>
                                <p>Automatic database creation and configuration</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-table"></i>
                                </div>
                                <h5>Table Creation</h5>
                                <p>Complete schema setup with all required tables</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <h5>Admin Account</h5>
                                <p>Streamlined admin panel access</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <h5>AI-Powered</h5>
                                <p>Automatic question generation during gameplay</p>
                            </div>
                        </div>

                        <button type="button" class="btn btn-primary btn-lg" onclick="nextStep()">
                            <i class="fas fa-arrow-right"></i> Start Setup
                        </button>
                    </div>
                </div>

                <!-- Step 2: Database Configuration -->
                <div class="step-content" id="step2">
                    <h2 class="mb-3" style="font-size: 1.4rem; font-weight: 700;">Database Configuration</h2>
                    <p class="mb-3" style="font-size: 0.9rem; color: var(--secondary-color);">Configure your database connection settings. The system will create a database named 'lms' with a streamlined schema for efficient management.</p>

                    <div id="dbAlert"></div>

                    <form id="dbForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Database Host</label>
                                    <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                    <small class="text-muted">Usually 'localhost' for local installations</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Database Username</label>
                                    <input type="text" class="form-control" id="db_username" name="db_username" value="root" required>
                                    <small class="text-muted">Your MySQL username</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Database Password</label>
                            <div class="password-input-wrapper">
                                <input type="password" class="form-control" id="db_password" name="db_password" placeholder="Enter your MySQL password">
                                <button type="button" class="password-toggle-btn" onclick="togglePassword('db_password')">
                                    <i class="fas fa-eye" id="db_password_icon"></i>
                                </button>
                            </div>
                            <small class="text-muted">Leave empty if no password is set</small>
                        </div>

                        <div class="d-flex gap-3 flex-wrap">
                            <button type="button" class="btn btn-outline-secondary" onclick="testConnection()">
                                <i class="fas fa-plug"></i> Test Connection
                            </button>
                            <button type="button" class="btn btn-primary" onclick="createDatabase()" id="createDbBtn" disabled>
                                <i class="fas fa-database"></i> Create Database
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Step 3: Table Setup -->
                <div class="step-content" id="step3">
                    <h2 class="mb-3" style="font-size: 1.4rem; font-weight: 700;">Database Tables Setup</h2>
                    <p class="mb-3" style="font-size: 0.9rem; color: var(--secondary-color);">Creating streamlined database schema with essential tables for efficient management...</p>

                    <div id="tableAlert"></div>

                    <div class="loading-spinner" id="tableLoading">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3" style="font-size: 0.9rem; color: var(--secondary-color);">Setting up database tables...</p>
                    </div>

                    <div class="text-center" id="tableSuccess" style="display: none;">
                        <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--success-color);"></i>
                        <h4 class="mt-3" style="color: var(--success-color); font-size: 1.2rem;">Tables Created Successfully!</h4>
                        <p style="font-size: 0.9rem; color: var(--secondary-color);">Streamlined database schema created with essential tables and departments.</p>
                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                            <i class="fas fa-arrow-right"></i> Continue
                        </button>
                    </div>
                </div>

                <!-- Step 4: Admin Account -->
                <div class="step-content" id="step4">
                    <h2 class="mb-3" style="font-size: 1.4rem; font-weight: 700;">Create Administrator Account</h2>
                    <p class="mb-3" style="font-size: 0.9rem; color: var(--secondary-color);">Set up your administrator account to access the streamlined admin panel with essential management features.</p>

                    <div id="adminAlert"></div>

                    <form id="adminForm">
                        <div class="form-group">
                            <label class="form-label">Admin Username</label>
                            <input type="text" class="form-control" id="admin_username" name="admin_username" value="admin" required>
                            <small class="text-muted">This will be your login username</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                            <small class="text-muted">For password recovery and notifications</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Password</label>
                                    <div class="password-input-wrapper">
                                        <input type="password" class="form-control" id="admin_password" name="admin_password" required minlength="8">
                                        <button type="button" class="password-toggle-btn" onclick="togglePassword('admin_password')">
                                            <i class="fas fa-eye" id="admin_password_icon"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">Minimum 8 characters</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Confirm Password</label>
                                    <div class="password-input-wrapper">
                                        <input type="password" class="form-control" id="admin_password_confirm" required>
                                        <button type="button" class="password-toggle-btn" onclick="togglePassword('admin_password_confirm')">
                                            <i class="fas fa-eye" id="admin_password_confirm_icon"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">Re-enter your password</small>
                                </div>
                            </div>
                        </div>

                        <button type="button" class="btn btn-primary" onclick="createAdmin()">
                            <i class="fas fa-user-plus"></i> Create Admin Account
                        </button>
                    </form>
                </div>

                <!-- Step 5: Completion -->
                <div class="step-content" id="step5">
                    <div class="completion-animation">
                        <div class="completion-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2 class="mb-3" style="color: var(--success-color); font-size: 1.5rem; font-weight: 700;">Setup Complete!</h2>
                        <p class="mb-3" style="font-size: 0.95rem; color: var(--secondary-color);">Your LMS has been successfully installed and configured.</p>

                        <div class="alert alert-success">
                            <h5 style="font-size: 1rem; margin-bottom: 12px;"><i class="fas fa-info-circle"></i> What's Next?</h5>
                            <ul class="mb-0 text-start" style="font-size: 0.85rem; padding-left: 20px;">
                                <li>Your database 'lms' has been created with all necessary tables</li>
                                <li>Administrator account has been set up</li>
                                <li>All 31 departments have been configured</li>
                                <li>Game system and analytics are ready</li>
                                <li>AI questions are auto-generated during student gameplay</li>
                                <li>Simplified admin panel with essential features only</li>
                                <li>The system is ready for student registration</li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <h6 style="font-size: 0.9rem; margin-bottom: 8px;"><i class="fas fa-cog"></i> Admin Panel Features</h6>
                            <div class="row text-start" style="font-size: 0.8rem;">
                                <div class="col-md-6">
                                    <ul style="margin-bottom: 0; padding-left: 20px;">
                                        <li><strong>Dashboard</strong> - System overview</li>
                                        <li><strong>Students</strong> - Manage registrations</li>
                                        <li><strong>Departments</strong> - Academic structure</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul style="margin-bottom: 0; padding-left: 20px;">
                                        <li><strong>Game Analytics</strong> - Performance tracking</li>
                                        <li><strong>Settings</strong> - System configuration</li>
                                        <li><strong>Clean Interface</strong> - Simplified navigation</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <a href="index.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-home"></i> Go to Homepage
                            </a>
                            <a href="admin/login.php" class="btn btn-success btn-lg">
                                <i class="fas fa-sign-in-alt"></i> Admin Login
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        const totalSteps = 5;

        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        function updateStepIndicator() {
            document.querySelectorAll('.step').forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');

                if (stepNumber < currentStep) {
                    step.classList.add('completed');
                } else if (stepNumber === currentStep) {
                    step.classList.add('active');
                }
            });
        }

        function showStep(step) {
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById('step' + step).classList.add('active');
            updateProgress();
            updateStepIndicator();
        }

        function nextStep() {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);

                // Auto-start table setup when reaching step 3
                if (currentStep === 3) {
                    setTimeout(setupTables, 1000);
                }
            }
        }

        function showAlert(containerId, type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.getElementById(containerId).innerHTML = alertHtml;
        }

        function testConnection() {
            const formData = new FormData(document.getElementById('dbForm'));
            formData.append('action', 'test_connection');

            fetch('setup.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('dbAlert', 'success', data.message);
                    document.getElementById('createDbBtn').disabled = false;
                } else {
                    showAlert('dbAlert', 'danger', data.message);
                    document.getElementById('createDbBtn').disabled = true;
                }
            })
            .catch(error => {
                showAlert('dbAlert', 'danger', 'Connection test failed: ' + error.message);
            });
        }

        function createDatabase() {
            const formData = new FormData(document.getElementById('dbForm'));
            formData.append('action', 'create_database');

            fetch('setup.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('dbAlert', 'success', data.message);
                    setTimeout(() => {
                        nextStep();
                    }, 1500);
                } else {
                    showAlert('dbAlert', 'danger', data.message);
                }
            })
            .catch(error => {
                showAlert('dbAlert', 'danger', 'Database creation failed: ' + error.message);
            });
        }

        function setupTables() {
            document.getElementById('tableLoading').style.display = 'block';
            document.getElementById('tableSuccess').style.display = 'none';

            const formData = new FormData();
            formData.append('action', 'setup_tables');

            fetch('setup.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('tableLoading').style.display = 'none';

                if (data.success) {
                    document.getElementById('tableSuccess').style.display = 'block';
                    showAlert('tableAlert', 'success', data.message);
                } else {
                    showAlert('tableAlert', 'danger', data.message);
                }
            })
            .catch(error => {
                document.getElementById('tableLoading').style.display = 'none';
                showAlert('tableAlert', 'danger', 'Table setup failed: ' + error.message);
            });
        }

        function createAdmin() {
            const password = document.getElementById('admin_password').value;
            const confirmPassword = document.getElementById('admin_password_confirm').value;

            if (password !== confirmPassword) {
                showAlert('adminAlert', 'danger', 'Passwords do not match!');
                return;
            }

            if (password.length < 8) {
                showAlert('adminAlert', 'danger', 'Password must be at least 8 characters long!');
                return;
            }

            const formData = new FormData(document.getElementById('adminForm'));
            formData.append('action', 'create_admin');

            fetch('setup.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('adminAlert', 'success', data.message);
                    setTimeout(() => {
                        finalizeSetup();
                    }, 1500);
                } else {
                    showAlert('adminAlert', 'danger', data.message);
                }
            })
            .catch(error => {
                showAlert('adminAlert', 'danger', 'Admin creation failed: ' + error.message);
            });
        }

        function finalizeSetup() {
            const formData = new FormData();
            formData.append('action', 'finalize_setup');

            fetch('setup.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    nextStep();
                } else {
                    showAlert('adminAlert', 'danger', 'Setup finalization failed: ' + data.message);
                }
            })
            .catch(error => {
                showAlert('adminAlert', 'danger', 'Setup finalization failed: ' + error.message);
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            updateStepIndicator();

            // Auto-test connection with default values
            setTimeout(testConnection, 1000);
        });

        // Form validation
        const passwordConfirmField = document.getElementById('admin_password_confirm');
        if (passwordConfirmField) {
            passwordConfirmField.addEventListener('input', function() {
                const password = document.getElementById('admin_password').value;
                const confirmPassword = this.value;

                if (confirmPassword && password !== confirmPassword) {
                    this.setCustomValidity('Passwords do not match');
                    this.classList.add('is-invalid');
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('is-invalid');
                }
            });
        }

        // Prevent form submission on Enter key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.target.tagName === 'INPUT') {
                e.preventDefault();
            }
        });

        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + '_icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
