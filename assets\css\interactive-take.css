/**
 * Interactive Quiz Taking CSS
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.quiz-container {
    max-width: 1000px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.quiz-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.pause-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 12px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pause-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.quiz-info h2 {
    font-size: 20px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
}

.quiz-info p {
    color: #718096;
    font-size: 14px;
}

.header-center {
    flex: 1;
    max-width: 300px;
    margin: 0 30px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-bar {
    flex: 1;
    height: 12px;
    background: #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    border-radius: 6px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 14px;
    font-weight: 600;
    color: #4a5568;
    min-width: 40px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.xp-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    padding: 8px 14px;
    border-radius: 20px;
    font-weight: 600;
    color: #8b5a00;
    font-size: 14px;
}

.timer {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #718096;
    font-weight: 500;
}

/* Main Content */
.quiz-main {
    flex: 1;
    padding: 40px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.question-container {
    width: 100%;
    max-width: 700px;
}

.question-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.difficulty-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy {
    background: #c6f6d5;
    color: #22543d;
}

.difficulty-medium {
    background: #fed7d7;
    color: #742a2a;
}

.difficulty-hard {
    background: #fbb6ce;
    color: #702459;
}

.question-number {
    color: #718096;
    font-weight: 500;
    font-size: 14px;
}

.question-text {
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
    line-height: 1.4;
}

/* Answer Options */
.options-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.option-card {
    background: white;
    border: 3px solid #e2e8f0;
    border-radius: 16px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    overflow: hidden;
}

.option-card:hover {
    border-color: #cbd5e0;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.option-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea10, #764ba210);
}

.option-card input[type="radio"] {
    display: none;
}

.option-letter {
    width: 40px;
    height: 40px;
    background: #f7fafc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: #4a5568;
    font-size: 16px;
    flex-shrink: 0;
}

.option-card.selected .option-letter {
    background: #667eea;
    color: white;
}

.option-text {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    color: #2d3748;
}

.option-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0;
    transition: all 0.3s ease;
}

.option-card.selected .option-indicator {
    background: #48bb78;
    opacity: 1;
}

/* Footer Navigation */
.quiz-footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
}

.footer-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.question-dots {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.question-dot {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    color: #718096;
    cursor: pointer;
    transition: all 0.3s ease;
}

.question-dot:hover {
    background: #cbd5e0;
    transform: scale(1.1);
}

.question-dot.current {
    background: #667eea;
    color: white;
}

.question-dot.answered {
    background: #48bb78;
    color: white;
}

.question-dot.answered.current {
    background: #667eea;
    box-shadow: 0 0 0 3px #48bb78;
}

/* Buttons */
.btn {
    border: none;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
    padding: 12px 24px;
    font-size: 14px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-secondary:hover:not(:disabled) {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

/* Celebration Overlay */
.celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.5s ease-out;
}

.celebration-overlay.hidden {
    display: none;
}

.celebration-content {
    text-align: center;
    color: white;
    animation: bounceIn 0.8s ease-out;
}

.celebration-emoji {
    font-size: 80px;
    margin-bottom: 20px;
    animation: bounce 1s infinite;
}

.celebration-text {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
}

.celebration-message {
    font-size: 18px;
    opacity: 0.9;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease-out;
}

.modal-header {
    padding: 30px 30px 0;
    text-align: center;
}

.modal-header h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
}

.modal-body {
    padding: 20px 30px;
    text-align: center;
}

.modal-body p {
    color: #718096;
    font-size: 16px;
    margin-bottom: 20px;
}

.pause-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 20px 0;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 32px;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
}

.modal-footer {
    padding: 0 30px 30px;
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    80% {
        transform: translateY(-10px);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .quiz-header {
        flex-direction: column;
        gap: 15px;
        padding: 15px 20px;
    }
    
    .header-center {
        margin: 0;
        max-width: 100%;
    }
    
    .header-right {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .quiz-main {
        padding: 20px 15px;
    }
    
    .question-card {
        padding: 20px;
    }
    
    .question-text {
        font-size: 20px;
    }
    
    .option-card {
        padding: 16px;
    }
    
    .quiz-footer {
        flex-direction: column;
        gap: 15px;
        padding: 15px 20px;
    }
    
    .question-dots {
        order: -1;
    }
    
    .footer-left,
    .footer-right {
        width: 100%;
        display: flex;
        justify-content: center;
    }
    
    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
    
    .pause-stats {
        gap: 20px;
    }
    
    .modal-footer {
        flex-direction: column;
    }
}
