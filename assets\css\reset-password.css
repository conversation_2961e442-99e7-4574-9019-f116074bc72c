/* Password Reset Styles for AI-Powered LMS */

.reset-card {
    max-width: 500px;
    margin: 0 auto;
}

.reset-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32px;
    padding: 24px;
    background: #f8fafc;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
}

.progress-step span {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.progress-step.active .step-number {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.progress-step.active span {
    color: #6366f1;
    font-weight: 600;
}

.progress-step.completed .step-number {
    background: #10b981;
    color: white;
    border-color: #10b981;
}

.progress-step.completed span {
    color: #10b981;
    font-weight: 600;
}

.progress-line {
    width: 60px;
    height: 2px;
    background: #e5e7eb;
    margin: 0 16px;
    transition: all 0.3s ease;
}

.progress-line.completed {
    background: #10b981;
}

.reset-step {
    margin-bottom: 24px;
}

.step-header {
    text-align: center;
    margin-bottom: 32px;
}

.step-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.step-header h3 i {
    color: #6366f1;
    font-size: 1.2rem;
}

.step-header p {
    color: #6b7280;
    line-height: 1.5;
}

.step-header.success {
    padding: 32px;
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    border-radius: 16px;
    border: 1px solid #bbf7d0;
}

.step-header.success h3 {
    color: #16a34a;
    font-size: 1.5rem;
}

.step-header.success p {
    color: #15803d;
    font-size: 1.1rem;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #10b981 0%, #16a34a 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    box-shadow: 0 8px 30px rgba(16, 185, 129, 0.3);
}

.success-icon i {
    font-size: 2.5rem;
    color: white;
}

.success-actions {
    margin-top: 32px;
    text-align: center;
}

.success-actions .btn {
    background: linear-gradient(135deg, #16a34a 0%, #10b981 100%);
    color: white;
    padding: 16px 32px;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.success-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.form-note {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    border-radius: 10px;
    padding: 12px 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.9rem;
    color: #92400e;
}

.form-note i {
    color: #f59e0b;
    font-size: 1rem;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: block;
    font-size: 0.95rem;
}

.form-group input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    font-family: inherit;
}

.form-group input:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.toggle-password:hover {
    color: #374151;
    background: #f3f4f6;
}

.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 8px;
    transition: all 0.3s ease;
    background: #e5e7eb;
}

.password-strength.weak {
    background: linear-gradient(90deg, #ef4444 0%, #f87171 100%);
    width: 33%;
}

.password-strength.medium {
    background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
    width: 66%;
}

.password-strength.strong {
    background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
    width: 100%;
}

.btn {
    width: 100%;
    padding: 16px;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    margin-bottom: 16px;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.auth-links {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.link-secondary {
    color: #6b7280;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: color 0.3s ease;
}

.link-secondary:hover {
    color: #6366f1;
}

/* Animation for step transitions */
.reset-step {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress step animations */
.progress-step {
    animation: stepAppear 0.6s ease-out;
}

.progress-step:nth-child(1) { animation-delay: 0.1s; }
.progress-step:nth-child(3) { animation-delay: 0.2s; }
.progress-step:nth-child(5) { animation-delay: 0.3s; }

@keyframes stepAppear {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .reset-card {
        margin: 10px;
    }
    
    .reset-progress {
        padding: 20px 16px;
    }
    
    .progress-step span {
        font-size: 0.7rem;
    }
    
    .progress-line {
        width: 40px;
        margin: 0 8px;
    }
    
    .step-header h3 {
        font-size: 1.1rem;
        flex-direction: column;
        gap: 8px;
    }
    
    .auth-links {
        flex-direction: column;
        gap: 12px;
    }
    
    .link-secondary {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .reset-progress {
        flex-direction: column;
        gap: 16px;
    }
    
    .progress-line {
        width: 2px;
        height: 30px;
        margin: 0;
    }
    
    .progress-step {
        flex-direction: row;
        gap: 12px;
    }
    
    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .success-icon {
        width: 60px;
        height: 60px;
    }
    
    .success-icon i {
        font-size: 2rem;
    }
    
    .step-header.success {
        padding: 24px 20px;
    }
}
