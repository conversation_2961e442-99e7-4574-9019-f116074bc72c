<?php
require_once '../config/database.php';
require_once '../config/auth.php';

// Check if user is admin
requireLogin('admin');

$studentId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$studentId) {
    header('Location: students.php');
    exit;
}

try {
    // Get student details
    $student = fetchOne("
        SELECT s.*, 
               COALESCE(d.name, 'No Department') as department_name,
               COALESCE(al.level_name, 'No Level') as level_name
        FROM students s
        LEFT JOIN departments d ON s.department_id = d.id
        LEFT JOIN academic_levels al ON s.academic_level_id = al.id
        WHERE s.id = :id
    ", ['id' => $studentId]);

    if (!$student) {
        header('Location: students.php?error=Student not found');
        exit;
    }

    // Get game statistics
    $gameStats = fetchOne("
        SELECT 
            COUNT(*) as total_games,
            COALESCE(AVG(CASE WHEN questions_answered > 0 THEN (correct_answers * 100.0 / questions_answered) END), 0) as avg_score,
            MAX(CASE WHEN questions_answered > 0 THEN (correct_answers * 100.0 / questions_answered) END) as best_score,
            SUM(points_earned) as total_points
        FROM game_sessions 
        WHERE student_id = :student_id AND status = 'completed'
    ", ['student_id' => $studentId]);

    // Get recent game sessions
    $recentGames = fetchAll("
        SELECT 
            gs.*,
            (CASE WHEN gs.questions_answered > 0 THEN (gs.correct_answers * 100.0 / gs.questions_answered) ELSE 0 END) as score_percentage
        FROM game_sessions gs
        WHERE gs.student_id = :student_id AND gs.status = 'completed'
        ORDER BY gs.created_at DESC
        LIMIT 10
    ", ['student_id' => $studentId]);

    // Default values if no game stats
    $totalGames = $gameStats['total_games'] ?? 0;
    $avgScore = round($gameStats['avg_score'] ?? 0, 1);
    $bestScore = round($gameStats['best_score'] ?? 0, 1);
    $totalPoints = $gameStats['total_points'] ?? 0;

    $pageTitle = $student['first_name'] . ' ' . $student['last_name'] . ' - Profile';

} catch (Exception $e) {
    header('Location: students.php?error=' . urlencode('Error loading student profile'));
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - LMS Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link rel="stylesheet" href="../assets/css/students.css">
    <link rel="stylesheet" href="../assets/css/student-profile-redesign.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item active">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="questions.php" class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>AI Questions</span>
                </a>
                <a href="game-analytics.php" class="nav-item">
                    <i class="fas fa-gamepad"></i>
                    <span>Game Analytics</span>
                </a>
                <a href="messages.php" class="nav-item">
                    <i class="fas fa-envelope"></i>
                    <span>Messages</span>
                </a>
                <a href="rewards.php" class="nav-item">
                    <i class="fas fa-trophy"></i>
                    <span>Rewards</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <a href="students.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Students
                    </a>
                    <h1><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h1>
                    <p>Complete student profile and performance overview</p>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" onclick="sendMessage(<?php echo $student['id']; ?>)">
                        <i class="fas fa-envelope"></i>
                        Send Message
                    </button>
                </div>
            </header>

            <!-- Content -->
            <div class="content">
                <div class="profile-container">
                    <!-- Student Profile Header -->
                    <div class="profile-header-section">
                        <div class="profile-card-new">
                            <div class="profile-avatar-section">
                                <div class="profile-avatar-new">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="profile-basic-info">
                                    <h2><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h2>
                                    <p class="student-id-display"><?php echo htmlspecialchars($student['student_id']); ?></p>
                                    <p class="department-display"><?php echo htmlspecialchars($student['department_name']); ?></p>
                                </div>
                            </div>
                            <div class="profile-status-section">
                                <div class="approval-status-new">
                                    <?php if ($student['is_approved']): ?>
                                        <span class="status-badge-new approved">
                                            <i class="fas fa-check-circle"></i>
                                            APPROVED
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge-new pending">
                                            <i class="fas fa-clock"></i>
                                            PENDING
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="contact-info">
                                    <p><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($student['email']); ?></p>
                                    <?php if ($student['phone']): ?>
                                        <p><i class="fas fa-phone"></i> <?php echo htmlspecialchars($student['phone']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Stats -->
                    <div class="stats-section">
                        <h3 class="section-title"><i class="fas fa-chart-bar"></i> Performance Overview</h3>
                        <div class="stats-grid-new">
                            <div class="stat-card-new">
                                <div class="stat-icon-new">
                                    <i class="fas fa-gamepad"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number-new"><?php echo $totalGames; ?></span>
                                    <span class="stat-label-new">Total Quizzes</span>
                                </div>
                            </div>

                            <div class="stat-card-new">
                                <div class="stat-icon-new">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number-new"><?php echo $avgScore; ?>%</span>
                                    <span class="stat-label-new">Average Score</span>
                                </div>
                            </div>

                            <div class="stat-card-new">
                                <div class="stat-icon-new">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number-new"><?php echo $totalPoints; ?></span>
                                    <span class="stat-label-new">Total Points</span>
                                </div>
                            </div>

                            <div class="stat-card-new">
                                <div class="stat-icon-new">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number-new"><?php echo $bestScore; ?>%</span>
                                    <span class="stat-label-new">Best Score</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Details Section -->
                    <div class="details-section">
                        <h3 class="section-title"><i class="fas fa-info-circle"></i> Student Information</h3>
                        <div class="details-card-new">
                            <div class="details-grid-new">
                                <div class="detail-item-new">
                                    <div class="detail-label">Academic Level</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($student['level_name']); ?></div>
                                </div>
                                <div class="detail-item-new">
                                    <div class="detail-label">Date of Birth</div>
                                    <div class="detail-value"><?php echo $student['date_of_birth'] ? date('M d, Y', strtotime($student['date_of_birth'])) : 'Not provided'; ?></div>
                                </div>
                                <div class="detail-item-new">
                                    <div class="detail-label">Gender</div>
                                    <div class="detail-value"><?php echo htmlspecialchars(ucfirst($student['gender'] ?? 'Not specified')); ?></div>
                                </div>
                                <div class="detail-item-new">
                                    <div class="detail-label">Address</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($student['address'] ?? 'Not provided'); ?></div>
                                </div>
                                <div class="detail-item-new">
                                    <div class="detail-label">Registration Date</div>
                                    <div class="detail-value"><?php echo date('M d, Y H:i', strtotime($student['created_at'])); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity Section -->
                    <?php if (!empty($recentGames)): ?>
                    <div class="activity-section">
                        <h3 class="section-title"><i class="fas fa-history"></i> Recent Quiz Sessions</h3>
                        <div class="activity-card-new">
                            <div class="activity-list-new">
                                <?php foreach ($recentGames as $game): ?>
                                <div class="activity-item-new">
                                    <div class="activity-main">
                                        <div class="activity-date-new"><?php echo date('M d, Y H:i', strtotime($game['created_at'])); ?></div>
                                        <div class="activity-details-new">
                                            <span class="correct-answers"><?php echo $game['correct_answers']; ?>/<?php echo $game['questions_answered']; ?> correct</span>
                                            <span class="points-earned"><?php echo $game['points_earned']; ?> points</span>
                                        </div>
                                    </div>
                                    <div class="activity-score-new">
                                        <?php
                                        $percentage = round($game['score_percentage'], 1);
                                        $scoreClass = $percentage >= 70 ? 'good' : ($percentage >= 50 ? 'average' : 'poor');
                                        ?>
                                        <span class="score-badge-new <?php echo $scoreClass; ?>">
                                            <?php echo $percentage; ?>%
                                        </span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="../assets/js/students.js"></script>
</body>
</html>
