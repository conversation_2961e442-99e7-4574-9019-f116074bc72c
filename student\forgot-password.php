<?php
/**
 * Student Password Reset Page for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

$step = isset($_GET['step']) ? $_GET['step'] : 'email';
$error = '';
$success = '';

// Step 1: Email/Matriculation Number verification
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $step === 'email') {
    $identifier = sanitizeInput($_POST['identifier']);
    
    if (empty($identifier)) {
        $error = 'Please enter your Matriculation Number or email address.';
    } else {
        // Check if student exists
        $student = fetchOne("
            SELECT id, student_id, email, first_name, last_name 
            FROM students 
            WHERE student_id = :identifier OR email = :identifier
        ", ['identifier' => $identifier]);
        
        if (!$student) {
            $error = 'No account found with this Matriculation Number or email address.';
        } else {
            // Store student ID in session for next step
            startSecureSession();
            $_SESSION['reset_student_id'] = $student['id'];
            header('Location: forgot-password.php?step=security');
            exit();
        }
    }
}

// Step 2: Security questions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $step === 'security') {
    startSecureSession();
    
    if (!isset($_SESSION['reset_student_id'])) {
        header('Location: forgot-password.php');
        exit();
    }
    
    $studentId = $_SESSION['reset_student_id'];
    $answers = [
        sanitizeInput($_POST['answer_1']),
        sanitizeInput($_POST['answer_2'])
    ];
    
    // Verify security answers
    $securityAnswers = fetchAll("
        SELECT ssa.answer_hash, sq.question 
        FROM student_security_answers ssa 
        JOIN security_questions sq ON ssa.question_id = sq.id 
        WHERE ssa.student_id = :student_id 
        ORDER BY ssa.question_id
    ", ['student_id' => $studentId]);
    
    if (count($securityAnswers) !== 2) {
        $error = 'Security questions not found. Please contact administration.';
    } else {
        $correctAnswers = 0;

        for ($i = 0; $i < 2; $i++) {
            if (isset($answers[$i]) && isset($securityAnswers[$i])) {
                if (verifyPassword(strtolower($answers[$i]), $securityAnswers[$i]['answer_hash'])) {
                    $correctAnswers++;
                }
            }
        }

        if ($correctAnswers === 2) { // Both answers must be correct
            header('Location: forgot-password.php?step=reset');
            exit();
        } else {
            $error = 'Incorrect answers. Please try again or contact administration.';
        }
    }
}

// Step 3: Password reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $step === 'reset') {
    startSecureSession();
    
    if (!isset($_SESSION['reset_student_id'])) {
        header('Location: forgot-password.php');
        exit();
    }
    
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    
    if (empty($password) || empty($confirmPassword)) {
        $error = 'Please fill in all fields.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long.';
    } else {
        // Update password
        $passwordHash = generateSecureHash($password);
        $updated = updateRecord('students', 
            ['password_hash' => $passwordHash], 
            'id = :id', 
            ['id' => $_SESSION['reset_student_id']]
        );
        
        if ($updated) {
            // Clear session
            unset($_SESSION['reset_student_id']);
            $success = 'Password reset successful! You can now login with your new password.';
            $step = 'complete';
        } else {
            $error = 'Failed to reset password. Please try again.';
        }
    }
}

// Get security questions for step 2
$securityQuestions = [];
if ($step === 'security') {
    startSecureSession();
    if (isset($_SESSION['reset_student_id'])) {
        $securityQuestions = fetchAll("
            SELECT sq.question 
            FROM student_security_answers ssa 
            JOIN security_questions sq ON ssa.question_id = sq.id 
            WHERE ssa.student_id = :student_id 
            ORDER BY ssa.question_id
        ", ['student_id' => $_SESSION['reset_student_id']]);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link rel="stylesheet" href="../assets/css/reset-password.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card reset-card">
            <div class="auth-header">
                <div class="logo-container">
                    <img src="../images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="logo">
                </div>
                <h1>Reset Password</h1>
                <p>AI-Powered Learning Management System</p>
                <p class="school-name">Ogbonnaya Onu Polytechnic, Aba</p>
            </div>
            
            <div class="auth-body">
                <!-- Progress Indicator -->
                <div class="reset-progress">
                    <div class="progress-step <?php echo $step === 'email' ? 'active' : ($step !== 'email' ? 'completed' : ''); ?>">
                        <div class="step-number">1</div>
                        <span>Verify Identity</span>
                    </div>
                    <div class="progress-line <?php echo $step !== 'email' ? 'completed' : ''; ?>"></div>
                    <div class="progress-step <?php echo $step === 'security' ? 'active' : ($step === 'reset' || $step === 'complete' ? 'completed' : ''); ?>">
                        <div class="step-number">2</div>
                        <span>Security Questions</span>
                    </div>
                    <div class="progress-line <?php echo $step === 'reset' || $step === 'complete' ? 'completed' : ''; ?>"></div>
                    <div class="progress-step <?php echo $step === 'reset' ? 'active' : ($step === 'complete' ? 'completed' : ''); ?>">
                        <div class="step-number">3</div>
                        <span>New Password</span>
                    </div>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($step === 'email'): ?>
                    <!-- Step 1: Email/Matriculation Number -->
                    <div class="reset-step">
                        <div class="step-header">
                            <h3><i class="fas fa-user-check"></i> Verify Your Identity</h3>
                            <p>Enter your Matriculation Number or email address to begin password reset</p>
                        </div>
                        
                        <form method="POST" class="auth-form">
                            <div class="form-group">
                                <label for="identifier">Matriculation Number or Email Address</label>
                                <input
                                    type="text"
                                    id="identifier"
                                    name="identifier"
                                    required
                                    placeholder="Enter your Matriculation Number (e.g., HD2023/07745/1/CS) or email"
                                    value="<?php echo isset($_POST['identifier']) ? htmlspecialchars($_POST['identifier']) : ''; ?>"
                                >
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i>
                                Continue
                            </button>
                        </form>
                    </div>
                    
                <?php elseif ($step === 'security'): ?>
                    <!-- Step 2: Security Questions -->
                    <div class="reset-step">
                        <div class="step-header">
                            <h3><i class="fas fa-shield-alt"></i> Answer Security Questions</h3>
                            <p>Please answer your security questions to verify your identity</p>
                        </div>
                        
                        <form method="POST" class="auth-form">
                            <?php foreach ($securityQuestions as $index => $question): ?>
                                <div class="form-group">
                                    <label for="answer_<?php echo $index + 1; ?>">
                                        Question <?php echo $index + 1; ?>: <?php echo htmlspecialchars($question['question']); ?>
                                    </label>
                                    <input 
                                        type="text" 
                                        id="answer_<?php echo $index + 1; ?>" 
                                        name="answer_<?php echo $index + 1; ?>" 
                                        required 
                                        placeholder="Enter your answer"
                                    >
                                </div>
                            <?php endforeach; ?>
                            
                            <div class="form-note">
                                <i class="fas fa-info-circle"></i>
                                <span>Please answer both security questions correctly</span>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i>
                                Verify Answers
                            </button>
                        </form>
                    </div>
                    
                <?php elseif ($step === 'reset'): ?>
                    <!-- Step 3: New Password -->
                    <div class="reset-step">
                        <div class="step-header">
                            <h3><i class="fas fa-lock"></i> Set New Password</h3>
                            <p>Choose a strong password for your account</p>
                        </div>
                        
                        <form method="POST" class="auth-form">
                            <div class="form-group">
                                <label for="password">New Password</label>
                                <div class="password-input">
                                    <input type="password" id="password" name="password" required minlength="8">
                                    <button type="button" class="toggle-password" onclick="togglePassword('password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength" id="password-strength"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password">Confirm New Password</label>
                                <div class="password-input">
                                    <input type="password" id="confirm_password" name="confirm_password" required minlength="8">
                                    <button type="button" class="toggle-password" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check"></i>
                                Reset Password
                            </button>
                        </form>
                    </div>
                    
                <?php elseif ($step === 'complete'): ?>
                    <!-- Step 4: Complete -->
                    <div class="reset-step">
                        <div class="step-header success">
                            <div class="success-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h3>Password Reset Complete!</h3>
                            <p>Your password has been successfully reset. You can now login with your new password.</p>
                        </div>
                        
                        <div class="success-actions">
                            <a href="login.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i>
                                Login Now
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="auth-links">
                    <a href="login.php" class="link-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Login
                    </a>
                    <a href="../index.php" class="link-secondary">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </a>
                </div>
            </div>
            
            <div class="auth-footer">
                <div class="security-info">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure password reset process</span>
                </div>
                <p>&copy; 2025 Ogbonnaya Onu Polytechnic, Aba. All rights reserved.</p>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/reset-password.js"></script>
    <script>
        // Simple validation for forgot password form
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.auth-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const identifier = document.getElementById('identifier');
                    const answer1 = document.getElementById('answer_1');
                    const answer2 = document.getElementById('answer_2');
                    const password = document.getElementById('password');
                    const confirmPassword = document.getElementById('confirm_password');

                    let isValid = true;
                    let errorMessage = '';

                    // Clear previous errors
                    const errorDiv = document.querySelector('.alert-danger');
                    if (errorDiv) {
                        errorDiv.remove();
                    }

                    // Step 1: Identifier validation
                    if (identifier && !identifier.value.trim()) {
                        errorMessage = 'Please enter your Matriculation Number or email address.';
                        isValid = false;
                    }

                    // Step 2: Security questions validation
                    if (answer1 && !answer1.value.trim()) {
                        errorMessage = 'Please answer the first security question.';
                        isValid = false;
                    }
                    if (answer2 && !answer2.value.trim()) {
                        errorMessage = 'Please answer the second security question.';
                        isValid = false;
                    }

                    // Step 3: Password validation
                    if (password) {
                        if (!password.value) {
                            errorMessage = 'Password is required.';
                            isValid = false;
                        } else if (password.value.length < 8) {
                            errorMessage = 'Password must be at least 8 characters long.';
                            isValid = false;
                        }
                    }

                    if (confirmPassword && password) {
                        if (!confirmPassword.value) {
                            errorMessage = 'Please confirm your password.';
                            isValid = false;
                        } else if (password.value !== confirmPassword.value) {
                            errorMessage = 'Passwords do not match.';
                            isValid = false;
                        }
                    }

                    if (!isValid) {
                        e.preventDefault();

                        // Show error message
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-danger';
                        alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> ' + errorMessage;

                        const resetStep = document.querySelector('.reset-step');
                        if (resetStep) {
                            resetStep.insertBefore(alertDiv, resetStep.firstChild);
                        }
                    }
                });
            }
        });

        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleButton = passwordInput.nextElementSibling;
            const toggleIcon = toggleButton.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
