<?php
/**
 * Landing Page for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once 'config/database.php';

// Initialize default values
$stats = [
    'total_students' => 0,
    'total_departments' => 31,
    'total_questions' => 0,
    'active_today' => 0
];

$schoolName = 'Ogbonnaya Onu Polytechnic, Aba';
$schoolMotto = 'Excellence in Technical Education';

// Try to get system statistics (only if database is set up)
try {
    // Test database connection
    $testQuery = fetchOne("SELECT 1 as test");
    if ($testQuery) {
        // Get system statistics for display
        $stats = [
            'total_students' => fetchOne("SELECT COUNT(*) as count FROM students WHERE status = 'active'")['count'] ?? 0,
            'total_departments' => fetchOne("SELECT COUNT(*) as count FROM departments")['count'] ?? 31,
            'total_questions' => fetchOne("SELECT COUNT(*) as count FROM questions")['count'] ?? 0,
            'active_today' => fetchOne("SELECT COUNT(DISTINCT user_id) as count FROM user_sessions WHERE DATE(created_at) = CURDATE() AND user_type = 'student'")['count'] ?? 0
        ];

        // Get school settings
        $schoolName = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = 'school_name'")['setting_value'] ?? 'Ogbonnaya Onu Polytechnic, Aba';
        $schoolMotto = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = 'school_motto'")['setting_value'] ?? 'Excellence in Technical Education';
    }
} catch (Exception $e) {
    // Database not set up yet, use default values
    error_log("Database not ready: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered LMS - <?php echo htmlspecialchars($schoolName); ?></title>
    <meta name="description" content="AI-Powered Learning Management System for Ogbonnaya Onu Polytechnic students. Gamified learning with personalized quizzes and rewards.">
    <link rel="stylesheet" href="assets/css/landing.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="images/logo.jpg" alt="School Logo" class="nav-logo">
                <div class="brand-text">
                    <h3>AI-Powered LMS</h3>
                    <p>Smart Learning Platform</p>
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#stats" class="nav-link">Statistics</a>
            </div>
            
            <div class="nav-actions">
                <a href="student/login.php" class="btn btn-outline">
                    <i class="fas fa-graduation-cap"></i>
                    Student Login
                </a>
                <a href="admin/login.php" class="btn btn-primary">
                    <i class="fas fa-user-shield"></i>
                    Admin
                </a>
            </div>
            
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-particles"></div>
            <div class="hero-grid"></div>
        </div>

        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-crown"></i>
                    <span>Elite Learning Platform</span>
                    <div class="badge-glow"></div>
                </div>

                <h1 class="hero-title">
                    <span class="title-main">Master Knowledge</span>
                    <span class="title-sub gradient-text">Through Interactive Learning</span>
                    <div class="title-underline"></div>
                </h1>

                <p class="hero-description">
                    Choose your learning adventure! Challenge yourself with Quick Games, test your endurance in Endless Mode,
                    or embark on progressive Mission adventures. Each mode offers a unique way to master your subjects
                    at <?php echo htmlspecialchars($schoolName); ?>.
                </p>



                <div class="hero-actions">
                    <a href="student/register.php" class="btn btn-primary btn-large">
                        <i class="fas fa-play"></i>
                        <span>Start Learning</span>
                    </a>
                    <a href="student/login.php" class="btn btn-outline btn-large">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Student Login</span>
                    </a>
                </div>


            </div>

            <div class="hero-visual">
                <div class="hero-image">
                    <img src="images/image2.jpg" alt="School Building" class="school-image">
                </div>
            </div>
        </div>
    </section>

    <!-- Game Modes Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Game Modes</h2>
                <p>Choose your learning adventure</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                    </div>
                    <h3>Quick Game</h3>
                    <p>Jump into a fast-paced learning session perfect for quick study breaks. Answer questions across different subjects and earn points in just a few minutes. Great for reviewing concepts and testing your knowledge on the go.</p>
                    <div class="feature-stats">
                        <div class="stat">
                            <i class="fas fa-clock"></i>
                            <span>5-10 Minutes</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-star"></i>
                            <span>Quick Points</span>
                        </div>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                    </div>
                    <h3>Mission Mode</h3>
                    <p>Embark on structured learning missions with specific objectives and goals. Complete challenges, unlock new topics, and progress through carefully designed educational pathways.</p>
                    <div class="feature-stats">
                        <div class="stat">
                            <i class="fas fa-target"></i>
                            <span>Goal-Oriented</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-trophy"></i>
                            <span>Achievements</span>
                        </div>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="fas fa-infinity"></i>
                        </div>
                    </div>
                    <h3>Endless Mode</h3>
                    <p>Challenge yourself with continuous learning sessions that adapt to your performance. Keep answering questions for as long as you can and climb the leaderboards with your knowledge and endurance.</p>
                    <div class="feature-stats">
                        <div class="stat">
                            <i class="fas fa-chart-line"></i>
                            <span>Adaptive</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-medal"></i>
                            <span>Leaderboards</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- About Game Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>About Our Learning Game</h2>
                    <p class="about-subtitle"><?php echo htmlspecialchars($schoolMotto); ?></p>

                    <p>Welcome to the ultimate educational adventure at <?php echo htmlspecialchars($schoolName); ?>! Our revolutionary gamified learning platform transforms traditional education into an epic quest where knowledge is power, and every correct answer brings you closer to mastery.</p>

                    <div class="about-features">
                        <div class="about-feature game-feature-item">
                            <i class="fas fa-route"></i>
                            <span>Personalized quest paths tailored to your learning style</span>
                        </div>
                        <div class="about-feature game-feature-item">
                            <i class="fas fa-robot"></i>
                            <span>AI-powered challenges that adapt to your skill level</span>
                        </div>
                        <div class="about-feature game-feature-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Real-time progress tracking with visual achievements</span>
                        </div>
                        <div class="about-feature game-feature-item">
                            <i class="fas fa-trophy"></i>
                            <span>Epic rewards system with badges and leaderboards</span>
                        </div>
                    </div>

                    <a href="student/register.php" class="btn btn-primary game-btn">
                        <i class="fas fa-rocket"></i>
                        <span>Begin Your Adventure</span>
                        <div class="btn-glow"></div>
                    </a>
                </div>


            </div>
        </div>
    </section>
    
    <!-- Game Statistics Section -->
    <section id="stats" class="statistics">
        <div class="container">
            <div class="section-header">
                <h2>Game World Statistics</h2>
                <p>See how our learning community is conquering knowledge together</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card game-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                        <div class="stat-glow"></div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" data-target="<?php echo $stats['total_students']; ?>">0</h3>
                        <p>Active Players</p>
                        <div class="stat-badge">
                            <i class="fas fa-gamepad"></i>
                            <span>Online</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card game-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-map"></i>
                        <div class="stat-glow"></div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" data-target="<?php echo $stats['total_departments']; ?>">0</h3>
                        <p>Learning Realms</p>
                        <div class="stat-badge">
                            <i class="fas fa-compass"></i>
                            <span>Explore</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card game-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-puzzle-piece"></i>
                        <div class="stat-glow"></div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" data-target="<?php echo $stats['total_questions']; ?>">0</h3>
                        <p>Challenge Quests</p>
                        <div class="stat-badge">
                            <i class="fas fa-brain"></i>
                            <span>AI Generated</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card game-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-fire"></i>
                        <div class="stat-glow"></div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" data-target="<?php echo $stats['active_today']; ?>">0</h3>
                        <p>Daily Adventurers</p>
                        <div class="stat-badge">
                            <i class="fas fa-calendar-day"></i>
                            <span>Today</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="images/logo.jpg" alt="School Logo" class="footer-logo">
                        <h4>AI-Powered LMS</h4>
                        <p><?php echo htmlspecialchars($schoolName); ?></p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="student/login.php">Student Login</a></li>
                        <li><a href="student/register.php">Register</a></li>
                        <li><a href="admin/login.php">Admin Portal</a></li>
                        <li><a href="#features">Features</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Support</a></li>
                        <li><a href="#">System Status</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Connect</h4>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 <?php echo htmlspecialchars($schoolName); ?>. All rights reserved.</p>
                <p>Powered by AI-Powered LMS Technology</p>
            </div>
        </div>
    </footer>
    
    <script src="assets/js/landing.js"></script>
    <script src="assets/js/gaming-effects.js"></script>

    <!-- Page Transition Overlay -->
    <div id="page-transition" class="page-transition">
        <div class="transition-content">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
            <h3>Loading...</h3>
            <p>Preparing your learning adventure</p>
        </div>
    </div>

    <script>
        // Page transition functionality
        document.addEventListener('DOMContentLoaded', function() {
            try {
                const transitionOverlay = document.getElementById('page-transition');
                const buttons = document.querySelectorAll('a.btn, .btn, .nav-link');

                if (transitionOverlay && buttons.length > 0) {
                    buttons.forEach(button => {
                        button.addEventListener('click', function(e) {
                            try {
                                const href = this.getAttribute('href');

                                // Skip transition for anchor links and external links
                                if (!href || href.startsWith('#') || href.startsWith('http') || href.startsWith('mailto')) {
                                    return;
                                }

                                e.preventDefault();

                                // Show transition overlay
                                transitionOverlay.classList.add('active');

                                // Navigate after animation
                                setTimeout(() => {
                                    window.location.href = href;
                                }, 800);
                            } catch (error) {
                                console.error('Error in transition handler:', error);
                            }
                        });
                    });
                }
            } catch (error) {
                console.error('Error initializing page transitions:', error);
            }
        });
    </script>

    <style>
        .page-transition {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
        }

        .page-transition.active {
            opacity: 1;
            visibility: visible;
        }

        .transition-content {
            text-align: center;
            color: white;
            transform: translateY(20px);
            transition: transform 0.3s ease-in-out 0.1s;
        }

        .page-transition.active .transition-content {
            transform: translateY(0);
        }

        .loading-spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
        }

        .spinner-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 3px solid transparent;
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .spinner-ring:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
            animation-delay: -0.3s;
            border-top-color: rgba(255, 255, 255, 0.7);
        }

        .spinner-ring:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
            animation-delay: -0.6s;
            border-top-color: rgba(255, 255, 255, 0.4);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .transition-content h3 {
            margin: 0 0 10px;
            font-size: 24px;
            font-weight: 600;
        }

        .transition-content p {
            margin: 0;
            font-size: 16px;
            opacity: 0.8;
        }
    </style>
</body>
</html>
