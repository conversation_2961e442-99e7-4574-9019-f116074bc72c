-- Game System Tables for AI-Powered LMS
-- Ogbonnaya Onu Polytechnic, Aba

-- Game Sessions Table
CREATE TABLE IF NOT EXISTS game_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    game_mode ENUM('quick', 'endless', 'mission') NOT NULL,
    difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'easy',
    questions_count INT DEFAULT 10,
    questions_answered INT DEFAULT 0,
    correct_answers INT DEFAULT 0,
    wrong_answers INT DEFAULT 0,
    points_earned INT DEFAULT 0,
    lives_remaining INT DEFAULT 3,
    level_reached INT DEFAULT 1,
    status ENUM('active', 'completed', 'failed', 'abandoned') DEFAULT 'active',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    session_data JSON,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    INDEX idx_student_mode (student_id, game_mode),
    INDEX idx_status (status),
    INDEX idx_points (points_earned DESC)
);

-- Game Questions Table (for tracking questions used in games)
CREATE TABLE IF NOT EXISTS game_questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    question_text TEXT NOT NULL,
    option_a VARCHAR(500) NOT NULL,
    option_b VARCHAR(500) NOT NULL,
    option_c VARCHAR(500) NOT NULL,
    option_d VARCHAR(500) NOT NULL,
    correct_answer ENUM('A', 'B', 'C', 'D') NOT NULL,
    student_answer ENUM('A', 'B', 'C', 'D') NULL,
    is_correct BOOLEAN DEFAULT FALSE,
    points_awarded INT DEFAULT 0,
    time_taken INT DEFAULT 0, -- in seconds
    question_order INT NOT NULL,
    difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'easy',
    answered_at TIMESTAMP NULL,
    FOREIGN KEY (session_id) REFERENCES game_sessions(id) ON DELETE CASCADE,
    INDEX idx_session (session_id),
    INDEX idx_difficulty (difficulty)
);

-- Student Question History Table (for preventing question repetition)
CREATE TABLE IF NOT EXISTS student_question_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    question_hash VARCHAR(64) NOT NULL,
    question_text TEXT NOT NULL,
    department_id INT NOT NULL,
    academic_level_id INT NOT NULL,
    difficulty ENUM('easy', 'medium', 'hard') NOT NULL,
    game_mode ENUM('quick', 'endless', 'mission') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_level_id) REFERENCES academic_levels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_question (student_id, question_hash),
    INDEX idx_student_dept_level (student_id, department_id, academic_level_id),
    INDEX idx_difficulty_mode (difficulty, game_mode),
    INDEX idx_created (created_at)
);

-- Student Achievements Table
CREATE TABLE IF NOT EXISTS student_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    achievement_type ENUM('points', 'streak', 'level', 'mode', 'special') NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50) DEFAULT 'fas fa-medal',
    points_reward INT DEFAULT 0,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_type (achievement_type),
    INDEX idx_earned (earned_at DESC)
);

-- Game Leaderboards Table
CREATE TABLE IF NOT EXISTS game_leaderboards (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    game_mode ENUM('quick', 'endless', 'mission', 'overall') NOT NULL,
    score INT NOT NULL,
    rank_position INT,
    period ENUM('daily', 'weekly', 'monthly', 'all_time') DEFAULT 'all_time',
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_mode_period (student_id, game_mode, period),
    INDEX idx_mode_period (game_mode, period),
    INDEX idx_score (score DESC),
    INDEX idx_rank (rank_position)
);

-- Mission Levels Table
CREATE TABLE IF NOT EXISTS mission_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level_number INT NOT NULL UNIQUE,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    difficulty ENUM('easy', 'medium', 'hard') NOT NULL,
    questions_required INT DEFAULT 10,
    points_to_unlock INT DEFAULT 0,
    points_reward INT DEFAULT 100,
    boss_level BOOLEAN DEFAULT FALSE,
    unlock_requirements JSON,
    level_data JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_level (level_number),
    INDEX idx_difficulty (difficulty),
    INDEX idx_active (is_active)
);

-- Student Mission Progress Table
CREATE TABLE IF NOT EXISTS student_mission_progress (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    level_id INT NOT NULL,
    status ENUM('locked', 'available', 'in_progress', 'completed') DEFAULT 'locked',
    attempts INT DEFAULT 0,
    best_score INT DEFAULT 0,
    stars_earned INT DEFAULT 0, -- 1-3 stars based on performance
    completed_at TIMESTAMP NULL,
    unlocked_at TIMESTAMP NULL,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (level_id) REFERENCES mission_levels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_level (student_id, level_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
);

-- Game Statistics Table (for analytics)
CREATE TABLE IF NOT EXISTS game_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    stat_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
    games_played INT DEFAULT 0,
    total_points INT DEFAULT 0,
    questions_answered INT DEFAULT 0,
    correct_answers INT DEFAULT 0,
    accuracy_percentage DECIMAL(5,2) DEFAULT 0.00,
    longest_streak INT DEFAULT 0,
    average_time_per_question DECIMAL(6,2) DEFAULT 0.00,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_period (student_id, stat_type, period_start),
    INDEX idx_student_type (student_id, stat_type),
    INDEX idx_period (period_start, period_end)
);

-- Insert default mission levels
INSERT INTO mission_levels (level_number, title, description, difficulty, questions_required, points_to_unlock, points_reward, boss_level) VALUES
(1, 'Welcome to Learning', 'Your first steps into the world of knowledge', 'easy', 5, 0, 50, FALSE),
(2, 'Building Foundations', 'Strengthen your basic understanding', 'easy', 8, 50, 75, FALSE),
(3, 'Rising Challenge', 'Time to test your growing skills', 'medium', 10, 125, 100, FALSE),
(4, 'Knowledge Guardian', 'Face the first boss challenge!', 'medium', 15, 225, 200, TRUE),
(5, 'Advanced Explorer', 'Venture into complex territories', 'medium', 12, 425, 150, FALSE),
(6, 'Expert Territory', 'Only the skilled can proceed', 'hard', 15, 575, 200, FALSE),
(7, 'Master Challenge', 'Prove your mastery', 'hard', 20, 775, 250, FALSE),
(8, 'Final Boss', 'The ultimate test of knowledge!', 'hard', 25, 1025, 500, TRUE);

-- Insert sample achievements
INSERT INTO student_achievements (student_id, achievement_type, title, description, icon, points_reward, earned_at) VALUES
(1, 'points', 'First Steps', 'Earned your first 100 points!', 'fas fa-star', 25, NOW()),
(1, 'streak', 'On Fire!', 'Answered 5 questions correctly in a row', 'fas fa-fire', 50, NOW()),
(1, 'level', 'Level Up!', 'Completed your first mission level', 'fas fa-flag', 75, NOW());

-- Create indexes for better performance
CREATE INDEX idx_game_sessions_student_points ON game_sessions(student_id, points_earned DESC);
CREATE INDEX idx_game_questions_session_order ON game_questions(session_id, question_order);
CREATE INDEX idx_achievements_student_earned ON student_achievements(student_id, earned_at DESC);
CREATE INDEX idx_leaderboards_mode_score ON game_leaderboards(game_mode, score DESC);
CREATE INDEX idx_mission_progress_student_status ON student_mission_progress(student_id, status);

-- Views for easy data retrieval
CREATE OR REPLACE VIEW student_game_stats AS
SELECT 
    s.id as student_id,
    s.first_name,
    s.last_name,
    s.student_id as matriculation_no,
    d.name as department_name,
    al.level_name,
    COALESCE(SUM(gs.points_earned), 0) as total_points,
    COALESCE(MAX(CASE WHEN gs.game_mode = 'endless' THEN gs.points_earned END), 0) as highest_endless_score,
    COALESCE(MAX(CASE WHEN gs.game_mode = 'mission' THEN gs.level_reached END), 0) as highest_mission_level,
    COUNT(CASE WHEN gs.game_mode = 'quick' THEN 1 END) as quick_games_played,
    COUNT(CASE WHEN gs.status = 'completed' THEN 1 END) as total_completed_games,
    COALESCE(AVG(CASE WHEN gs.status = 'completed' THEN (gs.correct_answers * 100.0 / gs.questions_answered) END), 0) as average_accuracy
FROM students s
LEFT JOIN departments d ON s.department_id = d.id
LEFT JOIN academic_levels al ON s.academic_level_id = al.id
LEFT JOIN game_sessions gs ON s.id = gs.student_id
GROUP BY s.id, s.first_name, s.last_name, s.student_id, d.name, al.level_name;

-- View for global leaderboard
CREATE OR REPLACE VIEW global_leaderboard AS
SELECT 
    s.id as student_id,
    s.first_name,
    s.last_name,
    s.student_id as matriculation_no,
    d.name as department_name,
    COALESCE(SUM(gs.points_earned), 0) as total_points,
    ROW_NUMBER() OVER (ORDER BY COALESCE(SUM(gs.points_earned), 0) DESC) as rank_position
FROM students s
LEFT JOIN departments d ON s.department_id = d.id
LEFT JOIN game_sessions gs ON s.id = gs.student_id
GROUP BY s.id, s.first_name, s.last_name, s.student_id, d.name
ORDER BY total_points DESC;
