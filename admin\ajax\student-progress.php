<?php
/**
 * AJAX endpoint for student progress details
 */

require_once '../../config/database.php';

// Check if admin is logged in
requireLogin('admin');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo '<div class="error">Invalid student ID</div>';
    exit;
}

$studentId = (int)$_GET['id'];

try {
    // Get student details
    $student = fetchOne("
        SELECT s.*, d.name as department_name, al.level_name
        FROM students s
        JOIN departments d ON s.department_id = d.id
        JOIN academic_levels al ON s.academic_level_id = al.id
        WHERE s.id = :id
    ", ['id' => $studentId]);

    if (!$student) {
        echo '<div class="error">Student not found</div>';
        exit;
    }

    // Get quiz statistics
    $quizStats = fetchOne("
        SELECT 
            COUNT(*) as total_quizzes,
            AVG(score) as avg_score,
            AVG(total_questions) as avg_questions,
            MAX(score) as best_score,
            MAX(total_questions) as max_questions
        FROM quiz_sessions 
        WHERE student_id = :id
    ", ['id' => $studentId]);

    // Get recent quiz sessions
    $recentQuizzes = fetchAll("
        SELECT qs.*, d.name as department_name, al.level_name
        FROM quiz_sessions qs
        JOIN departments d ON qs.department_id = d.id
        JOIN academic_levels al ON qs.academic_level_id = al.id
        WHERE qs.student_id = :id
        ORDER BY qs.created_at DESC
        LIMIT 10
    ", ['id' => $studentId]);

    // Get department/level performance
    $levelPerformance = fetchAll("
        SELECT
            CONCAT(d.name, ' - ', al.level_name) as level_name,
            COUNT(*) as quiz_count,
            AVG(qs.score) as avg_score,
            AVG(qs.total_questions) as avg_questions,
            MAX(qs.score) as best_score
        FROM quiz_sessions qs
        JOIN departments d ON qs.department_id = d.id
        JOIN academic_levels al ON qs.academic_level_id = al.id
        WHERE qs.student_id = :id
        GROUP BY qs.department_id, qs.academic_level_id, d.name, al.level_name
        ORDER BY quiz_count DESC
    ", ['id' => $studentId]);

    // Get mission mode progress
    $missionProgress = fetchAll("
        SELECT
            ml.level_number,
            ml.title,
            ml.difficulty,
            ml.questions_required,
            ml.points_reward,
            COALESCE(smp.status, 'locked') as status,
            COALESCE(smp.attempts, 0) as attempts,
            COALESCE(smp.best_score, 0) as best_score,
            COALESCE(smp.stars_earned, 0) as stars_earned,
            smp.completed_at
        FROM mission_levels ml
        LEFT JOIN student_mission_progress smp ON ml.id = smp.level_id AND smp.student_id = :id
        WHERE ml.is_active = 1
        ORDER BY ml.level_number
    ", ['id' => $studentId]);

    // Get mission statistics
    $missionStats = [
        'total_levels' => count($missionProgress),
        'completed_levels' => count(array_filter($missionProgress, function($m) { return $m['status'] === 'completed'; })),
        'total_stars' => array_sum(array_column($missionProgress, 'stars_earned')),
        'total_attempts' => array_sum(array_column($missionProgress, 'attempts')),
        'total_mission_points' => fetchOne("
            SELECT COALESCE(SUM(points_earned), 0) as total
            FROM game_sessions
            WHERE student_id = :id AND game_mode = 'mission' AND status = 'completed'
        ", ['id' => $studentId])['total'] ?? 0
    ];

    // Calculate overall performance
    $totalQuizzes = $quizStats['total_quizzes'] ?? 0;
    $avgScore = $quizStats['avg_score'] ?? 0;
    $avgQuestions = $quizStats['avg_questions'] ?? 0;
    $overallPercentage = $avgQuestions > 0 ? ($avgScore / $avgQuestions) * 100 : 0;

} catch (Exception $e) {
    echo '<div class="error">Error loading student progress: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit;
}
?>

<div class="student-progress-details">
    <!-- Student Header -->
    <div class="progress-header">
        <div class="student-avatar-large">
            <i class="fas fa-user"></i>
        </div>
        <div class="student-details">
            <h2><?php echo htmlspecialchars(($student['first_name'] ?? '') . ' ' . ($student['last_name'] ?? '')); ?></h2>
            <p class="student-id"><?php echo htmlspecialchars($student['student_id'] ?? 'N/A'); ?></p>
            <p class="student-dept"><?php echo htmlspecialchars(($student['department_name'] ?? 'Unknown') . ' - ' . ($student['level_name'] ?? 'Unknown')); ?></p>
            <div class="student-status">
                <?php if (($student['is_approved'] ?? 0) == 1): ?>
                    <span class="status approved">
                        <i class="fas fa-check-circle"></i>
                        Approved
                    </span>
                <?php else: ?>
                    <span class="status pending">
                        <i class="fas fa-clock"></i>
                        Pending Approval
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Progress Stats -->
    <div class="progress-stats">
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo number_format($totalQuizzes); ?></h3>
                <p>Total Quizzes</p>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo number_format($overallPercentage, 1); ?>%</h3>
                <p>Average Score</p>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-star"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo number_format($student['total_points'] ?? 0); ?></h3>
                <p>Total Points</p>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $quizStats['best_score'] ?? 0; ?></h3>
                <p>Best Score</p>
            </div>
        </div>
    </div>

    <!-- Level Performance -->
    <?php if (!empty($levelPerformance)): ?>
    <div class="level-performance">
        <h3><i class="fas fa-chart-bar"></i> Department & Level Performance</h3>
        <div class="performance-list">
            <?php foreach ($levelPerformance as $level): ?>
                <div class="performance-item">
                    <div class="level-info">
                        <h4><?php echo htmlspecialchars($level['level_name']); ?></h4>
                        <p><?php echo $level['quiz_count']; ?> quiz<?php echo $level['quiz_count'] != 1 ? 'es' : ''; ?></p>
                    </div>
                    <div class="performance-stats">
                        <div class="performance-bar">
                            <?php
                            $percentage = $level['avg_questions'] > 0 ? ($level['avg_score'] / $level['avg_questions']) * 100 : 0;
                            $barClass = $percentage >= 70 ? 'good' : ($percentage >= 50 ? 'average' : 'poor');
                            ?>
                            <div class="bar-fill <?php echo $barClass; ?>" style="width: <?php echo $percentage; ?>%"></div>
                        </div>
                        <span class="percentage"><?php echo number_format($percentage, 1); ?>%</span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Mission Mode Progress -->
    <div class="mission-progress">
        <h3><i class="fas fa-rocket"></i> Mission Mode Progress</h3>

        <!-- Mission Statistics -->
        <div class="mission-stats">
            <div class="mission-stat">
                <div class="stat-icon">
                    <i class="fas fa-flag-checkered"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo $missionStats['completed_levels']; ?>/<?php echo $missionStats['total_levels']; ?></h4>
                    <p>Levels Completed</p>
                </div>
            </div>

            <div class="mission-stat">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo $missionStats['total_stars']; ?></h4>
                    <p>Total Stars</p>
                </div>
            </div>

            <div class="mission-stat">
                <div class="stat-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo number_format($missionStats['total_mission_points']); ?></h4>
                    <p>Mission Points</p>
                </div>
            </div>

            <div class="mission-stat">
                <div class="stat-icon">
                    <i class="fas fa-redo"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo $missionStats['total_attempts']; ?></h4>
                    <p>Total Attempts</p>
                </div>
            </div>
        </div>

        <!-- Mission Levels Detail -->
        <?php if (!empty($missionProgress)): ?>
        <div class="mission-levels">
            <h4>Level Progress</h4>
            <div class="levels-grid">
                <?php foreach ($missionProgress as $mission): ?>
                    <div class="mission-level <?php echo $mission['status']; ?>">
                        <div class="level-header">
                            <span class="level-number">Level <?php echo $mission['level_number']; ?></span>
                            <span class="level-status <?php echo $mission['status']; ?>">
                                <?php
                                $statusIcons = [
                                    'completed' => '✅',
                                    'available' => '🔓',
                                    'in_progress' => '⏳',
                                    'locked' => '🔒'
                                ];
                                echo $statusIcons[$mission['status']] ?? '🔒';
                                ?>
                            </span>
                        </div>

                        <h5><?php echo htmlspecialchars($mission['title']); ?></h5>

                        <div class="level-details">
                            <div class="detail-item">
                                <span class="label">Difficulty:</span>
                                <span class="value difficulty-<?php echo strtolower($mission['difficulty']); ?>">
                                    <?php echo ucfirst($mission['difficulty']); ?>
                                </span>
                            </div>

                            <div class="detail-item">
                                <span class="label">Questions:</span>
                                <span class="value"><?php echo $mission['questions_required']; ?></span>
                            </div>

                            <?php if ($mission['status'] !== 'locked'): ?>
                                <div class="detail-item">
                                    <span class="label">Attempts:</span>
                                    <span class="value"><?php echo $mission['attempts']; ?></span>
                                </div>

                                <?php if ($mission['status'] === 'completed'): ?>
                                    <div class="detail-item">
                                        <span class="label">Stars:</span>
                                        <span class="value stars">
                                            <?php
                                            for ($i = 1; $i <= 3; $i++) {
                                                echo $i <= $mission['stars_earned'] ? '⭐' : '☆';
                                            }
                                            ?>
                                        </span>
                                    </div>

                                    <div class="detail-item">
                                        <span class="label">Best Score:</span>
                                        <span class="value"><?php echo number_format($mission['best_score']); ?> pts</span>
                                    </div>

                                    <?php if ($mission['completed_at']): ?>
                                        <div class="detail-item">
                                            <span class="label">Completed:</span>
                                            <span class="value"><?php echo date('M d, Y', strtotime($mission['completed_at'])); ?></span>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php else: ?>
            <p class="no-data">No mission progress data available.</p>
        <?php endif; ?>
    </div>

    <!-- Recent Quiz Activity -->
    <?php if (!empty($recentQuizzes)): ?>
    <div class="recent-activity">
        <h3><i class="fas fa-history"></i> Recent Quiz Activity</h3>
        <div class="activity-list">
            <?php foreach ($recentQuizzes as $quiz): ?>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div class="activity-content">
                        <h4><?php echo htmlspecialchars($quiz['department_name'] . ' - ' . $quiz['level_name']); ?></h4>
                        <p>Score: <?php echo $quiz['score']; ?>/<?php echo $quiz['total_questions']; ?> 
                           (<?php echo round(($quiz['score'] / $quiz['total_questions']) * 100); ?>%)</p>
                        <span class="activity-date"><?php echo date('M d, Y H:i', strtotime($quiz['created_at'])); ?></span>
                    </div>
                    <div class="activity-score">
                        <?php 
                        $percentage = ($quiz['score'] / $quiz['total_questions']) * 100;
                        $scoreClass = $percentage >= 70 ? 'good' : ($percentage >= 50 ? 'average' : 'poor');
                        ?>
                        <span class="score-badge <?php echo $scoreClass; ?>">
                            <?php echo round($percentage); ?>%
                        </span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Action Buttons -->
    <div class="progress-actions">
        <?php if (($student['is_approved'] ?? 0) != 1): ?>
            <button class="btn btn-success" onclick="approveStudent(<?php echo $student['id']; ?>)">
                <i class="fas fa-check"></i>
                Approve Student
            </button>
        <?php endif; ?>
        <button class="btn btn-primary" onclick="viewFullProfile(<?php echo $student['id']; ?>)">
            <i class="fas fa-user"></i>
            VIEW FULL PROFILE
        </button>
        <button class="btn btn-info" onclick="sendMessage(<?php echo $student['id']; ?>)">
            <i class="fas fa-envelope"></i>
            Send Message
        </button>
    </div>
</div>

<style>
.student-progress-details {
    max-width: 100%;
}

.progress-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.student-avatar-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.student-details h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.progress-stats .stat-item {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-stats .stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.subject-performance, .recent-activity {
    margin-bottom: 2rem;
}

.subject-performance h3, .recent-activity h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: #1a202c;
    font-weight: 600;
}

.performance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 0.75rem;
}

.performance-bar {
    width: 100px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 1rem;
}

.bar-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.bar-fill.good { background: #48bb78; }
.bar-fill.average { background: #ed8936; }
.bar-fill.poor { background: #e53e3e; }

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 0.75rem;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-date {
    font-size: 0.875rem;
    color: #718096;
}

.progress-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

/* Mission Progress Styles */
.mission-progress {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.mission-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.mission-stat {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.mission-stat .stat-icon {
    width: 40px;
    height: 40px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.mission-stat .stat-content h4 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
    color: #1e293b;
}

.mission-stat .stat-content p {
    margin: 0;
    color: #64748b;
    font-size: 0.9rem;
}

.levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.mission-level {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.mission-level.completed {
    border-color: #10b981;
    background: #f0fdf4;
}

.mission-level.available {
    border-color: #3b82f6;
    background: #eff6ff;
}

.mission-level.in_progress {
    border-color: #f59e0b;
    background: #fffbeb;
}

.mission-level.locked {
    border-color: #9ca3af;
    background: #f9fafb;
    opacity: 0.7;
}

.level-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.level-number {
    font-weight: bold;
    color: #1e293b;
}

.level-status {
    font-size: 1.2rem;
}

.mission-level h5 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 1.1rem;
}

.level-details {
    display: grid;
    gap: 0.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
}

.detail-item .label {
    color: #6b7280;
    font-size: 0.9rem;
}

.detail-item .value {
    font-weight: 500;
    color: #1f2937;
}

.difficulty-easy { color: #10b981; }
.difficulty-medium { color: #f59e0b; }
.difficulty-hard { color: #ef4444; }

.stars {
    font-size: 1.1rem;
}

.no-data {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 2rem;
}
</style>
