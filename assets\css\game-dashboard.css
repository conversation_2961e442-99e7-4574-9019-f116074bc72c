/* Gamified Dashboard Styles for AI-Powered LMS */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.game-dashboard {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.game-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    object-fit: cover;
}

.school-info h1 {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.school-info p {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.player-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    justify-content: center;
}

.player-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.player-details h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.player-details p {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.player-stats {
    display: flex;
    gap: 1rem;
}

.player-stats span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.85rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.75rem;
    background: none;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6b7280;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 500;
}

.btn-icon:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-2px);
}

.btn-icon.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Main Content */
.game-main {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

.section-title {
    font-size: 2rem;
    font-weight: 800;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Game Mode Cards */
.game-modes {
    margin-bottom: 3rem;
}

.game-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.game-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.game-card:hover::before {
    transform: scaleX(1);
}

.game-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.quick-game .card-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.endless-mode .card-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.mission-mode .card-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.game-card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
}

.card-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.card-content p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.card-stats {
    display: flex;
    gap: 1rem;
}

.card-stats span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.game-card:hover .card-overlay {
    opacity: 1;
}

.play-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.game-card:hover .play-button {
    transform: translateY(0);
}

.play-button i {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

/* Stats Section */
.stats-section {
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: #6b7280;
    font-weight: 500;
}

/* Achievements Section */
.achievements-section {
    margin-bottom: 2rem;
}

.achievements-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.achievement-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.achievement-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.achievement-content h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.achievement-content p {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.achievement-date {
    font-size: 0.8rem;
    color: #9ca3af;
    font-weight: 500;
}

/* Game Modals */
.game-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
    padding: 2rem;
}

.game-modal.show {
    opacity: 1;
}

.game-modal.closing {
    opacity: 0;
    transform: scale(0.9);
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.game-modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Quick Game Modal */
.game-setup {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.setup-group label {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.difficulty-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.difficulty-btn {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
}

.difficulty-btn:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.difficulty-btn.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.difficulty-btn i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.difficulty-btn span {
    font-weight: 600;
    color: #1f2937;
}

.difficulty-btn small {
    color: #6b7280;
    font-size: 0.8rem;
}

.question-slider {
    text-align: center;
}

.question-slider input[type="range"] {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e5e7eb;
    outline: none;
    margin-bottom: 1rem;
}

.slider-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #667eea;
}

/* Endless Game Modal */
.endless-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
}

.info-card {
    text-align: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.info-card i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.info-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.info-card p {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Mission Game Modal */
.mission-map {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.mission-level {
    background: #f8fafc;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mission-level.completed {
    background: rgba(16, 185, 129, 0.1);
    border-color: #10b981;
    color: #10b981;
}

.mission-level.available {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    color: #667eea;
}

.mission-level.available:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.mission-level.locked {
    opacity: 0.5;
    cursor: not-allowed;
}

.mission-level i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.mission-info {
    text-align: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.mission-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 1rem;
}

.loading-progress {
    height: 100%;
    background: white;
    width: 0%;
    transition: width 2s ease;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .player-info {
        justify-content: flex-start;
    }

    .game-main {
        padding: 1rem;
    }

    .game-cards {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .difficulty-options {
        grid-template-columns: 1fr;
    }

    .endless-info {
        grid-template-columns: 1fr;
    }

    .mission-map {
        grid-template-columns: 1fr;
    }

    /* Reduce particle effects on mobile */
    .ui-particle {
        display: none;
    }
}

/* Enhanced UI Animations */
@keyframes particleFloat {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--end-x), var(--end-y)) scale(0);
        opacity: 0;
    }
}

@keyframes cardGlow {
    0%, 100% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }
}

@keyframes statPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes achievementShine {
    0% {
        background-position: -200% center;
    }
    100% {
        background-position: 200% center;
    }
}

@keyframes iconBounce {
    0% {
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        transform: scale(1.2) rotate(-5deg);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced Interactive Elements */
.ui-particle {
    animation: particleFloat 1s ease-out forwards;
}

.game-card:hover {
    animation: cardGlow 2s ease-in-out infinite;
}

.stat-card:hover {
    animation: statPulse 1s ease-in-out infinite;
}

.achievement-item.unlocked {
    position: relative;
    overflow: hidden;
}

.achievement-item.unlocked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    background-size: 200% 100%;
    animation: achievementShine 2s infinite;
    border-radius: 16px;
}

.game-card:hover .card-icon {
    animation: iconBounce 0.6s ease-in-out infinite alternate;
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}
