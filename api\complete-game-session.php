<?php
/**
 * Complete Game Session API for AI-Powered LMS
 * Finalizes game sessions and awards achievements
 */

require_once '../config/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit();
}

// Validate required fields
$required_fields = ['session_id', 'questions_answered', 'correct_answers', 'wrong_answers', 'points_earned'];
foreach ($required_fields as $field) {
    if (!isset($input[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
        exit();
    }
}

$sessionId = (int)$input['session_id'];
$questionsAnswered = (int)$input['questions_answered'];
$correctAnswers = (int)$input['correct_answers'];
$wrongAnswers = (int)$input['wrong_answers'];
$pointsEarned = (int)$input['points_earned'];
$totalTime = (int)($input['total_time'] ?? 0);
$accuracy = (float)($input['accuracy'] ?? 0);

try {
    // Get session info
    $session = fetchOne("
        SELECT * FROM game_sessions 
        WHERE id = :id AND status = 'active'
    ", ['id' => $sessionId]);
    
    if (!$session) {
        throw new Exception('Invalid or inactive session');
    }
    
    // Preserve existing session data and add completion info
    $existingSessionData = json_decode($session['session_data'], true) ?? [];
    $completionData = [
        'total_time' => $totalTime,
        'accuracy' => $accuracy,
        'completed_at' => date('Y-m-d H:i:s')
    ];
    $updatedSessionData = array_merge($existingSessionData, $completionData);

    // Update game session
    $updated = updateRecord('game_sessions', [
        'questions_answered' => $questionsAnswered,
        'correct_answers' => $correctAnswers,
        'wrong_answers' => $wrongAnswers,
        'points_earned' => $pointsEarned,
        'status' => 'completed',
        'completed_at' => date('Y-m-d H:i:s'),
        'session_data' => json_encode($updatedSessionData)
    ], 'id = ?', [$sessionId]);
    
    if (!$updated) {
        throw new Exception('Failed to update session');
    }
    
    // Check and award achievements
    $achievements = checkAndAwardAchievements($session['student_id'], $session, $pointsEarned, $correctAnswers, $accuracy);
    
    // Update leaderboards
    updateLeaderboards($session['student_id'], $session['game_mode'], $pointsEarned);
    
    // Update student statistics
    updateStudentStatistics($session['student_id'], $session, $pointsEarned, $questionsAnswered, $correctAnswers, $totalTime);

    // Handle mission-specific updates
    $missionResult = null;
    if ($session['game_mode'] === 'mission') {
        error_log("Processing mission completion for student {$session['student_id']}, points: $pointsEarned");
        $missionResult = updateMissionProgress($session['student_id'], $session, $pointsEarned, $accuracy, $input);
    }

    $response = [
        'success' => true,
        'message' => 'Game session completed successfully',
        'achievements' => $achievements,
        'final_score' => $pointsEarned,
        'accuracy' => $accuracy
    ];

    // Add mission-specific data if available
    if ($missionResult) {
        $response = array_merge($response, $missionResult);
    }

    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Complete game session error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to complete session: ' . $e->getMessage()
    ]);
}

function checkAndAwardAchievements($studentId, $session, $pointsEarned, $correctAnswers, $accuracy) {
    $achievements = [];
    
    try {
        // Get student's total points
        $totalPoints = fetchOne("
            SELECT COALESCE(SUM(points_earned), 0) as total 
            FROM game_sessions 
            WHERE student_id = :id AND status = 'completed'
        ", ['id' => $studentId])['total'] ?? 0;
        
        // First game achievement
        $gamesPlayed = fetchOne("
            SELECT COUNT(*) as count 
            FROM game_sessions 
            WHERE student_id = :id AND status = 'completed'
        ", ['id' => $studentId])['count'] ?? 0;
        
        if ($gamesPlayed == 1) {
            $achievements[] = awardAchievement($studentId, 'First Game', 'Completed your first game!', 'fas fa-play', 25);
        }
        
        // Points milestones
        $pointsMilestones = [100, 500, 1000, 2500, 5000, 10000];
        foreach ($pointsMilestones as $milestone) {
            if ($totalPoints >= $milestone && !hasAchievement($studentId, "Points Master {$milestone}")) {
                $achievements[] = awardAchievement($studentId, "Points Master {$milestone}", "Earned {$milestone} total points!", 'fas fa-star', $milestone / 10);
            }
        }
        
        // Perfect score achievement
        if ($accuracy == 100 && $correctAnswers >= 5) {
            $achievements[] = awardAchievement($studentId, 'Perfect Score', 'Achieved 100% accuracy!', 'fas fa-trophy', 100);
        }
        
        // Speed demon (high score in quick time)
        if ($session['game_mode'] == 'quick' && $pointsEarned >= 200) {
            $achievements[] = awardAchievement($studentId, 'Speed Demon', 'High score in quick game!', 'fas fa-bolt', 50);
        }
        
        // Streak achievements
        $streak = calculateCurrentStreak($studentId);
        $streakMilestones = [5, 10, 20, 50];
        foreach ($streakMilestones as $milestone) {
            if ($streak >= $milestone && !hasAchievement($studentId, "Streak {$milestone}")) {
                $achievements[] = awardAchievement($studentId, "Streak {$milestone}", "Answered {$milestone} questions correctly in a row!", 'fas fa-fire', $milestone * 2);
            }
        }
        
    } catch (Exception $e) {
        error_log("Achievement check error: " . $e->getMessage());
    }
    
    return $achievements;
}

function awardAchievement($studentId, $title, $description, $icon, $pointsReward) {
    // Check if already has this achievement
    if (hasAchievement($studentId, $title)) {
        return null;
    }
    
    $achievementId = insertRecord('student_achievements', [
        'student_id' => $studentId,
        'achievement_type' => 'points',
        'title' => $title,
        'description' => $description,
        'icon' => $icon,
        'points_reward' => $pointsReward
    ]);
    
    if ($achievementId) {
        return [
            'id' => $achievementId,
            'title' => $title,
            'description' => $description,
            'icon' => $icon,
            'points_reward' => $pointsReward
        ];
    }
    
    return null;
}

function hasAchievement($studentId, $title) {
    $existing = fetchOne("
        SELECT id FROM student_achievements 
        WHERE student_id = :student_id AND title = :title
    ", [
        'student_id' => $studentId,
        'title' => $title
    ]);
    
    return $existing !== null;
}

function calculateCurrentStreak($studentId) {
    // Get recent answers in order
    $recentAnswers = fetchAll("
        SELECT gq.is_correct 
        FROM game_questions gq
        JOIN game_sessions gs ON gq.session_id = gs.id
        WHERE gs.student_id = :student_id 
        AND gq.answered_at IS NOT NULL
        ORDER BY gq.answered_at DESC
        LIMIT 100
    ", ['student_id' => $studentId]);
    
    $streak = 0;
    foreach ($recentAnswers as $answer) {
        if ($answer['is_correct']) {
            $streak++;
        } else {
            break;
        }
    }
    
    return $streak;
}

function updateLeaderboards($studentId, $gameMode, $pointsEarned) {
    try {
        // Update or insert leaderboard entry
        $existing = fetchOne("
            SELECT id, score FROM game_leaderboards 
            WHERE student_id = :student_id AND game_mode = :game_mode AND period = 'all_time'
        ", [
            'student_id' => $studentId,
            'game_mode' => $gameMode
        ]);
        
        if ($existing) {
            // Update if this is a higher score
            if ($pointsEarned > $existing['score']) {
                updateRecord('game_leaderboards', [
                    'score' => $pointsEarned,
                    'recorded_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$existing['id']]);
            }
        } else {
            // Insert new leaderboard entry
            insertRecord('game_leaderboards', [
                'student_id' => $studentId,
                'game_mode' => $gameMode,
                'score' => $pointsEarned,
                'period' => 'all_time'
            ]);
        }
        
        // Update overall leaderboard
        $totalPoints = fetchOne("
            SELECT COALESCE(SUM(points_earned), 0) as total 
            FROM game_sessions 
            WHERE student_id = :id AND status = 'completed'
        ", ['id' => $studentId])['total'] ?? 0;
        
        $overallEntry = fetchOne("
            SELECT id FROM game_leaderboards 
            WHERE student_id = :student_id AND game_mode = 'overall' AND period = 'all_time'
        ", ['student_id' => $studentId]);
        
        if ($overallEntry) {
            updateRecord('game_leaderboards', [
                'score' => $totalPoints,
                'recorded_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$overallEntry['id']]);
        } else {
            insertRecord('game_leaderboards', [
                'student_id' => $studentId,
                'game_mode' => 'overall',
                'score' => $totalPoints,
                'period' => 'all_time'
            ]);
        }
        
    } catch (Exception $e) {
        error_log("Leaderboard update error: " . $e->getMessage());
    }
}

function updateStudentStatistics($studentId, $session, $pointsEarned, $questionsAnswered, $correctAnswers, $totalTime) {
    try {
        $today = date('Y-m-d');
        
        // Update daily statistics
        $dailyStats = fetchOne("
            SELECT * FROM game_statistics 
            WHERE student_id = :student_id AND stat_type = 'daily' AND period_start = :date
        ", [
            'student_id' => $studentId,
            'date' => $today
        ]);
        
        if ($dailyStats) {
            updateRecord('game_statistics', [
                'games_played' => $dailyStats['games_played'] + 1,
                'total_points' => $dailyStats['total_points'] + $pointsEarned,
                'questions_answered' => $dailyStats['questions_answered'] + $questionsAnswered,
                'correct_answers' => $dailyStats['correct_answers'] + $correctAnswers,
                'accuracy_percentage' => (($dailyStats['correct_answers'] + $correctAnswers) / ($dailyStats['questions_answered'] + $questionsAnswered)) * 100,
                'average_time_per_question' => $totalTime > 0 ? $totalTime / $questionsAnswered : 0
            ], 'id = ?', [$dailyStats['id']]);
        } else {
            insertRecord('game_statistics', [
                'student_id' => $studentId,
                'stat_type' => 'daily',
                'games_played' => 1,
                'total_points' => $pointsEarned,
                'questions_answered' => $questionsAnswered,
                'correct_answers' => $correctAnswers,
                'accuracy_percentage' => ($correctAnswers / $questionsAnswered) * 100,
                'average_time_per_question' => $totalTime > 0 ? $totalTime / $questionsAnswered : 0,
                'period_start' => $today,
                'period_end' => $today
            ]);
        }
        
    } catch (Exception $e) {
        error_log("Statistics update error: " . $e->getMessage());
    }
}

function updateMissionProgress($studentId, $session, $pointsEarned, $accuracy, $input) {
    try {
        // Get level_id from session_data
        $sessionData = json_decode($session['session_data'], true);
        $levelId = $sessionData['mission_level_id'] ?? $sessionData['level_id'] ?? null;

        if (!$levelId) {
            error_log("Mission completion: No level_id found in session data. Session data: " . json_encode($sessionData));
            return;
        }

        // Verify that all questions were answered correctly (mission completion requirement)
        $questionsAnswered = $input['questions_answered'] ?? 0;
        $correctAnswers = $input['correct_answers'] ?? 0;
        $questionsRequired = $sessionData['questions_required'] ?? 0;

        if ($questionsAnswered < $questionsRequired) {
            error_log("Mission incomplete: Student $studentId answered $questionsAnswered/$questionsRequired questions for level $levelId");
            return; // Don't mark as completed if not all questions answered
        }

        // NEW REQUIREMENT: All questions must be answered correctly to unlock next level
        if ($correctAnswers < $questionsRequired) {
            error_log("Mission not perfect: Student $studentId got $correctAnswers/$questionsRequired correct for level $levelId - marking as completed but not unlocking next level");

            // Still mark this level as completed (with lower stars) but don't unlock next level
            $levelCompleted = true;
            $unlockNextLevel = false;
        } else {
            error_log("Mission perfect: Student $studentId got all $correctAnswers/$questionsRequired correct for level $levelId - unlocking next level");
            $levelCompleted = true;
            $unlockNextLevel = true;
        }

        // Get level info for logging
        $levelInfo = fetchOne("SELECT level_number, title FROM mission_levels WHERE id = ?", [$levelId]);
        $levelDisplay = $levelInfo ? "Level {$levelInfo['level_number']} ({$levelInfo['title']})" : "Level ID $levelId";

        error_log("🎯 Mission completion: Student $studentId completed $levelDisplay - Points: $pointsEarned, Accuracy: $accuracy%, Questions: $questionsAnswered/$questionsRequired");

        // Calculate stars based on performance
        $starsEarned = calculateStarsEarned($accuracy, $input['stars_earned'] ?? null);

        // Get current mission progress
        $currentProgress = fetchOne("
            SELECT * FROM student_mission_progress
            WHERE student_id = :student_id AND level_id = :level_id
        ", [
            'student_id' => $studentId,
            'level_id' => $levelId
        ]);

        if ($currentProgress) {
            // Update existing progress
            $updateData = [
                'status' => 'completed',
                'attempts' => $currentProgress['attempts'] + 1,
                'completed_at' => date('Y-m-d H:i:s')
            ];

            // Update best score and stars if this attempt is better
            if ($pointsEarned > $currentProgress['best_score']) {
                $updateData['best_score'] = $pointsEarned;
            }
            if ($starsEarned > $currentProgress['stars_earned']) {
                $updateData['stars_earned'] = $starsEarned;
            }

            updateRecord('student_mission_progress', $updateData,
                'student_id = ? AND level_id = ?', [
                $studentId,
                $levelId
            ]);

            error_log("Updated existing mission progress for student $studentId, level $levelId - Status: completed, Stars: $starsEarned, Attempts: " . ($currentProgress['attempts'] + 1));
        } else {
            // Create new progress record
            insertRecord('student_mission_progress', [
                'student_id' => $studentId,
                'level_id' => $levelId,
                'status' => 'completed',
                'attempts' => 1,
                'best_score' => $pointsEarned,
                'stars_earned' => $starsEarned,
                'completed_at' => date('Y-m-d H:i:s'),
                'unlocked_at' => date('Y-m-d H:i:s')
            ]);

            error_log("Created new mission progress for student $studentId, level $levelId - Status: completed, Stars: $starsEarned");
        }

        // Unlock next level only if all questions were answered correctly
        $unlockResult = ['unlocked' => false, 'level_info' => null];
        if ($unlockNextLevel) {
            $unlockResult = unlockNextMissionLevel($studentId, $levelId);
        }

        // Check if this is the final level (Level 8)
        $currentLevel = fetchOne("SELECT level_number FROM mission_levels WHERE id = ?", [$levelId]);
        $totalLevels = fetchOne("SELECT COUNT(*) as total FROM mission_levels WHERE is_active = 1")['total'];
        $isFinalLevel = $currentLevel && $currentLevel['level_number'] == $totalLevels;

        // Return mission completion result
        $result = [
            'mission_completed' => true,
            'level_completed' => $levelDisplay,
            'stars_earned' => $starsEarned,
            'perfect_score' => $unlockNextLevel,
            'next_level_unlocked' => $unlockResult['unlocked'] ?? false,
            'next_level_info' => $unlockResult['level_info'] ?? null,
            'unlock_message' => $unlockNextLevel ?
                'Perfect score! Next level unlocked!' :
                'Level completed! Get all questions correct to unlock the next level.'
        ];

        // Add special message for final level completion
        if ($isFinalLevel && $unlockNextLevel) {
            $result['final_mission_completed'] = true;
            $result['congratulations_message'] = '🎉 INCREDIBLE! You have conquered ALL missions and proven yourself as the ultimate learning champion! Your dedication, skill, and perseverance have led you to complete every challenge. You are truly AMAZING! 🌟';
            $result['unlock_message'] = '🏆 MISSION MASTERY ACHIEVED! You have beaten all missions!';
            error_log("🎉 FINAL MISSION COMPLETED! Student $studentId has beaten all missions!");
        }

        return $result;

    } catch (Exception $e) {
        error_log("Mission progress update error: " . $e->getMessage());
        return null;
    }
}

function calculateStarsEarned($accuracy, $providedStars = null) {
    // Use provided stars if available, otherwise calculate based on accuracy
    if ($providedStars !== null && $providedStars >= 1 && $providedStars <= 3) {
        return (int)$providedStars;
    }

    // Calculate stars based on accuracy
    if ($accuracy >= 90) return 3;
    if ($accuracy >= 70) return 2;
    if ($accuracy >= 50) return 1;
    return 1; // Minimum 1 star for completion
}

function unlockNextMissionLevel($studentId, $currentLevelId) {
    try {
        // Get current level number
        $currentLevel = fetchOne("
            SELECT level_number FROM mission_levels WHERE id = :id
        ", ['id' => $currentLevelId]);

        if (!$currentLevel) {
            error_log("Current level not found for level_id: $currentLevelId");
            return ['unlocked' => false, 'level_info' => null];
        }

        $currentLevelNumber = $currentLevel['level_number'];
        $nextLevelNumber = $currentLevelNumber + 1;

        // Get next level
        $nextLevel = fetchOne("
            SELECT id, level_number, title FROM mission_levels
            WHERE level_number = :next_level AND is_active = 1
        ", ['next_level' => $nextLevelNumber]);

        if (!$nextLevel) {
            error_log("No next level found after level $currentLevelNumber");
            return ['unlocked' => false, 'level_info' => null]; // No next level
        }

        // Check if next level is already unlocked or completed
        $nextLevelProgress = fetchOne("
            SELECT status FROM student_mission_progress
            WHERE student_id = :student_id AND level_id = :level_id
        ", [
            'student_id' => $studentId,
            'level_id' => $nextLevel['id']
        ]);

        if ($nextLevelProgress && in_array($nextLevelProgress['status'], ['available', 'in_progress', 'completed'])) {
            error_log("Next level {$nextLevel['id']} is already unlocked for student $studentId (status: {$nextLevelProgress['status']})");
            return ['unlocked' => false, 'level_info' => null]; // Already unlocked
        }

        // IMMEDIATE UNLOCK: Completing current level immediately unlocks the next one
        error_log("Unlocking next level {$nextLevel['id']} (Level {$nextLevelNumber}: {$nextLevel['title']}) for student $studentId (completed level $currentLevelNumber)");

        executeQuery("
            INSERT INTO student_mission_progress (student_id, level_id, status, unlocked_at, created_at)
            VALUES (:student_id, :level_id, 'available', NOW(), NOW())
            ON DUPLICATE KEY UPDATE
                status = CASE WHEN status = 'locked' THEN 'available' ELSE status END,
                unlocked_at = CASE WHEN status = 'locked' THEN NOW() ELSE unlocked_at END,
                updated_at = NOW()
        ", [
            'student_id' => $studentId,
            'level_id' => $nextLevel['id']
        ]);

        // Verify the unlock was successful
        $verifyUnlock = fetchOne("
            SELECT status FROM student_mission_progress
            WHERE student_id = :student_id AND level_id = :level_id
        ", [
            'student_id' => $studentId,
            'level_id' => $nextLevel['id']
        ]);

        if ($verifyUnlock && $verifyUnlock['status'] === 'available') {
            error_log("✅ Successfully unlocked Level {$nextLevelNumber} for student $studentId");
            return [
                'unlocked' => true,
                'level_info' => [
                    'level_number' => $nextLevel['level_number'],
                    'title' => $nextLevel['title'],
                    'id' => $nextLevel['id']
                ]
            ];
        } else {
            error_log("❌ Failed to unlock Level {$nextLevelNumber} for student $studentId - Status: " . ($verifyUnlock['status'] ?? 'not found'));
            return ['unlocked' => false, 'level_info' => null];
        }

    } catch (Exception $e) {
        error_log("Next level unlock error: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        return ['unlocked' => false, 'level_info' => null];
    }
}
?>
