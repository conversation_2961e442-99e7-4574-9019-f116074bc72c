<?php
/**
 * Mission Game Interface - Immersive Level-based Learning
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

// Get session ID from URL
$sessionId = $_GET['session_id'] ?? null;
if (!$sessionId) {
    header('Location: mission-mode.php');
    exit();
}

// Get mission session details
$missionSession = fetchOne("
    SELECT gs.*,
           JSON_EXTRACT(gs.session_data, '$.mission_level_id') as mission_level_id,
           ml.title, ml.description, ml.difficulty, ml.questions_required,
           ml.level_number, ml.boss_level, ml.points_reward,
           s.first_name, s.last_name, s.department_id, s.academic_level_id,
           d.name as department_name, al.level_name
    FROM game_sessions gs
    JOIN mission_levels ml ON JSON_EXTRACT(gs.session_data, '$.mission_level_id') = ml.id
    JOIN students s ON gs.student_id = s.id
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE gs.id = :session_id AND gs.student_id = :student_id AND gs.status = 'active' AND gs.game_mode = 'mission'
", [
    'session_id' => $sessionId,
    'student_id' => $_SESSION['user_id']
]);

if (!$missionSession) {
    header('Location: mission-mode.php?error=Invalid mission session');
    exit();
}

$pageTitle = "Mission: " . $missionSession['title'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - LMS</title>
    <link rel="stylesheet" href="../assets/css/mission-game.css?v=<?php echo time(); ?>">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body class="mission-game-page">
    <div class="mission-game-container">
        <!-- Mission Header -->
        <header class="mission-header">
            <div class="header-content">
                <div class="mission-info">
                    <div class="mission-badge">
                        <i class="fas fa-flag"></i>
                        <span>Level <?php echo $missionSession['level_number']; ?></span>
                    </div>
                    <h1><?php echo htmlspecialchars($missionSession['title']); ?></h1>
                    <div class="difficulty-indicator">
                        <span class="difficulty-badge <?php echo strtolower($missionSession['difficulty_level']); ?>">
                            <?php echo ucfirst($missionSession['difficulty_level']); ?>
                        </span>
                    </div>
                </div>
                
                <div class="mission-stats">
                    <div class="stat-item">
                        <i class="fas fa-heart"></i>
                        <span id="lives-count">3</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span id="current-score">0</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span id="timer"><?php echo isset($missionSession['time_limit']) ? $missionSession['time_limit'] : '10'; ?>:00</span>
                    </div>
                </div>
                
                <div class="mission-actions">
                    <button class="btn-icon" onclick="pauseMission()" title="Pause">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="btn-icon" onclick="quitMission()" title="Quit">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <!-- Mission Progress -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="mission-progress"></div>
                </div>
                <div class="progress-text">
                    <span id="current-question">0</span> / <span id="total-questions"><?php echo $missionSession['questions_count']; ?></span>
                </div>
            </div>
        </header>

        <!-- Main Game Area -->
        <main class="mission-main">
            <!-- Story Introduction -->
            <div id="story-intro" class="story-screen">
                <div class="story-content animate__animated animate__fadeIn">
                    <div class="story-icon">
                        <i class="fas fa-scroll"></i>
                    </div>
                    <h2>Mission Briefing</h2>
                    <div class="story-text">
                        <?php
                        $storyIntro = isset($missionSession['story_intro']) ? $missionSession['story_intro'] :
                                     "Welcome to " . $missionSession['title'] . "! Complete this mission to earn " .
                                     $missionSession['points_reward'] . " points and unlock new challenges.";
                        echo nl2br(htmlspecialchars($storyIntro));
                        ?>
                    </div>
                    <button class="btn-primary btn-large" onclick="startMission()">
                        <i class="fas fa-rocket"></i>
                        Begin Mission
                    </button>
                </div>
            </div>

            <!-- Loading Screen -->
            <div id="loading-screen" class="loading-screen" style="display: none;">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <h2>Preparing Mission</h2>
                    <p>AI is generating your personalized challenges...</p>
                    <div class="loading-progress">
                        <div class="loading-bar" id="loading-bar"></div>
                    </div>
                </div>
            </div>

            <!-- Question Container -->
            <div id="question-container" class="question-container" style="display: none;">
                <div class="question-card animate__animated">
                    <div class="question-header">
                        <div class="question-number">
                            Question <span id="question-num">1</span>
                        </div>
                        <div class="lives-display">
                            <div class="life-heart active" data-life="1">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="life-heart active" data-life="2">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="life-heart active" data-life="3">
                                <i class="fas fa-heart"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="question-content">
                        <h2 id="question-text">Loading question...</h2>
                    </div>
                    
                    <div class="options-container">
                        <button class="option-btn" data-option="A" onclick="selectAnswer('A')">
                            <span class="option-letter">A</span>
                            <span class="option-text" id="option-a">Option A</span>
                        </button>
                        <button class="option-btn" data-option="B" onclick="selectAnswer('B')">
                            <span class="option-letter">B</span>
                            <span class="option-text" id="option-b">Option B</span>
                        </button>
                        <button class="option-btn" data-option="C" onclick="selectAnswer('C')">
                            <span class="option-letter">C</span>
                            <span class="option-text" id="option-c">Option C</span>
                        </button>
                        <button class="option-btn" data-option="D" onclick="selectAnswer('D')">
                            <span class="option-letter">D</span>
                            <span class="option-text" id="option-d">Option D</span>
                        </button>
                    </div>
                    
                    <div class="question-actions">
                        <button class="btn-submit" id="submit-btn" onclick="submitAnswer()" disabled>
                            <i class="fas fa-check"></i>
                            Submit Answer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mission Complete Screen -->
            <div id="mission-complete" class="mission-complete-screen" style="display: none;">
                <div class="complete-content animate__animated animate__bounceIn">
                    <div class="complete-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h1>Mission Complete!</h1>
                    <div class="story-completion">
                        <p id="completion-story"><?php echo nl2br(htmlspecialchars(isset($missionSession['story_completion']) ? $missionSession['story_completion'] : 'Congratulations! You have successfully completed this mission!')); ?></p>
                    </div>
                    
                    <div class="mission-results">
                        <div class="result-stat">
                            <div class="stat-value" id="final-score">0</div>
                            <div class="stat-label">Final Score</div>
                        </div>
                        <div class="result-stat">
                            <div class="stat-value" id="stars-earned">0</div>
                            <div class="stat-label">Stars Earned</div>
                        </div>
                        <div class="result-stat">
                            <div class="stat-value" id="accuracy-rate">0%</div>
                            <div class="stat-label">Accuracy</div>
                        </div>
                    </div>
                    
                    <div class="complete-actions">
                        <button class="btn-primary" onclick="nextMission()">
                            <i class="fas fa-arrow-right"></i>
                            Next Mission
                        </button>
                        <button class="btn-secondary" onclick="backToMissions()">
                            <i class="fas fa-map"></i>
                            Mission Map
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mission Failed Screen -->
            <div id="mission-failed" class="mission-failed-screen" style="display: none;">
                <div class="failed-content animate__animated animate__shakeX">
                    <div class="failed-icon">
                        <i class="fas fa-skull-crossbones"></i>
                    </div>
                    <h1>Mission Failed</h1>
                    <p>Don't give up! Every failure is a step closer to success.</p>
                    
                    <div class="failed-actions">
                        <button class="btn-primary" onclick="retryMission()">
                            <i class="fas fa-redo"></i>
                            Retry Mission
                        </button>
                        <button class="btn-secondary" onclick="backToMissions()">
                            <i class="fas fa-map"></i>
                            Mission Map
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mission Data -->
    <script>
        const missionConfig = {
            sessionId: <?php echo $sessionId; ?>,
            levelId: <?php echo $missionSession['mission_level_id']; ?>,
            studentId: <?php echo $_SESSION['user_id']; ?>,
            totalQuestions: <?php echo $missionSession['questions_required']; ?>,
            timeLimit: <?php echo isset($missionSession['time_limit']) ? $missionSession['time_limit'] : 10; ?>,
            difficulty: '<?php echo $missionSession['difficulty']; ?>',
            departmentId: <?php echo $missionSession['department_id']; ?>,
            academicLevelId: <?php echo $missionSession['academic_level_id']; ?>,
            pointsReward: <?php echo $missionSession['points_reward']; ?>
        };
    </script>
    
    <script src="../assets/js/mission-game.js"></script>
</body>
</html>
