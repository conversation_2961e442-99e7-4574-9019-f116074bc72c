/**
 * Interactive Quiz Results CSS
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.result-container {
    max-width: 1000px;
    margin: 0 auto;
    min-height: 100vh;
}

/* Celebration Header */
.celebration-header {
    padding: 40px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.celebration-animation {
    position: relative;
    z-index: 2;
}

.confetti {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background-image: 
        radial-gradient(circle, #ff6b6b 2px, transparent 2px),
        radial-gradient(circle, #4ecdc4 2px, transparent 2px),
        radial-gradient(circle, #45b7d1 2px, transparent 2px),
        radial-gradient(circle, #f9ca24 2px, transparent 2px),
        radial-gradient(circle, #6c5ce7 2px, transparent 2px);
    background-size: 50px 50px, 60px 60px, 70px 70px, 80px 80px, 90px 90px;
    background-position: 0 0, 10px 10px, 20px 20px, 30px 30px, 40px 40px;
    animation: confettiFall 3s ease-out infinite;
    opacity: 0.7;
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}

.celebration-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 40px;
    display: inline-block;
    animation: celebrationBounce 1s ease-out;
}

@keyframes celebrationBounce {
    0% {
        transform: scale(0.3) translateY(50px);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) translateY(-10px);
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

.celebration-emoji {
    font-size: 80px;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    80% {
        transform: translateY(-10px);
    }
}

.celebration-title {
    font-size: 48px;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.celebration-subtitle {
    font-size: 20px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
}

.celebration-message {
    font-size: 16px;
    color: #718096;
}

/* Results Main */
.results-main {
    padding: 0 20px 40px;
}

.results-summary {
    background: white;
    border-radius: 25px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 40px;
    flex-wrap: wrap;
}

/* Score Circle */
.score-circle {
    flex-shrink: 0;
}

.score-ring {
    width: 200px;
    height: 200px;
    position: relative;
    background: conic-gradient(
        from 0deg,
        #e2e8f0 0deg,
        #e2e8f0 calc(var(--percentage) * 3.6deg),
        #48bb78 calc(var(--percentage) * 3.6deg),
        #48bb78 360deg
    );
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: scoreReveal 2s ease-out;
}

@keyframes scoreReveal {
    from {
        transform: rotate(-90deg);
    }
    to {
        transform: rotate(0deg);
    }
}

.score-ring::before {
    content: '';
    position: absolute;
    width: 160px;
    height: 160px;
    background: white;
    border-radius: 50%;
}

.score-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.score-number {
    font-size: 48px;
    font-weight: 800;
    color: #2d3748;
    line-height: 1;
}

.score-label {
    font-size: 16px;
    font-weight: 600;
    color: #718096;
    margin-top: 5px;
}

/* Stats Grid */
.stats-grid {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
}

.stat-card {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
}

.stat-card.correct {
    background: linear-gradient(135deg, #c6f6d5, #9ae6b4);
}

.stat-card.incorrect {
    background: linear-gradient(135deg, #fed7d7, #feb2b2);
}

.stat-card.xp {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.stat-card.difficulty {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
}

.stat-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
}

/* Progress Update */
.progress-update {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.progress-update h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 20px;
    text-align: center;
}

.progress-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.progress-icon {
    font-size: 32px;
    flex-shrink: 0;
}

.progress-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.progress-content p {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.btn {
    border: none;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
    font-size: 16px;
}

.btn-large {
    padding: 16px 32px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* Answer Review */
.answer-review {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.answer-review.hidden {
    display: none;
}

.answer-review h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 25px;
    text-align: center;
}

.answers-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.answer-card {
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
}

.answer-card.correct {
    border-color: #48bb78;
    background: linear-gradient(135deg, #c6f6d510, #9ae6b410);
}

.answer-card.incorrect {
    border-color: #f56565;
    background: linear-gradient(135deg, #fed7d710, #feb2b210);
}

.answer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.question-number {
    background: #667eea;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.status-correct {
    color: #22543d;
    font-weight: 600;
}

.status-incorrect {
    color: #742a2a;
    font-weight: 600;
}

.question-text {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
    line-height: 1.4;
}

.answer-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    background: #f8f9fa;
}

.option.correct-answer {
    background: #c6f6d5;
    border: 2px solid #48bb78;
}

.option.wrong-selection {
    background: #fed7d7;
    border: 2px solid #f56565;
}

.option.selected {
    font-weight: 600;
}

.option-letter {
    width: 24px;
    height: 24px;
    background: #e2e8f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 12px;
    flex-shrink: 0;
}

.option.correct-answer .option-letter {
    background: #48bb78;
    color: white;
}

.option.wrong-selection .option-letter {
    background: #f56565;
    color: white;
}

.option-text {
    flex: 1;
}

.option-indicator {
    font-size: 16px;
}

.explanation {
    background: #f0f4f8;
    border-left: 4px solid #667eea;
    padding: 16px;
    border-radius: 8px;
    font-size: 14px;
    color: #4a5568;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .celebration-title {
        font-size: 36px;
    }
    
    .celebration-subtitle {
        font-size: 18px;
    }
    
    .results-summary {
        flex-direction: column;
        text-align: center;
    }
    
    .score-ring {
        width: 150px;
        height: 150px;
    }
    
    .score-ring::before {
        width: 120px;
        height: 120px;
    }
    
    .score-number {
        font-size: 36px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .progress-items {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-large {
        width: 100%;
        max-width: 300px;
    }
}
