/* Endless Mode Styles - Infinite Challenge Game */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.endless-game-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Endless Header */
.endless-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.game-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.endless-badge {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    animation: infinityPulse 2s ease-in-out infinite;
}

.game-info h1 {
    font-size: 1.5rem;
    font-weight: 800;
    color: #1f2937;
    margin: 0;
}

.game-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.stat-item i {
    color: #8b5cf6;
}

.game-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Level Progress */
.level-progress-container {
    padding: 0 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    margin-top: 1rem;
}

.level-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.difficulty-indicator {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.level-progress-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    margin-bottom: 0.5rem;
}

.level-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 4px;
    position: relative;
}

.level-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progressShimmer 2s infinite;
}

.progress-text {
    text-align: center;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

/* Main Game Area */
.endless-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

/* Intro Screen */
.intro-screen {
    width: 100%;
    max-width: 600px;
    text-align: center;
}

.intro-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.intro-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    color: white;
    animation: infinityFloat 3s ease-in-out infinite;
}

.intro-content h2 {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
}

.intro-description p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 2rem;
}

.game-rules {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.rule-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    font-weight: 500;
    color: #374151;
}

.rule-item i {
    color: #8b5cf6;
    font-size: 1.2rem;
}

/* Loading Screen */
.loading-screen {
    width: 100%;
    max-width: 500px;
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

.loading-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.loading-content p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.loading-progress {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-bar {
    height: 100%;
    background: white;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* Question Container */
.question-container {
    width: 100%;
    max-width: 800px;
}

.question-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.question-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.question-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: #6b7280;
}

.streak-counter {
    font-size: 0.9rem;
    font-weight: 600;
    color: #8b5cf6;
}

.lives-display {
    display: flex;
    gap: 0.5rem;
}

.life-heart {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.life-heart.active {
    color: #ef4444;
    animation: heartbeat 2s ease-in-out infinite;
}

.life-heart.lost {
    color: #d1d5db;
    transform: scale(0.8);
}

.question-content {
    margin-bottom: 2rem;
}

.question-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
}

.options-container {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.option-btn {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.option-btn:hover {
    border-color: #8b5cf6;
    background: rgba(139, 92, 246, 0.05);
    transform: translateX(5px);
}

.option-letter {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: #374151;
    flex-shrink: 0;
}

.option-text {
    flex: 1;
    font-weight: 500;
    color: #374151;
}

.question-actions {
    text-align: center;
    margin-bottom: 1rem;
}

/* Power-ups */
.powerups-container {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.powerup-btn {
    width: 60px;
    height: 60px;
    border: 2px solid #8b5cf6;
    border-radius: 12px;
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    position: relative;
}

.powerup-btn:hover:not(:disabled) {
    background: #8b5cf6;
    color: white;
    transform: translateY(-2px);
}

.powerup-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.powerup-btn i {
    font-size: 1.2rem;
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    font-size: 1rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-submit {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-submit:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.btn-submit:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes infinityPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes infinityFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced Endless Animations */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes correctAnswer {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); background-color: #10b981; }
    100% { transform: scale(1); }
}

@keyframes incorrectAnswer {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

@keyframes floatUp {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -150%) scale(1);
    }
}

@keyframes screenShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes streakParticle {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translate(var(--random-x, 0), var(--random-y, 0)) scale(0);
        opacity: 0;
    }
}

@keyframes levelUpParticle {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translate(var(--random-x, 0), var(--random-y, 0)) scale(0);
        opacity: 0;
    }
}

@keyframes pulse-animation {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes achievementGlow {
    0%, 100% { box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3); }
    50% { box-shadow: 0 8px 30px rgba(245, 158, 11, 0.6); }
}

/* Game Over Screen */
.game-over-screen {
    width: 100%;
    max-width: 700px;
    text-align: center;
}

.game-over-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.game-over-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 3rem;
    color: white;
    animation: shake 0.5s ease-in-out infinite;
}

.game-over-content h1 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 2rem;
}

.final-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: #8b5cf6;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.achievement-display {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    margin: 1rem 0;
    animation: achievementGlow 2s ease-in-out infinite;
}

.achievement-display h3 {
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.achievement-item {
    font-weight: 600;
}

.game-over-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Enhanced Interactive Elements */
.option-btn.selected {
    border-color: #8b5cf6 !important;
    background: rgba(139, 92, 246, 0.1) !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2) !important;
}

.option-btn.correct {
    border-color: #10b981 !important;
    background: rgba(16, 185, 129, 0.1) !important;
    color: #10b981 !important;
}

.option-btn.incorrect {
    border-color: #ef4444 !important;
    background: rgba(239, 68, 68, 0.1) !important;
    color: #ef4444 !important;
}

.option-btn.disabled {
    opacity: 0.3;
    pointer-events: none;
}

.pulse-animation {
    animation: pulse-animation 1s infinite;
}

.floating-text {
    animation: floatUp 2s ease-out forwards;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Inter', sans-serif;
    font-weight: 800;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .game-stats {
        gap: 1rem;
    }

    .endless-main {
        padding: 1rem;
    }

    .question-card, .intro-content, .game-over-content {
        padding: 1.5rem;
    }

    .game-rules {
        grid-template-columns: 1fr;
    }

    .powerups-container {
        gap: 0.5rem;
    }

    .powerup-btn {
        width: 50px;
        height: 50px;
    }

    .final-stats {
        grid-template-columns: 1fr;
    }

    .game-over-actions {
        flex-direction: column;
    }

    .floating-text {
        font-size: 1.5rem;
    }

    .pause-content {
        padding: 2rem 1.5rem;
        max-width: 95%;
    }

    .pause-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        padding: 1rem;
    }

    .pause-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }

    .pause-buttons .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Pause Overlay Styles */
.pause-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.pause-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    max-width: 500px;
    width: 90%;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
}

.pause-icon {
    font-size: 4rem;
    color: #8b5cf6;
    margin-bottom: 1rem;
}

.pause-content h2 {
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 2rem;
    font-weight: 700;
}

.pause-content p {
    color: #718096;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.pause-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(139, 92, 246, 0.1);
    border-radius: 15px;
}

.pause-stats .stat-item {
    text-align: center;
}

.pause-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #8b5cf6;
    margin-bottom: 0.25rem;
}

.pause-stats .stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.pause-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.pause-buttons .btn {
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pause-buttons .btn-primary {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.pause-buttons .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

.pause-buttons .btn-secondary {
    background: linear-gradient(135deg, #718096, #a0aec0);
    color: white;
}

.pause-buttons .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(113, 128, 150, 0.4);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
