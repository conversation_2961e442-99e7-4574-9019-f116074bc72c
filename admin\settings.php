<?php
/**
 * System Settings for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_general':
                    $siteName = trim($_POST['site_name']);
                    $siteDescription = trim($_POST['site_description']);
                    $contactEmail = trim($_POST['contact_email']);
                    $timezone = trim($_POST['timezone']);
                    
                    if (!$siteName || !$contactEmail) {
                        throw new Exception('Site name and contact email are required');
                    }
                    
                    // Update or insert settings
                    $settings = [
                        'site_name' => $siteName,
                        'site_description' => $siteDescription,
                        'contact_email' => $contactEmail,
                        'timezone' => $timezone
                    ];
                    
                    foreach ($settings as $key => $value) {
                        execute("
                            INSERT INTO system_settings (setting_key, setting_value, updated_at)
                            VALUES (:key, :value, NOW())
                            ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()
                        ", ['key' => $key, 'value' => $value]);
                    }
                    
                    $success = "General settings updated successfully!";
                    break;
                    
                case 'update_quiz':
                    $defaultQuizTime = (int)$_POST['default_quiz_time'];
                    $questionsPerQuiz = (int)$_POST['questions_per_quiz'];
                    $passingScore = (int)$_POST['passing_score'];
                    $allowRetakes = isset($_POST['allow_retakes']) ? 1 : 0;
                    $showCorrectAnswers = isset($_POST['show_correct_answers']) ? 1 : 0;
                    
                    if ($defaultQuizTime < 1 || $questionsPerQuiz < 1 || $passingScore < 1 || $passingScore > 100) {
                        throw new Exception('Please enter valid quiz settings');
                    }
                    
                    $quizSettings = [
                        'default_quiz_time' => $defaultQuizTime,
                        'questions_per_quiz' => $questionsPerQuiz,
                        'passing_score' => $passingScore,
                        'allow_retakes' => $allowRetakes,
                        'show_correct_answers' => $showCorrectAnswers
                    ];
                    
                    foreach ($quizSettings as $key => $value) {
                        execute("
                            INSERT INTO system_settings (setting_key, setting_value, updated_at)
                            VALUES (:key, :value, NOW())
                            ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()
                        ", ['key' => $key, 'value' => $value]);
                    }
                    
                    $success = "Quiz settings updated successfully!";
                    break;
                    
                case 'update_ai':
                    $aiProvider = trim($_POST['ai_provider']);
                    $aiApiKey = trim($_POST['ai_api_key']);
                    $aiModel = trim($_POST['ai_model']);
                    $questionComplexity = trim($_POST['question_complexity']);
                    
                    $aiSettings = [
                        'ai_provider' => $aiProvider,
                        'ai_api_key' => $aiApiKey ? $aiApiKey : getSetting('ai_api_key'), // Don't overwrite if empty
                        'ai_model' => $aiModel,
                        'question_complexity' => $questionComplexity
                    ];
                    
                    foreach ($aiSettings as $key => $value) {
                        if ($key === 'ai_api_key' && !$value) continue; // Skip empty API key
                        
                        execute("
                            INSERT INTO system_settings (setting_key, setting_value, updated_at)
                            VALUES (:key, :value, NOW())
                            ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()
                        ", ['key' => $key, 'value' => $value]);
                    }
                    
                    $success = "AI settings updated successfully!";
                    break;
                    

            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Helper function to get setting value
function getSetting($key, $default = '') {
    $result = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = :key", ['key' => $key]);
    return $result ? $result['setting_value'] : $default;
}

// Get current settings
$currentSettings = [
    'site_name' => getSetting('site_name', 'AI-Powered LMS'),
    'site_description' => getSetting('site_description', 'Ogbonnaya Onu Polytechnic Learning Management System'),
    'contact_email' => getSetting('contact_email', '<EMAIL>'),
    'timezone' => getSetting('timezone', 'Africa/Lagos'),
    'default_quiz_time' => getSetting('default_quiz_time', '30'),
    'questions_per_quiz' => getSetting('questions_per_quiz', '20'),
    'passing_score' => getSetting('passing_score', '60'),
    'allow_retakes' => getSetting('allow_retakes', '1'),
    'show_correct_answers' => getSetting('show_correct_answers', '1'),
    'ai_provider' => getSetting('ai_provider', 'google'),
    'ai_api_key' => getSetting('ai_api_key', 'AIzaSyBdgNuHjMkjDTrGjByhUoXgDjZh2eF0Ft4'),
    'ai_model' => getSetting('ai_model', 'gemini-1.5-flash-latest'),
    'question_complexity' => getSetting('question_complexity', 'medium')
];



$pageTitle = 'System Settings';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="game-analytics.php" class="nav-item">
                    <i class="fas fa-gamepad"></i>
                    <span>Game Analytics</span>
                </a>
                <a href="settings.php" class="nav-item active">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="admin-profile">
                    <div class="admin-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="admin-info">
                        <div class="admin-name">Administrator</div>
                        <div class="admin-role">System Admin</div>
                    </div>
                </div>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>System Settings</h1>
                    <p>Configure system preferences and parameters</p>
                </div>
                <div class="header-right">
                    <div class="system-status">
                        <span class="status-indicator online"></span>
                        <span>System Online</span>
                    </div>
                </div>
            </header>
            
            <!-- Messages -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- Settings Tabs -->
            <div class="settings-container">
                <div class="settings-tabs">
                    <button class="tab-button active" onclick="showTab('general')">
                        <i class="fas fa-cog"></i>
                        General
                    </button>
                    <button class="tab-button" onclick="showTab('quiz')">
                        <i class="fas fa-clipboard-list"></i>
                        Quiz Settings
                    </button>
                    <button class="tab-button" onclick="showTab('ai')">
                        <i class="fas fa-robot"></i>
                        AI Configuration
                    </button>
                </div>
                
                <!-- General Settings Tab -->
                <div id="general" class="tab-content active">
                    <div class="settings-section">
                        <h3><i class="fas fa-cog"></i> General Settings</h3>

                        <div class="info-box">
                            <i class="fas fa-info-circle" style="color: #17a2b8;"></i>
                            <div class="info-content">
                                <h5>System Configuration</h5>
                                <p>Configure basic system settings including site information, contact details, and timezone preferences.</p>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h4><i class="fas fa-globe"></i> Site Information</h4>
                            <p>Basic information about your learning management system.</p>

                            <form method="POST">
                                <input type="hidden" name="action" value="update_general">

                                <div class="form-group">
                                    <label for="site_name"><i class="fas fa-tag"></i> Site Name *</label>
                                    <input type="text" id="site_name" name="site_name" value="<?php echo htmlspecialchars($currentSettings['site_name']); ?>" required placeholder="Enter your site name">
                                </div>

                                <div class="form-group">
                                    <label for="site_description"><i class="fas fa-align-left"></i> Site Description</label>
                                    <textarea id="site_description" name="site_description" rows="4" placeholder="Describe your learning management system"><?php echo htmlspecialchars($currentSettings['site_description']); ?></textarea>
                                </div>
                            
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="contact_email"><i class="fas fa-envelope"></i> Contact Email *</label>
                                        <input type="email" id="contact_email" name="contact_email" value="<?php echo htmlspecialchars($currentSettings['contact_email']); ?>" required placeholder="<EMAIL>">
                                    </div>

                                    <div class="form-group">
                                        <label for="timezone"><i class="fas fa-clock"></i> Timezone</label>
                                        <select id="timezone" name="timezone">
                                            <option value="Africa/Lagos" <?php echo $currentSettings['timezone'] === 'Africa/Lagos' ? 'selected' : ''; ?>>🇳🇬 Africa/Lagos (WAT)</option>
                                            <option value="UTC" <?php echo $currentSettings['timezone'] === 'UTC' ? 'selected' : ''; ?>>🌍 UTC (Universal)</option>
                                            <option value="America/New_York" <?php echo $currentSettings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>>🇺🇸 America/New_York (EST)</option>
                                            <option value="Europe/London" <?php echo $currentSettings['timezone'] === 'Europe/London' ? 'selected' : ''; ?>>🇬🇧 Europe/London (GMT)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        Save General Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Quiz Settings Tab -->
                <div id="quiz" class="tab-content">
                    <div class="settings-section">
                        <h3><i class="fas fa-clipboard-list"></i> Quiz Configuration</h3>

                        <div class="info-box warning">
                            <i class="fas fa-exclamation-triangle" style="color: #f39c12;"></i>
                            <div class="info-content">
                                <h5>Quiz Settings Impact</h5>
                                <p>Changes to quiz settings will affect all future quizzes. Existing quiz attempts will not be modified.</p>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h4><i class="fas fa-stopwatch"></i> Quiz Timing & Scoring</h4>
                            <p>Configure default quiz parameters and scoring criteria.</p>

                            <form method="POST">
                                <input type="hidden" name="action" value="update_quiz">

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="default_quiz_time"><i class="fas fa-clock"></i> Default Quiz Time (minutes) *</label>
                                        <input type="number" id="default_quiz_time" name="default_quiz_time" value="<?php echo $currentSettings['default_quiz_time']; ?>" min="1" max="180" required placeholder="30">
                                        <small style="color: #64748b; font-size: 0.875rem; margin-top: 0.5rem; display: block;">Recommended: 30-60 minutes</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="questions_per_quiz"><i class="fas fa-list-ol"></i> Questions per Quiz *</label>
                                        <input type="number" id="questions_per_quiz" name="questions_per_quiz" value="<?php echo $currentSettings['questions_per_quiz']; ?>" min="1" max="100" required placeholder="20">
                                        <small style="color: #64748b; font-size: 0.875rem; margin-top: 0.5rem; display: block;">Recommended: 15-25 questions</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="passing_score"><i class="fas fa-percentage"></i> Passing Score (%) *</label>
                                    <input type="number" id="passing_score" name="passing_score" value="<?php echo $currentSettings['passing_score']; ?>" min="1" max="100" required placeholder="60">
                                    <small style="color: #64748b; font-size: 0.875rem; margin-top: 0.5rem; display: block;">Minimum score required to pass a quiz</small>
                                </div>
                            
                                <div class="form-group">
                                    <label><i class="fas fa-cogs"></i> Quiz Options</label>
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="allow_retakes" <?php echo $currentSettings['allow_retakes'] ? 'checked' : ''; ?>>
                                            <span class="checkmark"><i class="fas fa-redo"></i> Allow quiz retakes</span>
                                        </label>

                                        <label class="checkbox-label">
                                            <input type="checkbox" name="show_correct_answers" <?php echo $currentSettings['show_correct_answers'] ? 'checked' : ''; ?>>
                                            <span class="checkmark"><i class="fas fa-eye"></i> Show correct answers after completion</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        Save Quiz Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- AI Configuration Tab -->
                <div id="ai" class="tab-content">
                    <div class="settings-section">
                        <h3><i class="fas fa-robot"></i> AI Question Generation</h3>

                        <div class="info-box success">
                            <i class="fas fa-magic" style="color: #28a745;"></i>
                            <div class="info-content">
                                <h5>Intelligent Question Generation</h5>
                                <p>Configure AI settings to automatically generate diverse, department-specific questions with wide and deep scope coverage.</p>
                            </div>
                        </div>

                        <!-- API Setup Guide Link -->
                        <div class="info-box warning">
                            <i class="fas fa-key" style="color: #f39c12;"></i>
                            <div class="info-content">
                                <h5>Need Help Setting Up Your API Key?</h5>
                                <p>Follow our step-by-step guide to get your OpenAI API key and start generating questions automatically.</p>
                                <div style="margin-top: 1rem;">
                                    <a href="api-setup.php" class="btn btn-secondary" style="text-decoration: none;">
                                        <i class="fas fa-external-link-alt"></i>
                                        API Setup Guide
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h4><i class="fas fa-brain"></i> AI Provider Configuration</h4>
                            <p>Select your preferred AI provider and model for question generation.</p>

                            <form method="POST">
                                <input type="hidden" name="action" value="update_ai">

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="ai_provider"><i class="fas fa-cloud"></i> AI Provider</label>
                                        <select id="ai_provider" name="ai_provider">
                                            <option value="openai" <?php echo $currentSettings['ai_provider'] === 'openai' ? 'selected' : ''; ?>>🤖 OpenAI (GPT Models)</option>
                                            <option value="anthropic" <?php echo $currentSettings['ai_provider'] === 'anthropic' ? 'selected' : ''; ?>>🧠 Anthropic (Claude)</option>
                                            <option value="google" <?php echo $currentSettings['ai_provider'] === 'google' ? 'selected' : ''; ?>>🔍 Google AI (Gemini)</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="ai_model"><i class="fas fa-microchip"></i> AI Model</label>
                                        <select id="ai_model" name="ai_model">
                                            <option value="gemini-1.5-flash-latest" <?php echo $currentSettings['ai_model'] === 'gemini-1.5-flash-latest' ? 'selected' : ''; ?>>🔍 Gemini 1.5 Flash (Fast & Efficient)</option>
                                            <option value="gemini-1.5-pro-latest" <?php echo $currentSettings['ai_model'] === 'gemini-1.5-pro-latest' ? 'selected' : ''; ?>>🔍 Gemini 1.5 Pro (Advanced)</option>
                                            <option value="gpt-3.5-turbo" <?php echo $currentSettings['ai_model'] === 'gpt-3.5-turbo' ? 'selected' : ''; ?>>🤖 GPT-3.5 Turbo</option>
                                            <option value="gpt-4" <?php echo $currentSettings['ai_model'] === 'gpt-4' ? 'selected' : ''; ?>>🤖 GPT-4</option>
                                            <option value="claude-3" <?php echo $currentSettings['ai_model'] === 'claude-3' ? 'selected' : ''; ?>>🧠 Claude 3</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="ai_api_key"><i class="fas fa-key"></i> API Key</label>
                                    <input type="password" id="ai_api_key" name="ai_api_key" placeholder="Enter new API key (leave blank to keep current)">
                                    <small style="color: #64748b; font-size: 0.875rem; margin-top: 0.5rem; display: block;"><i class="fas fa-shield-alt"></i> Your API key is encrypted and stored securely</small>
                                </div>
                            
                            <div class="form-group">
                                <label for="question_complexity">Question Complexity</label>
                                <select id="question_complexity" name="question_complexity">
                                    <option value="basic" <?php echo $currentSettings['question_complexity'] === 'basic' ? 'selected' : ''; ?>>Basic</option>
                                    <option value="medium" <?php echo $currentSettings['question_complexity'] === 'medium' ? 'selected' : ''; ?>>Medium</option>
                                    <option value="advanced" <?php echo $currentSettings['question_complexity'] === 'advanced' ? 'selected' : ''; ?>>Advanced</option>
                                    <option value="mixed" <?php echo $currentSettings['question_complexity'] === 'mixed' ? 'selected' : ''; ?>>Mixed</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save AI Settings
                                </button>
                                <a href="test-gemini.php" class="btn btn-outline" target="_blank">
                                    <i class="fas fa-flask"></i>
                                    Test Gemini API
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

            </div>
        </main>
    </div>
    
    <script src="../assets/js/admin-dashboard.js"></script>
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab button
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
