-- Initial Data for AI-Powered LMS
-- Ogbonnaya Onu Polytechnic Departments and Setup Data

USE lms_ogbonnaya_onu;

-- Insert Academic Levels
INSERT IGNORE INTO academic_levels (level_name, level_code, description) VALUES
('ND1', 'ND1', 'National Diploma Year 1'),
('ND2', 'ND2', 'National Diploma Year 2'),
('HND1', 'HND1', 'Higher National Diploma Year 1'),
('HND2', 'HND2', 'Higher National Diploma Year 2');

-- Insert Departments (31 courses)
INSERT IGNORE INTO departments (name, code, description) VALUES
('Computer Science', 'CS', 'Computer Science and Information Technology'),
('Civil Engineering', 'CE', 'Civil and Structural Engineering'),
('Mass Communication', 'MC', 'Mass Communication and Media Studies'),
('Electrical/Electronic Engineering', 'EEE', 'Electrical and Electronic Engineering'),
('Mechanical Engineering', 'ME', 'Mechanical Engineering Technology'),
('Accountancy', 'ACC', 'Accounting and Financial Management'),
('Business Administration', 'BA', 'Business Administration and Management'),
('Banking and Finance', 'BF', 'Banking and Financial Services'),
('Marketing', 'MKT', 'Marketing and Sales Management'),
('Public Administration', 'PA', 'Public Administration and Governance'),
('Office Technology and Management', 'OTM', 'Office Technology and Management'),
('Statistics', 'STAT', 'Statistics and Data Analysis'),
('Architecture', 'ARCH', 'Architectural Technology'),
('Building Technology', 'BT', 'Building Construction Technology'),
('Quantity Surveying', 'QS', 'Quantity Surveying and Cost Management'),
('Estate Management', 'EM', 'Estate Management and Valuation'),
('Urban and Regional Planning', 'URP', 'Urban and Regional Planning'),
('Surveying and Geo-informatics', 'SGI', 'Surveying and Geo-informatics'),
('Agricultural Technology', 'AT', 'Agricultural Technology and Management'),
('Food Technology', 'FT', 'Food Science and Technology'),
('Hospitality Management', 'HM', 'Hospitality and Tourism Management'),
('Fashion Design and Clothing Technology', 'FDCT', 'Fashion Design and Clothing Technology'),
('Fine and Applied Arts', 'FAA', 'Fine and Applied Arts'),
('Music Technology', 'MT', 'Music Technology and Production'),
('Library and Information Science', 'LIS', 'Library and Information Science'),
('Science Laboratory Technology', 'SLT', 'Science Laboratory Technology'),
('Computer Engineering', 'CPE', 'Computer Engineering Technology'),
('Pharmaceutical Technology', 'PT', 'Pharmaceutical Technology'),
('Environmental Technology', 'ET', 'Environmental Technology and Management'),
('Cooperative Economics and Management', 'CEM', 'Cooperative Economics and Management'),
('Insurance', 'INS', 'Insurance and Risk Management');

-- Insert Security Questions
INSERT IGNORE INTO security_questions (question) VALUES
('What is your mother\'s maiden name?'),
('What was the name of your first pet?'),
('In which city were you born?'),
('What is your favorite color?'),
('What was the name of your primary school?'),
('What is your favorite food?'),
('What was your childhood nickname?'),
('What is the name of your best friend?'),
('What was your first car model?'),
('What is your favorite movie?');

-- Insert Reward Types
INSERT IGNORE INTO reward_types (name, description, icon, points_required, badge_image) VALUES
('First Quiz', 'Completed your first quiz', '🎯', 0, 'badge-first-quiz.png'),
('Perfect Score', 'Achieved 100% in a quiz', '⭐', 0, 'badge-perfect.png'),
('Quick Learner', 'Completed quiz in under 5 minutes', '⚡', 0, 'badge-speed.png'),
('Consistent Learner', '5 days streak', '🔥', 0, 'badge-streak-5.png'),
('Dedicated Student', '10 days streak', '💎', 0, 'badge-streak-10.png'),
('Quiz Master', 'Completed 10 quizzes', '👑', 100, 'badge-master.png'),
('Subject Expert', 'Achieved 90%+ average in a subject', '🏆', 200, 'badge-expert.png'),
('Knowledge Seeker', 'Attempted 50 questions', '📚', 50, 'badge-seeker.png'),
('Rising Star', 'Earned 500 points', '🌟', 500, 'badge-star.png'),
('Academic Champion', 'Earned 1000 points', '🏅', 1000, 'badge-champion.png');

-- Insert System Settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('quiz_time_limit', '1800', 'Default quiz time limit in seconds (30 minutes)'),
('questions_per_quiz', '20', 'Default number of questions per quiz'),
('points_per_correct', '10', 'Points awarded for each correct answer'),
('points_bonus_perfect', '50', 'Bonus points for perfect score'),
('points_bonus_speed', '25', 'Bonus points for completing quiz quickly'),
('streak_bonus_multiplier', '1.5', 'Multiplier for streak bonuses'),
('min_pass_percentage', '60', 'Minimum percentage to pass a quiz'),
('max_daily_quizzes', '5', 'Maximum quizzes a student can take per day'),
('school_name', 'Ogbonnaya Onu Polytechnic, Aba', 'Official school name'),
('school_motto', 'Excellence in Technical Education', 'School motto'),
('academic_session', '2024/2025', 'Current academic session'),
('registration_open', '1', 'Whether student registration is open (1=yes, 0=no)');

-- Note: Subjects data removed - AI now generates questions directly for department + level combinations


