/* Landing Page Styles for AI-Powered LMS */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #1f2937;
    overflow-x: hidden;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a3e 25%, #2d1b69 50%, #3b2f7a 75%, #4c3d8b 100%);
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 20, 147, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 191, 255, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 60% 60%, rgba(50, 205, 50, 0.2) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: backgroundPulse 8s ease-in-out infinite alternate;
}

body::after {
    content: '\f11b';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: fixed;
    top: 85%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    font-size: 35vw;
    color: rgba(138, 43, 226, 0.04);
    pointer-events: none;
    z-index: -2;
    animation: gamepadFloat 20s ease-in-out infinite;
}

@keyframes backgroundPulse {
    0% { opacity: 0.8; }
    100% { opacity: 1; }
}

@keyframes gamepadFloat {
    0%, 100% {
        transform: translate(-50%, -50%) rotate(-15deg) scale(1);
        opacity: 0.04;
    }
    25% {
        transform: translate(-49%, -51%) rotate(-12deg) scale(1.02);
        opacity: 0.06;
    }
    50% {
        transform: translate(-51%, -49%) rotate(-18deg) scale(0.98);
        opacity: 0.03;
    }
    75% {
        transform: translate(-50%, -50%) rotate(-15deg) scale(1.01);
        opacity: 0.05;
    }
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg,
        rgba(15, 15, 35, 0.98) 0%,
        rgba(26, 26, 62, 0.95) 50%,
        rgba(45, 27, 105, 0.92) 100%) !important;
    backdrop-filter: blur(25px);
    border-bottom: 3px solid rgba(168, 85, 247, 0.5);
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow:
        0 8px 32px rgba(138, 43, 226, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Ensure navbar stays dark on scroll */
.navbar.scrolled,
.navbar:hover,
.navbar.active {
    background: linear-gradient(135deg,
        rgba(15, 15, 35, 0.98) 0%,
        rgba(26, 26, 62, 0.95) 50%,
        rgba(45, 27, 105, 0.92) 100%) !important;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nav-logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    object-fit: cover;
}

.brand-text h3 {
    font-size: 1.3rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 2px;
    text-shadow: 0 0 15px rgba(168, 85, 247, 0.8), 0 0 30px rgba(138, 43, 226, 0.4);
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 50%, #c7d2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-text p {
    font-size: 0.85rem;
    color: #c7d2fe;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(199, 210, 254, 0.5);
}

.nav-menu {
    display: flex;
    gap: 32px;
}

.nav-link {
    text-decoration: none;
    color: #c7d2fe;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 10px 18px;
    border-radius: 12px;
    text-shadow: 0 0 5px rgba(199, 210, 254, 0.3);
}

.nav-link:hover,
.nav-link.active {
    color: #ffffff;
    background: linear-gradient(135deg,
        rgba(168, 85, 247, 0.3) 0%,
        rgba(138, 43, 226, 0.2) 100%);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
    box-shadow: 0 4px 15px rgba(168, 85, 247, 0.3);
    transform: translateY(-1px);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(168, 85, 247, 0.6);
}

.nav-actions {
    display: flex;
    gap: 12px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 14px 24px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 700;
    font-size: 0.95rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg,
        #8b5cf6 0%,
        #a855f7 50%,
        #c084fc 100%);
    color: white;
    box-shadow:
        0 8px 25px rgba(139, 92, 246, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    border: 2px solid rgba(168, 85, 247, 0.3);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow:
        0 20px 50px rgba(139, 92, 246, 0.6),
        0 0 30px rgba(168, 85, 247, 0.4);
    border-color: rgba(168, 85, 247, 0.6);
}

.btn-outline {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(248, 250, 252, 0.1) 100%);
    color: #ffffff;
    border: 2px solid rgba(168, 85, 247, 0.4);
    backdrop-filter: blur(15px);
    box-shadow:
        0 4px 15px rgba(168, 85, 247, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
    background: linear-gradient(135deg,
        rgba(168, 85, 247, 0.2) 0%,
        rgba(139, 92, 246, 0.15) 100%);
    color: white;
    transform: translateY(-3px);
    box-shadow:
        0 12px 35px rgba(168, 85, 247, 0.4),
        0 0 20px rgba(255, 255, 255, 0.2);
    border-color: rgba(168, 85, 247, 0.6);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1rem;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #ffffff;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg,
        rgba(10, 10, 25, 0.98) 0%,
        rgba(20, 20, 45, 0.95) 30%,
        rgba(15, 15, 35, 0.97) 70%,
        rgba(10, 10, 25, 0.98) 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 100px; /* Reasonable padding to account for fixed navbar */
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(245, 158, 11, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

.hero::after {
    content: '';
    position: absolute;
    top: 10%;
    right: 10%;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, rgba(138, 43, 226, 0.1), rgba(168, 85, 247, 0.1));
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
    filter: blur(40px);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 2;
}



.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(251, 191, 36, 0.1) 100%);
    color: #f59e0b;
    padding: 12px 20px;
    border-radius: 50px;
    font-size: 0.95rem;
    font-weight: 700;
    margin-bottom: 32px;
    border: 2px solid rgba(245, 158, 11, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.hero-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(245, 158, 11, 0.3);
}

.hero-title {
    font-size: 4.2rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 32px;
    color: #ffffff;
    text-shadow:
        0 0 30px rgba(168, 85, 247, 0.8),
        0 0 60px rgba(138, 43, 226, 0.4),
        0 0 90px rgba(245, 158, 11, 0.2);
}

.gradient-text {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    filter: drop-shadow(0 0 10px rgba(168, 85, 247, 0.5));
}

.gradient-text::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    border-radius: 2px;
    opacity: 0.3;
}

.hero-description {
    font-size: 1.3rem;
    color: #e0e7ff;
    line-height: 1.7;
    margin-bottom: 32px;
    text-shadow: 0 0 15px rgba(224, 231, 255, 0.5);
    font-weight: 500;
}

/* Game Modes Section */
.game-modes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin: 40px 0;
}

.mode-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mode-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.mode-card:hover::before {
    left: 100%;
}

.mode-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.5);
}

.mode-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    color: white;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.mode-card h3 {
    color: #ffffff;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 12px;
}

.mode-card p {
    color: #a78bfa;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
}

.mode-features {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.mode-features span {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #cbd5e1;
    font-size: 0.85rem;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mode-features span i {
    color: #3b82f6;
    font-size: 0.8rem;
}

.hero-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 48px;
}

.hero-stats {
    display: flex;
    gap: 32px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-image {
    position: relative;
    width: 100%;
    max-width: 500px;
    height: 400px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.school-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
}
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.school-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 2;
}

.floating-card {
    position: absolute;
    background: white;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #1f2937;
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 1.2rem;
    color: #6366f1;
}

.card-1 {
    top: 20px;
    right: 20px;
    animation-delay: 0s;
}

.card-2 {
    bottom: 80px;
    left: 20px;
    animation-delay: 1s;
}

.card-3 {
    top: 50%;
    right: -20px;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Features Section */
.features {
    padding: 120px 0;
    background: linear-gradient(135deg,
        rgba(15, 15, 35, 0.98) 0%,
        rgba(26, 26, 62, 0.95) 30%,
        rgba(45, 27, 105, 0.97) 70%,
        rgba(15, 15, 35, 0.98) 100%);
    position: relative;
    overflow: hidden;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(138, 43, 226, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(168, 85, 247, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.2) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
    animation: backgroundPulse 10s ease-in-out infinite alternate;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.section-header h2 {
    font-size: 2.8rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 16px;
    text-shadow: 0 0 20px rgba(168, 85, 247, 0.8), 0 0 40px rgba(138, 43, 226, 0.4);
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 50%, #c7d2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: 1.2rem;
    color: #c7d2fe;
    max-width: 600px;
    margin: 0 auto;
    text-shadow: 0 0 10px rgba(199, 210, 254, 0.5);
    font-weight: 500;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 2;
}

.feature-card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(248, 250, 252, 0.12) 50%,
        rgba(199, 210, 254, 0.08) 100%);
    padding: 2.5rem;
    border-radius: 24px;
    border: 2px solid rgba(168, 85, 247, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(138, 43, 226, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(138, 43, 226, 0.1),
        rgba(168, 85, 247, 0.1),
        rgba(245, 158, 11, 0.1));
    border-radius: 24px;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow:
        0 30px 80px rgba(138, 43, 226, 0.3),
        0 0 0 2px rgba(168, 85, 247, 0.4) inset;
    border-color: rgba(168, 85, 247, 0.6);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg,
        rgba(138, 43, 226, 0.2) 0%,
        rgba(168, 85, 247, 0.3) 50%,
        rgba(245, 158, 11, 0.2) 100%);
    border-radius: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 10px 30px rgba(138, 43, 226, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    position: relative;
    overflow: hidden;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: transform 0.6s ease;
}

.feature-card:hover .feature-icon::before {
    transform: rotate(45deg) translate(100%, 100%);
}

.feature-icon i {
    font-size: 2.8rem;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    z-index: 1;
}

.feature-card:hover .feature-icon {
    transform: scale(1.15) rotate(5deg);
    box-shadow:
        0 15px 40px rgba(138, 43, 226, 0.4),
        0 0 20px rgba(168, 85, 247, 0.3);
}

.feature-card h3 {
    font-size: 1.4rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 16px;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.feature-card p {
    color: #c7d2fe;
    line-height: 1.7;
    font-weight: 500;
    text-shadow: 0 0 5px rgba(199, 210, 254, 0.3);
}

/* About Section */
.about {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 16px;
}

.about-subtitle {
    font-size: 1.2rem;
    color: #6366f1;
    font-weight: 600;
    margin-bottom: 24px;
}

.about-text p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 32px;
}

.about-features {
    margin-bottom: 32px;
}

.about-feature {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.about-feature i {
    color: #10b981;
    font-size: 1.1rem;
}

.about-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.campus-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 40px 32px 32px;
    color: white;
}

.overlay-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.overlay-content p {
    color: #e5e7eb;
}

/* Statistics Section */
.statistics {
    padding: 120px 0;
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.6) 0%, rgba(224, 242, 254, 0.4) 100%);
    position: relative;
    overflow: hidden;
}

.statistics::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 20% 80%, rgba(147, 197, 253, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 1;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    padding: 3rem 2rem;
    border-radius: 24px;
    text-align: center;
    border: 2px solid rgba(59, 130, 246, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);
    border-radius: 24px 24px 0 0;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 50px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.2);
}

.stat-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 32px;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
}

.stat-icon i {
    font-size: 2.2rem;
    color: white;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
}

.stat-content h3 {
    font-size: 3rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 8px;
}

.stat-content p {
    color: #6b7280;
    font-weight: 600;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.footer-logo {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    object-fit: cover;
}

.footer-brand h4 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.footer-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #f9fafb;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 12px;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #6366f1;
}

.social-links {
    display: flex;
    gap: 16px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: #374151;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #d1d5db;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #6366f1;
    color: white;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 20px;
    text-align: center;
    color: #9ca3af;
}

.footer-bottom p {
    margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu,
    .nav-actions {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .game-modes {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .mode-card {
        padding: 20px;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .hero-actions {
        gap: 12px;
    }
    
    .btn-large {
        padding: 14px 24px;
        font-size: 0.9rem;
    }
}

/* ===== GAMIFIED STYLES ===== */

/* Game Button Styles */
.game-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
    border: 2px solid #a855f7;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
    transition: all 0.3s ease;
}

.game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.6);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.game-btn:hover .btn-glow {
    left: 100%;
}

/* Game Stats */
.game-stat {
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.8) 0%, rgba(26, 26, 62, 0.6) 100%);
    border: 2px solid rgba(138, 43, 226, 0.3);
    border-radius: 16px;
    padding: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.game-stat:hover {
    transform: translateY(-5px);
    border-color: rgba(168, 85, 247, 0.6);
    box-shadow: 0 10px 30px rgba(138, 43, 226, 0.3);
}

.game-stat .stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
}

.game-stat .stat-number {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 800;
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
}

.game-stat .stat-label {
    color: #a78bfa;
    font-size: 1rem;
    font-weight: 600;
}

/* Game World */
.game-world {
    position: relative;
    width: 100%;
    height: 500px;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 62, 0.7) 100%);
    border-radius: 20px;
    border: 2px solid rgba(138, 43, 226, 0.3);
    overflow: hidden;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.9) 0%, rgba(168, 85, 247, 0.8) 100%);
    border: 2px solid rgba(168, 85, 247, 0.6);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 8px 25px rgba(138, 43, 226, 0.4);
    animation: float 3s ease-in-out infinite;
}

.achievement-card {
    top: 10%;
    left: -20%;
    animation-delay: 0s;
}

.xp-card {
    top: 15%;
    right: -25%;
    animation-delay: 1s;
}

.level-card {
    bottom: 25%;
    left: -15%;
    animation-delay: 2s;
}

.streak-card {
    bottom: 10%;
    right: -20%;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.card-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #8b5cf6, #a855f7, #c084fc, #8b5cf6);
    border-radius: 12px;
    z-index: -1;
    animation: rotate 4s linear infinite;
    opacity: 0.7;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.game-character {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.character-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #10b981, #34d399);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 10px;
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.character-level {
    background: rgba(15, 15, 35, 0.9);
    border: 2px solid #10b981;
    border-radius: 20px;
    padding: 5px 15px;
    color: #34d399;
    font-weight: 700;
    font-size: 0.9rem;
}

/* Game Feature Cards */
.game-feature {
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 62, 0.8) 100%);
    border: 2px solid rgba(138, 43, 226, 0.3);
    border-radius: 20px;
    padding: 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.game-feature:hover {
    transform: translateY(-10px);
    border-color: rgba(168, 85, 247, 0.6);
    box-shadow: 0 20px 40px rgba(138, 43, 226, 0.3);
}

.game-feature .feature-icon {
    position: relative;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.5);
}

.icon-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, #8b5cf6, #a855f7, #c084fc, #8b5cf6);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 6s linear infinite;
    opacity: 0.6;
}

.game-feature h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
}

.game-feature p {
    color: #a78bfa;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.feature-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #10b981, #34d399);
    border-radius: 20px;
    padding: 5px 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.4);
}

/* Game Statistics Cards */
.game-stat-card {
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 62, 0.8) 100%);
    border: 2px solid rgba(138, 43, 226, 0.3);
    border-radius: 20px;
    padding: 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.game-stat-card:hover {
    transform: translateY(-8px);
    border-color: rgba(168, 85, 247, 0.6);
    box-shadow: 0 15px 35px rgba(138, 43, 226, 0.4);
}

.game-stat-card .stat-icon {
    position: relative;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.8rem;
    color: white;
    box-shadow: 0 0 25px rgba(168, 85, 247, 0.5);
}

.stat-glow {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #8b5cf6, #a855f7, #c084fc, #8b5cf6);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 8s linear infinite;
    opacity: 0.5;
}

.game-stat-card .stat-number {
    color: #ffffff;
    font-size: 2.8rem;
    font-weight: 800;
    text-shadow: 0 0 15px rgba(168, 85, 247, 0.6);
    margin-bottom: 10px;
}

.game-stat-card .stat-content p {
    color: #a78bfa;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.stat-badge {
    background: linear-gradient(135deg, #f59e0b, #f97316);
    border-radius: 15px;
    padding: 4px 10px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    box-shadow: 0 0 12px rgba(245, 158, 11, 0.4);
}



/* Game Feature Items */
.game-feature-item {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
    border: 1px solid rgba(168, 85, 247, 0.2);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.game-feature-item:hover {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.2) 0%, rgba(168, 85, 247, 0.1) 100%);
    border-color: rgba(168, 85, 247, 0.4);
    transform: translateX(10px);
}

.game-feature-item i {
    color: #a855f7;
    margin-right: 12px;
    font-size: 1.2rem;
}

.game-feature-item span {
    color: #a78bfa;
    font-weight: 500;
}

/* Enhanced Gaming Hero Styles */
@keyframes particleFloat {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-20px) translateX(10px); }
    66% { transform: translateY(10px) translateX(-5px); }
    100% { transform: translateY(0px) translateX(0px); }
}

@keyframes gridPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.hero-badge .badge-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #8b5cf6, #a855f7, #f59e0b, #10b981);
    border-radius: 50px;
    z-index: -1;
    animation: rotate 3s linear infinite;
    opacity: 0.7;
}

.title-main {
    display: block;
    font-size: 4.5rem;
    font-weight: 900;
    color: #ffffff;
    text-shadow: 0 0 30px rgba(168, 85, 247, 0.8);
}

.title-sub {
    display: block;
    font-size: 3rem;
    font-weight: 700;
    margin-top: 10px;
}

.title-underline {
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #8b5cf6, #a855f7, #f59e0b);
    margin: 20px 0;
    border-radius: 2px;
    animation: pulse 2s ease-in-out infinite;
}

.hero-features {
    display: flex;
    gap: 15px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.feature-pill {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(168, 85, 247, 0.1));
    border: 1px solid rgba(168, 85, 247, 0.3);
    padding: 8px 16px;
    border-radius: 25px;
    color: #a855f7;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.feature-pill:hover {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.3), rgba(168, 85, 247, 0.2));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
}

.hero-cta {
    position: relative;
    overflow: hidden;
}

.btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(245, 158, 11, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.3) 0%, transparent 50%);
    animation: particleMove 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes particleMove {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

.stat-card {
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.8), rgba(26, 26, 62, 0.6));
    border: 2px solid rgba(138, 43, 226, 0.3);
    border-radius: 20px;
    padding: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(168, 85, 247, 0.5);
}

.stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
}

.stat-info .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
}

.stat-info .stat-label {
    display: block;
    font-size: 0.9rem;
    color: #a855f7;
    font-weight: 600;
    margin-top: 5px;
}

.stat-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(168, 85, 247, 0.3), transparent);
    border-radius: 20px;
    z-index: -1;
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-glow {
    opacity: 1;
}

/* Hero Image */
.hero-image {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.school-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 50px rgba(138, 43, 226, 0.2);
    transition: transform 0.3s ease;
}

.school-image:hover {
    transform: scale(1.02);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 62, 0.9));
    border: 2px solid rgba(138, 43, 226, 0.5);
    border-radius: 15px;
    padding: 12px 16px;
    color: white;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    animation: floatCard 4s ease-in-out infinite;
    backdrop-filter: blur(10px);
}

.achievement-card {
    top: 10%;
    right: -10%;
    color: #f59e0b;
    border-color: rgba(245, 158, 11, 0.5);
    animation-delay: 0s;
}

.xp-card {
    top: 30%;
    left: -15%;
    color: #8b5cf6;
    border-color: rgba(139, 92, 246, 0.5);
    animation-delay: 1s;
}

.level-card {
    bottom: 30%;
    right: -5%;
    color: #10b981;
    border-color: rgba(16, 185, 129, 0.5);
    animation-delay: 2s;
}

.streak-card {
    bottom: 10%;
    left: -10%;
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.5);
    animation-delay: 3s;
}

@keyframes floatCard {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-15px) rotate(2deg);
        opacity: 1;
    }
}

.card-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 15px;
    z-index: -1;
    animation: cardGlow 3s ease-in-out infinite;
}

.achievement-card .card-glow {
    background: linear-gradient(45deg, transparent, rgba(245, 158, 11, 0.3), transparent);
}

.xp-card .card-glow {
    background: linear-gradient(45deg, transparent, rgba(139, 92, 246, 0.3), transparent);
}

.level-card .card-glow {
    background: linear-gradient(45deg, transparent, rgba(16, 185, 129, 0.3), transparent);
}

.streak-card .card-glow {
    background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.3), transparent);
}

@keyframes cardGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Enhanced Features Section */
.features-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.features-grid-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(138, 43, 226, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(138, 43, 226, 0.05) 1px, transparent 1px);
    background-size: 100px 100px;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(100px, 100px); }
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(251, 191, 36, 0.1));
    border: 2px solid rgba(245, 158, 11, 0.3);
    color: #f59e0b;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 700;
    margin-bottom: 20px;
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
}

.section-divider {
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #f59e0b, #fbbf24, #f59e0b);
    margin: 20px auto;
    border-radius: 2px;
    animation: pulse 2s ease-in-out infinite;
}

.premium-feature {
    border: 2px solid rgba(245, 158, 11, 0.5);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.05));
}

.epic-feature {
    border: 2px solid rgba(168, 85, 247, 0.5);
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(196, 181, 253, 0.05));
}

.rare-feature {
    border: 2px solid rgba(34, 197, 94, 0.5);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(110, 231, 183, 0.05));
}

.legendary-feature {
    border: 2px solid rgba(239, 68, 68, 0.5);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(252, 165, 165, 0.05));
}

.feature-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.feature-rank {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(15, 15, 35, 0.9));
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 800;
    letter-spacing: 1px;
}

.premium-feature .feature-rank {
    color: #f59e0b;
    border-color: rgba(245, 158, 11, 0.3);
}

.epic-feature .feature-rank {
    color: #a855f7;
    border-color: rgba(168, 85, 247, 0.3);
}

.rare-feature .feature-rank {
    color: #10b981;
    border-color: rgba(16, 185, 129, 0.3);
}

.legendary-feature .feature-rank {
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.3);
}

.icon-pulse {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    animation: iconPulse 2s ease-in-out infinite;
    z-index: -1;
}

.premium-feature .icon-pulse {
    background: linear-gradient(45deg, transparent, rgba(245, 158, 11, 0.3), transparent);
}

.epic-feature .icon-pulse {
    background: linear-gradient(45deg, transparent, rgba(168, 85, 247, 0.3), transparent);
}

.rare-feature .icon-pulse {
    background: linear-gradient(45deg, transparent, rgba(16, 185, 129, 0.3), transparent);
}

.legendary-feature .icon-pulse {
    background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.3), transparent);
}

@keyframes iconPulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.feature-stats {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

.feature-stats .stat {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg,
        rgba(138, 43, 226, 0.2) 0%,
        rgba(168, 85, 247, 0.15) 100%);
    border: 1px solid rgba(168, 85, 247, 0.4);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    color: #ffffff;
    font-weight: 600;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
    transition: all 0.3s ease;
}

.feature-stats .stat:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(138, 43, 226, 0.3);
    border-color: rgba(168, 85, 247, 0.6);
}

.feature-stats .stat i {
    color: #fbbf24;
    text-shadow: 0 0 5px rgba(251, 191, 36, 0.5);
}

.feature-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: rotate 6s linear infinite;
}

.premium-feature .feature-glow {
    background: linear-gradient(45deg, transparent, rgba(245, 158, 11, 0.3), transparent, rgba(245, 158, 11, 0.3), transparent);
}

.epic-feature .feature-glow {
    background: linear-gradient(45deg, transparent, rgba(168, 85, 247, 0.3), transparent, rgba(168, 85, 247, 0.3), transparent);
}

.rare-feature .feature-glow {
    background: linear-gradient(45deg, transparent, rgba(16, 185, 129, 0.3), transparent, rgba(16, 185, 129, 0.3), transparent);
}

.legendary-feature .feature-glow {
    background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.3), transparent, rgba(239, 68, 68, 0.3), transparent);
}

.feature-card:hover .feature-glow {
    opacity: 1;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Additional Gaming Background Elements */
.hero::before {
    content: '\f11b';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 10%;
    right: 5%;
    font-size: 15vw;
    color: rgba(168, 85, 247, 0.02);
    pointer-events: none;
    z-index: -1;
    animation: gamepadFloat 25s ease-in-out infinite reverse;
}

.features::before {
    content: '\f11b';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    bottom: 10%;
    left: 5%;
    font-size: 12vw;
    color: rgba(138, 43, 226, 0.02);
    pointer-events: none;
    z-index: -1;
    animation: gamepadFloat 30s ease-in-out infinite;
    transform: rotate(25deg);
}

.statistics::before {
    content: '\f11b';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 15%;
    right: 10%;
    font-size: 10vw;
    color: rgba(255, 20, 147, 0.02);
    pointer-events: none;
    z-index: -1;
    animation: gamepadFloat 35s ease-in-out infinite reverse;
    transform: rotate(-30deg);
}

/* Make sections relative for positioning */
.hero, .features, .statistics {
    position: relative;
    overflow: hidden;
}
