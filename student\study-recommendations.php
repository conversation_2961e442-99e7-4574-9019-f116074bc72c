<?php
/**
 * AI-Powered Study Recommendations System
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

$studentId = $_SESSION['user_id'];

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $studentId]);

/**
 * Generate AI-powered study recommendations
 */
function generateStudyRecommendations($studentId) {
    $recommendations = [];
    
    // Get student performance data
    $performance = fetchAll("
        SELECT d.name as subject,
               COUNT(*) as quiz_count,
               AVG(qs.score_percentage) as avg_score,
               MAX(qs.score_percentage) as best_score,
               MIN(qs.score_percentage) as worst_score,
               STDDEV(qs.score_percentage) as consistency,
               MAX(qs.completed_at) as last_attempt,
               qs.difficulty
        FROM quiz_sessions qs
        JOIN departments d ON qs.department_id = d.id
        WHERE qs.student_id = :id AND qs.status = 'completed'
        GROUP BY d.id, d.name, qs.difficulty
        ORDER BY avg_score ASC, last_attempt DESC
    ", ['id' => $studentId]);
    
    // Get recent activity patterns
    $recentActivity = fetchAll("
        SELECT DATE(completed_at) as date,
               HOUR(completed_at) as hour,
               AVG(score_percentage) as avg_score,
               COUNT(*) as quiz_count
        FROM quiz_sessions 
        WHERE student_id = :id AND status = 'completed'
        AND completed_at >= DATE_SUB(NOW(), INTERVAL 14 DAY)
        GROUP BY DATE(completed_at), HOUR(completed_at)
        ORDER BY completed_at DESC
    ", ['id' => $studentId]);
    
    // Recommendation 1: Focus on weak subjects
    $weakSubjects = array_filter($performance, function($p) {
        return $p['avg_score'] < 70 && $p['quiz_count'] >= 2;
    });
    
    if (!empty($weakSubjects)) {
        $weakest = array_shift($weakSubjects);
        $recommendations[] = [
            'type' => 'improvement',
            'priority' => 'high',
            'title' => 'Focus on ' . $weakest['subject'],
            'description' => "Your average score in {$weakest['subject']} is " . number_format($weakest['avg_score'], 1) . "%. Consider taking more practice quizzes in this subject.",
            'action' => 'Take Quiz',
            'action_url' => "interactive-quiz.php?subject=" . urlencode($weakest['subject']),
            'icon' => 'fas fa-target',
            'color' => 'danger'
        ];
    }
    
    // Recommendation 2: Maintain strong subjects
    $strongSubjects = array_filter($performance, function($p) {
        return $p['avg_score'] >= 85 && $p['quiz_count'] >= 3;
    });
    
    if (!empty($strongSubjects)) {
        $strongest = array_reduce($strongSubjects, function($carry, $item) {
            return (!$carry || $item['avg_score'] > $carry['avg_score']) ? $item : $carry;
        });
        
        $daysSinceLastAttempt = (strtotime('now') - strtotime($strongest['last_attempt'])) / (60 * 60 * 24);
        
        if ($daysSinceLastAttempt > 7) {
            $recommendations[] = [
                'type' => 'maintenance',
                'priority' => 'medium',
                'title' => 'Maintain Your Strength in ' . $strongest['subject'],
                'description' => "You excel in {$strongest['subject']} with " . number_format($strongest['avg_score'], 1) . "% average. Take a refresher quiz to maintain your edge.",
                'action' => 'Refresher Quiz',
                'action_url' => "interactive-quiz.php?subject=" . urlencode($strongest['subject']),
                'icon' => 'fas fa-star',
                'color' => 'success'
            ];
        }
    }
    
    // Recommendation 3: Optimal study time
    if (!empty($recentActivity)) {
        $hourlyPerformance = [];
        foreach ($recentActivity as $activity) {
            $hour = $activity['hour'];
            if (!isset($hourlyPerformance[$hour])) {
                $hourlyPerformance[$hour] = ['total_score' => 0, 'count' => 0];
            }
            $hourlyPerformance[$hour]['total_score'] += $activity['avg_score'];
            $hourlyPerformance[$hour]['count']++;
        }
        
        $bestHour = null;
        $bestScore = 0;
        foreach ($hourlyPerformance as $hour => $data) {
            $avgScore = $data['total_score'] / $data['count'];
            if ($avgScore > $bestScore && $data['count'] >= 2) {
                $bestScore = $avgScore;
                $bestHour = $hour;
            }
        }
        
        if ($bestHour !== null) {
            $timeStr = date('g A', mktime($bestHour, 0));
            $recommendations[] = [
                'type' => 'timing',
                'priority' => 'low',
                'title' => 'Optimal Study Time: ' . $timeStr,
                'description' => "Your performance peaks around {$timeStr} with an average score of " . number_format($bestScore, 1) . "%. Consider scheduling study sessions during this time.",
                'action' => 'Set Reminder',
                'action_url' => '#',
                'icon' => 'fas fa-clock',
                'color' => 'info'
            ];
        }
    }
    
    // Recommendation 4: Difficulty progression
    $easyQuizzes = array_filter($performance, function($p) { return $p['difficulty'] === 'easy'; });
    $mediumQuizzes = array_filter($performance, function($p) { return $p['difficulty'] === 'medium'; });
    $hardQuizzes = array_filter($performance, function($p) { return $p['difficulty'] === 'hard'; });
    
    if (!empty($easyQuizzes)) {
        $easyAvg = array_sum(array_column($easyQuizzes, 'avg_score')) / count($easyQuizzes);
        
        if ($easyAvg >= 80 && empty($mediumQuizzes)) {
            $recommendations[] = [
                'type' => 'progression',
                'priority' => 'medium',
                'title' => 'Ready for Medium Difficulty',
                'description' => "You're scoring well on easy quizzes (avg: " . number_format($easyAvg, 1) . "%). Time to challenge yourself with medium difficulty questions!",
                'action' => 'Try Medium Quiz',
                'action_url' => 'interactive-quiz.php?difficulty=medium',
                'icon' => 'fas fa-level-up-alt',
                'color' => 'warning'
            ];
        } elseif (!empty($mediumQuizzes)) {
            $mediumAvg = array_sum(array_column($mediumQuizzes, 'avg_score')) / count($mediumQuizzes);
            if ($mediumAvg >= 75 && empty($hardQuizzes)) {
                $recommendations[] = [
                    'type' => 'progression',
                    'priority' => 'medium',
                    'title' => 'Ready for Hard Difficulty',
                    'description' => "Excellent progress on medium quizzes (avg: " . number_format($mediumAvg, 1) . "%). Challenge yourself with hard difficulty questions!",
                    'action' => 'Try Hard Quiz',
                    'action_url' => 'quick-game.php?difficulty=hard',
                    'icon' => 'fas fa-trophy',
                    'color' => 'primary'
                ];
            }
        }
    }
    
    // Recommendation 5: Consistency improvement
    $inconsistentSubjects = array_filter($performance, function($p) {
        return $p['consistency'] > 20 && $p['quiz_count'] >= 3;
    });
    
    if (!empty($inconsistentSubjects)) {
        $mostInconsistent = array_reduce($inconsistentSubjects, function($carry, $item) {
            return (!$carry || $item['consistency'] > $carry['consistency']) ? $item : $carry;
        });
        
        $recommendations[] = [
            'type' => 'consistency',
            'priority' => 'medium',
            'title' => 'Improve Consistency in ' . $mostInconsistent['subject'],
            'description' => "Your scores in {$mostInconsistent['subject']} vary significantly. Focus on understanding core concepts to achieve more consistent results.",
            'action' => 'Practice More',
            'action_url' => "interactive-quiz.php?subject=" . urlencode($mostInconsistent['subject']),
            'icon' => 'fas fa-chart-line',
            'color' => 'secondary'
        ];
    }
    
    // Sort recommendations by priority
    $priorityOrder = ['high' => 1, 'medium' => 2, 'low' => 3];
    usort($recommendations, function($a, $b) use ($priorityOrder) {
        return $priorityOrder[$a['priority']] - $priorityOrder[$b['priority']];
    });
    
    return array_slice($recommendations, 0, 6); // Return top 6 recommendations
}

$recommendations = generateStudyRecommendations($studentId);
$pageTitle = "Study Recommendations";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Student-specific styling */
        .sidebar-header h3 { color: #28a745; }
        .nav-item.active { background: linear-gradient(135deg, #28a745, #20c997); }
        .btn-primary { background: linear-gradient(135deg, #28a745, #20c997); }
        
        .recommendations-container {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .recommendation-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #28a745;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .recommendation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .recommendation-card.high-priority { border-left-color: #dc3545; }
        .recommendation-card.medium-priority { border-left-color: #ffc107; }
        .recommendation-card.low-priority { border-left-color: #17a2b8; }
        
        .recommendation-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .recommendation-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            flex-shrink: 0;
        }
        
        .recommendation-icon.danger { background: #dc3545; }
        .recommendation-icon.success { background: #28a745; }
        .recommendation-icon.warning { background: #ffc107; }
        .recommendation-icon.info { background: #17a2b8; }
        .recommendation-icon.primary { background: #007bff; }
        .recommendation-icon.secondary { background: #6c757d; }
        
        .recommendation-content {
            flex: 1;
        }
        
        .recommendation-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .recommendation-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .recommendation-action {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s ease;
        }
        
        .recommendation-action:hover {
            background: #218838;
            color: white;
        }
        
        .priority-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .priority-high { background: #dc3545; color: white; }
        .priority-medium { background: #ffc107; color: #212529; }
        .priority-low { background: #17a2b8; color: white; }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dee2e6;
        }
        
        .ai-badge {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Student Portal</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="quick-game.php" class="nav-item">
                    <i class="fas fa-bolt"></i>
                    <span>Quick Game</span>
                </a>
                <a href="endless-game.php" class="nav-item">
                    <i class="fas fa-infinity"></i>
                    <span>Endless Mode</span>
                </a>
                <a href="mission-mode.php" class="nav-item">
                    <i class="fas fa-map"></i>
                    <span>Mission Mode</span>
                </a>
                <a href="results.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>My Results</span>
                </a>
                <a href="analytics.php" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="study-recommendations.php" class="nav-item active">
                    <i class="fas fa-lightbulb"></i>
                    <span>Study Tips</span>
                </a>
                <a href="notifications.php" class="nav-item">
                    <i class="fas fa-bell"></i>
                    <span>Notifications</span>
                </a>
                <a href="profile.php" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
                <a href="../index.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Go Back to Site</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1><i class="fas fa-lightbulb"></i> AI Study Recommendations</h1>
                    <p>Personalized study suggestions based on your learning patterns</p>
                </div>
            </header>

            <div class="ai-badge">
                <i class="fas fa-robot"></i>
                AI-Powered Recommendations
            </div>

            <div class="recommendations-container">
                <?php if (empty($recommendations)): ?>
                    <div class="empty-state">
                        <i class="fas fa-graduation-cap"></i>
                        <h3>Keep Learning!</h3>
                        <p>Take more quizzes to get personalized study recommendations.</p>
                        <a href="interactive-quiz.php" class="recommendation-action">
                            <i class="fas fa-brain"></i>
                            Start Learning
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($recommendations as $rec): ?>
                        <div class="recommendation-card <?php echo $rec['priority']; ?>-priority" style="position: relative;">
                            <span class="priority-badge priority-<?php echo $rec['priority']; ?>">
                                <?php echo ucfirst($rec['priority']); ?> Priority
                            </span>
                            
                            <div class="recommendation-header">
                                <div class="recommendation-icon <?php echo $rec['color']; ?>">
                                    <i class="<?php echo $rec['icon']; ?>"></i>
                                </div>
                                <div class="recommendation-content">
                                    <h3 class="recommendation-title"><?php echo htmlspecialchars($rec['title']); ?></h3>
                                    <p class="recommendation-description"><?php echo htmlspecialchars($rec['description']); ?></p>
                                    <a href="<?php echo htmlspecialchars($rec['action_url']); ?>" class="recommendation-action">
                                        <i class="fas fa-arrow-right"></i>
                                        <?php echo htmlspecialchars($rec['action']); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
