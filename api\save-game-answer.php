<?php
/**
 * Save Game Answer API for AI-Powered LMS
 * Saves student answers during game sessions
 */

require_once '../config/database.php';

// Set JSON header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit();
}

// Validate required fields
$required_fields = ['session_id', 'question_id', 'is_correct', 'points', 'time_taken'];
foreach ($required_fields as $field) {
    if (!isset($input[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
        exit();
    }
}

$sessionId = (int)$input['session_id'];
$questionId = (int)$input['question_id'];
$answer = $input['answer'] ?? null;
$isCorrect = (bool)$input['is_correct'];
$points = (int)$input['points'];
$timeTaken = (int)$input['time_taken'];

try {
    // Verify the question belongs to this session
    $question = fetchOne("
        SELECT id FROM game_questions
        WHERE id = ? AND session_id = ?
    ", [$questionId, $sessionId]);
    
    if (!$question) {
        throw new Exception('Invalid question or session');
    }
    
    // Update the question with student's answer
    $updated = updateRecord('game_questions', [
        'student_answer' => $answer,
        'is_correct' => $isCorrect,
        'points_awarded' => $points,
        'time_taken' => $timeTaken,
        'answered_at' => date('Y-m-d H:i:s')
    ], 'id = ?', [$questionId]);
    
    if (!$updated) {
        throw new Exception('Failed to save answer');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Answer saved successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Save game answer error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to save answer: ' . $e->getMessage()
    ]);
}
?>
