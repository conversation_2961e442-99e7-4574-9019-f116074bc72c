<?php
/**
 * Gemini AI API Integration for LMS
 * Ogbonnaya Onu Polytechnic, Aba
 *
 * This file handles all Gemini AI API interactions for question generation
 */

require_once 'question-deduplication.php';

// Gemini API Configuration
define('GEMINI_API_KEY', 'AIzaSyBdgNuHjMkjDTrGjByhUoXgDjZh2eF0Ft4');
define('GEMINI_API_URL', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent');

/**
 * Generate AI questions using Gemini API with deduplication
 *
 * @param array $department Department information
 * @param array $academicLevel Academic level information
 * @param int $numQuestions Number of questions to generate
 * @param string $customPrompt Optional custom prompt additions
 * @param int $studentId Student ID for deduplication (optional)
 * @param string $difficulty Difficulty level for deduplication
 * @return array Generated questions or empty array on failure
 */
function generateQuestionsWithGemini($department, $academicLevel, $numQuestions = 10, $customPrompt = '', $studentId = null, $difficulty = 'easy') {
    try {
        error_log("Gemini API: Starting question generation for {$department['name']} - {$academicLevel['level_name']} (difficulty: $difficulty, count: $numQuestions)");

        // First check if we can make network requests
        if (!isNetworkAvailable()) {
            error_log("Gemini API: Network not available, skipping API call");
            return [];
        }

        // Get previously asked questions to avoid repetition
        $previousQuestions = [];
        if ($studentId) {
            $previousQuestions = getPreviousQuestions(
                $studentId,
                $department['id'],
                $academicLevel['id'],
                $difficulty,
                50 // Get last 50 questions for context
            );
        }

        // Create context about previous questions
        $avoidContext = '';
        if (!empty($previousQuestions)) {
            $questionTexts = array_map(function($q) { return $q['question_text']; }, $previousQuestions);
            $avoidContext = "\n\nIMPORTANT: Do NOT generate questions similar to these previously asked questions:\n" .
                           implode("\n- ", $questionTexts) . "\n\nMake sure all new questions are completely different in topic and content.";
        }

        // Create a simple, direct prompt with deduplication
        $prompt = "Generate exactly {$numQuestions} multiple choice questions about {$department['name']} for {$academicLevel['level_name']} students at {$difficulty} difficulty level.

For each question, provide:
- A clear question
- Four options (A, B, C, D)
- The correct answer

Format as JSON array:
[
  {
    \"question\": \"Question text here\",
    \"option_a\": \"Option A text\",
    \"option_b\": \"Option B text\",
    \"option_c\": \"Option C text\",
    \"option_d\": \"Option D text\",
    \"correct_answer\": \"A\"
  }
]

Make sure questions are relevant to {$department['name']} and appropriate for {$academicLevel['level_name']} level.{$avoidContext}";

        error_log("Gemini API: Sending prompt to API...");
        $response = callGeminiAPI($prompt);

        if ($response && isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            $content = $response['candidates'][0]['content']['parts'][0]['text'];
            error_log("Gemini API: Received response, parsing...");
            error_log("Gemini API Response Content: " . substr($content, 0, 500) . "...");

            // Try to extract JSON from the response
            $questions = parseSimpleGeminiResponse($content);

            if (!empty($questions) && is_array($questions)) {
                error_log("Gemini API: Successfully generated " . count($questions) . " questions");
                return array_slice($questions, 0, $numQuestions);
            } else {
                error_log("Gemini API: Failed to parse questions from response");
            }
        } else {
            error_log("Gemini API: Invalid response structure");
            if ($response) {
                error_log("Gemini API Response: " . json_encode($response));
            }
        }

        error_log("Gemini API: Failed to generate valid questions");
        return [];

    } catch (Exception $e) {
        error_log("Gemini API Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Simple parser for Gemini response
 */
function parseSimpleGeminiResponse($content) {
    // Try to find JSON in the response
    if (preg_match('/\[.*\]/s', $content, $matches)) {
        $jsonStr = $matches[0];
        $questions = json_decode($jsonStr, true);

        if ($questions && is_array($questions)) {
            // Validate basic structure
            $validQuestions = [];
            foreach ($questions as $q) {
                if (isset($q['question']) && isset($q['option_a']) && isset($q['option_b']) &&
                    isset($q['option_c']) && isset($q['option_d']) && isset($q['correct_answer'])) {
                    $validQuestions[] = $q;
                }
            }
            return $validQuestions;
        }
    }

    // If JSON parsing fails, try to parse text format
    $lines = explode("\n", $content);
    $questions = [];
    $currentQuestion = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        if (preg_match('/^Question:?\s*(.+)$/i', $line, $matches)) {
            if (!empty($currentQuestion)) {
                $questions[] = $currentQuestion;
            }
            $currentQuestion = ['question' => trim($matches[1])];
        } elseif (preg_match('/^A\)\s*(.+)$/i', $line, $matches)) {
            $currentQuestion['option_a'] = trim($matches[1]);
        } elseif (preg_match('/^B\)\s*(.+)$/i', $line, $matches)) {
            $currentQuestion['option_b'] = trim($matches[1]);
        } elseif (preg_match('/^C\)\s*(.+)$/i', $line, $matches)) {
            $currentQuestion['option_c'] = trim($matches[1]);
        } elseif (preg_match('/^D\)\s*(.+)$/i', $line, $matches)) {
            $currentQuestion['option_d'] = trim($matches[1]);
        } elseif (preg_match('/^Correct Answer:?\s*([ABCD])/i', $line, $matches)) {
            $currentQuestion['correct_answer'] = strtoupper($matches[1]);
        }
    }

    if (!empty($currentQuestion)) {
        $questions[] = $currentQuestion;
    }

    return $questions;
}

/**
 * Build comprehensive prompt for Gemini API
 */
function buildGeminiPrompt($department, $academicLevel, $numQuestions, $customPrompt, $uniqueId, $timestamp, $previousQuestions = []) {
    $departmentName = $department['name'] ?? 'General Studies';
    $departmentCode = $department['code'] ?? 'GEN';
    $levelName = $academicLevel['level_name'] ?? 'ND1';
    $levelCode = $academicLevel['level_code'] ?? 'ND1';
    
    $prompt = "Generate exactly {$numQuestions} unique, diverse, and challenging multiple-choice exam questions for:

EDUCATIONAL CONTEXT:
- Institution: Ogbonnaya Onu Polytechnic, Aba (Nigeria)
- Department: {$departmentName} ({$departmentCode})
- Academic Level: {$levelName} ({$levelCode})
- Generation ID: {$uniqueId}
- Timestamp: {$timestamp}

REQUIREMENTS:
1. Each question must be UNIQUE and NON-REPETITIVE
2. Questions must be appropriate for polytechnic {$levelName} level
3. Cover broad topics within {$departmentName}
4. Mix theoretical knowledge and practical application
5. Vary difficulty from moderate to challenging
6. Each question must have exactly 4 options (A, B, C, D)
7. Only ONE correct answer per question
8. Options should be plausible but clearly distinguishable

QUESTION TYPES TO INCLUDE:
- Conceptual understanding
- Problem-solving
- Application-based scenarios
- Industry-relevant practices
- Technical specifications
- Safety procedures (if applicable)
- Current trends and technologies

{$customPrompt}";

    // Add context about previously asked questions to avoid repetition
    if (!empty($previousQuestions)) {
        $prompt .= "\n\nIMPORTANT - AVOID REPEATING THESE PREVIOUSLY ASKED QUESTIONS:\n";
        $prompt .= "The following questions have been asked before and must NOT be repeated:\n\n";

        foreach (array_slice($previousQuestions, 0, 10) as $index => $prevQ) {
            $prompt .= ($index + 1) . ". " . trim($prevQ['question_text']) . "\n";
        }

        $prompt .= "\nGenerate completely NEW and DIFFERENT questions that cover different topics, concepts, or approaches than those listed above.\n";
    }

    $prompt .= "

STRICT OUTPUT FORMAT - Return ONLY valid JSON array:
[
  {
    \"question\": \"Question text here?\",
    \"options\": {
      \"A\": \"Option A text\",
      \"B\": \"Option B text\", 
      \"C\": \"Option C text\",
      \"D\": \"Option D text\"
    },
    \"correct_answer\": \"A\",
    \"explanation\": \"Brief explanation of why this is correct\"
  }
]

IMPORTANT: 
- Return ONLY the JSON array, no additional text
- Ensure questions are unique for this generation session
- Make questions challenging but fair for {$levelName} students
- Focus on {$departmentName} curriculum and industry standards";

    return $prompt;
}

/**
 * Call Gemini API with proper error handling (using file_get_contents instead of cURL)
 */
function callGeminiAPI($prompt) {
    $data = [
        'contents' => [
            [
                'parts' => [
                    [
                        'text' => $prompt
                    ]
                ]
            ]
        ],
        'generationConfig' => [
            'temperature' => 0.8,  // Higher for more creativity
            'topK' => 40,
            'topP' => 0.95,
            'maxOutputTokens' => 4000,
            'candidateCount' => 1
        ],
        'safetySettings' => [
            [
                'category' => 'HARM_CATEGORY_HARASSMENT',
                'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
            ],
            [
                'category' => 'HARM_CATEGORY_HATE_SPEECH',
                'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
            ],
            [
                'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
            ],
            [
                'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
            ]
        ]
    ];

    $postData = json_encode($data);

    // Create stream context for HTTP request
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n" .
                       "User-Agent: LMS-Gemini-Client/1.0\r\n" .
                       "Content-Length: " . strlen($postData) . "\r\n",
            'content' => $postData,
            'timeout' => 30,
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        ]
    ]);

    $url = GEMINI_API_URL . '?key=' . GEMINI_API_KEY;

    // Make the API request
    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        $error = error_get_last();
        throw new Exception("HTTP Request failed: " . ($error['message'] ?? 'Unknown error'));
    }

    // Check HTTP response code
    if (isset($http_response_header)) {
        $httpCode = null;
        foreach ($http_response_header as $header) {
            if (preg_match('/^HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                $httpCode = (int)$matches[1];
                break;
            }
        }

        if ($httpCode && $httpCode !== 200) {
            $errorMsg = "HTTP Error {$httpCode}";
            $errorData = json_decode($response, true);
            if (isset($errorData['error']['message'])) {
                $errorMsg .= ": " . $errorData['error']['message'];
            }
            throw new Exception($errorMsg);
        }
    }

    $decodedResponse = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON response: " . json_last_error_msg());
    }

    return $decodedResponse;
}

/**
 * Parse and clean Gemini API response
 */
function parseGeminiResponse($content) {
    // Clean up the response
    $content = trim($content);
    
    // Remove markdown code blocks if present
    $content = preg_replace('/```json\s*/', '', $content);
    $content = preg_replace('/```\s*$/', '', $content);
    $content = trim($content);
    
    // Try to extract JSON if there's extra text
    if (preg_match('/\[.*\]/s', $content, $matches)) {
        $content = $matches[0];
    }
    
    $questions = json_decode($content, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON Parse Error: " . json_last_error_msg());
        error_log("Content: " . substr($content, 0, 500));
        return [];
    }
    
    return $questions;
}

/**
 * Validate questions format and content
 */
function validateQuestions($questions) {
    if (!is_array($questions)) {
        return [];
    }
    
    $validQuestions = [];
    
    foreach ($questions as $question) {
        if (isValidQuestion($question)) {
            $validQuestions[] = $question;
        }
    }
    
    return $validQuestions;
}

/**
 * Check if a single question is valid
 */
function isValidQuestion($question) {
    // Check required fields
    if (!isset($question['question']) || !isset($question['options']) || !isset($question['correct_answer'])) {
        return false;
    }
    
    // Check question text
    if (empty(trim($question['question']))) {
        return false;
    }
    
    // Check options
    if (!is_array($question['options'])) {
        return false;
    }
    
    $requiredOptions = ['A', 'B', 'C', 'D'];
    foreach ($requiredOptions as $option) {
        if (!isset($question['options'][$option]) || empty(trim($question['options'][$option]))) {
            return false;
        }
    }
    
    // Check correct answer
    if (!in_array($question['correct_answer'], $requiredOptions)) {
        return false;
    }
    
    return true;
}

/**
 * Check if network connectivity is available
 */
function isNetworkAvailable() {
    // Check if allow_url_fopen is enabled
    if (!ini_get('allow_url_fopen')) {
        error_log("Network check: allow_url_fopen is disabled");
        return false;
    }

    // Try a simple connectivity test
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);

    $result = @file_get_contents('https://www.google.com', false, $context);
    if ($result === false) {
        error_log("Network check: Cannot reach external sites");
        return false;
    }

    return true;
}

/**
 * Test Gemini API connection
 */
function testGeminiConnection() {
    try {
        $testPrompt = "Generate 1 simple multiple choice question about basic mathematics. Return only JSON format with question, options (A,B,C,D), and correct_answer fields.";
        
        $response = callGeminiAPI($testPrompt);
        
        if ($response && isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'message' => 'Gemini API connection successful',
                'model' => 'gemini-1.5-flash-latest'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Invalid response from Gemini API'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Connection failed: ' . $e->getMessage()
        ];
    }
}
