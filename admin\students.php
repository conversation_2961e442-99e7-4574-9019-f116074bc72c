<?php
require_once '../config/database.php';
requireLogin('admin');

$message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $student_id = (int)($_POST['student_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'approve':
                executeQuery("UPDATE students SET is_approved = 1 WHERE id = ?", [$student_id]);
                $message = "Student approved successfully!";
                break;
                
            case 'reject':
                executeQuery("UPDATE students SET is_approved = 0 WHERE id = ?", [$student_id]);
                $message = "Student approval revoked!";
                break;
                
            case 'delete':
                executeQuery("DELETE FROM students WHERE id = ?", [$student_id]);
                $message = "Student deleted successfully!";
                break;
        }
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
    }
}

// Get filter
$status = $_GET['status'] ?? 'all';

// Get students with proper department and level information
$students = [];
try {
    $baseQuery = "
        SELECT s.*,
               COALESCE(d.name, 'Unknown Department') as department_name,
               COALESCE(al.level_name, 'Unknown Level') as level_name,
               s.registration_date as created_at
        FROM students s
        LEFT JOIN departments d ON s.department_id = d.id
        LEFT JOIN academic_levels al ON s.academic_level_id = al.id
    ";

    if ($status === 'pending') {
        $students = fetchAll($baseQuery . " WHERE (s.is_approved = 0 OR s.is_approved = FALSE OR s.is_approved IS NULL) ORDER BY s.id DESC");
    } elseif ($status === 'approved') {
        $students = fetchAll($baseQuery . " WHERE (s.is_approved = 1 OR s.is_approved = TRUE) ORDER BY s.id DESC");
    } else {
        $students = fetchAll($baseQuery . " ORDER BY s.id DESC");
    }
} catch (Exception $e) {
    error_log("Students query error: " . $e->getMessage());
    $students = [];
}

// Get counts with better handling
try {
    $totalStudents = fetchOne("SELECT COUNT(*) as count FROM students")['count'] ?? 0;
    $pendingStudents = fetchOne("SELECT COUNT(*) as count FROM students WHERE (is_approved = 0 OR is_approved = FALSE OR is_approved IS NULL)")['count'] ?? 0;
    $approvedStudents = fetchOne("SELECT COUNT(*) as count FROM students WHERE (is_approved = 1 OR is_approved = TRUE)")['count'] ?? 0;
} catch (Exception $e) {
    error_log("Count query error: " . $e->getMessage());
    $totalStudents = 0;
    $pendingStudents = 0;
    $approvedStudents = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students Management - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item active">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                    <?php if ($pendingStudents > 0): ?>
                        <span class="badge"><?php echo $pendingStudents; ?></span>
                    <?php endif; ?>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="game-analytics.php" class="nav-item">
                    <i class="fas fa-gamepad"></i>
                    <span>Game Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="admin-profile">
                    <div class="admin-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="admin-info">
                        <div class="admin-name">Administrator</div>
                        <div class="admin-role">System Admin</div>
                    </div>
                </div>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Students Management</h1>
                    <p>Manage student registrations and approvals</p>
                </div>
                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $totalStudents; ?></span>
                            <span class="stat-label">Total</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $approvedStudents; ?></span>
                            <span class="stat-label">Approved</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $pendingStudents; ?></span>
                            <span class="stat-label">Pending</span>
                        </div>
                    </div>
                    <?php if ($pendingStudents > 0): ?>
                        <div class="quick-actions">
                            <a href="?status=pending" class="btn btn-warning">
                                <i class="fas fa-clock"></i>
                                View Pending Students
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </header>
            
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <a href="?status=all" class="tab <?php echo $status === 'all' ? 'active' : ''; ?>">
                    All Students (<?php echo $totalStudents; ?>)
                </a>
                <a href="?status=pending" class="tab <?php echo $status === 'pending' ? 'active' : ''; ?>">
                    Pending Approval (<?php echo $pendingStudents; ?>)
                </a>
                <a href="?status=approved" class="tab <?php echo $status === 'approved' ? 'active' : ''; ?>">
                    Approved (<?php echo $approvedStudents; ?>)
                </a>
            </div>
            
            <!-- Students List -->
            <div class="students-container">
                <?php if (empty($students)): ?>
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <h3>No Students Found</h3>
                        <p>No students match the current filter.</p>

                        <?php if ($totalStudents > 0): ?>
                            <div class="empty-state-actions">
                                <a href="?status=all" class="btn btn-primary">
                                    <i class="fas fa-users"></i>
                                    SHOW ALL STUDENTS
                                </a>

                                <?php if ($status !== 'pending' && $pendingStudents > 0): ?>
                                    <a href="?status=pending" class="btn btn-warning">
                                        <i class="fas fa-clock"></i>
                                        View <?php echo $pendingStudents; ?> Pending Student<?php echo $pendingStudents > 1 ? 's' : ''; ?>
                                    </a>
                                <?php endif; ?>

                                <a href="?status=all&refresh=1" class="btn btn-secondary">
                                    <i class="fas fa-sync"></i>
                                    Refresh Data
                                </a>

                                <?php if ($status !== 'approved' && $approvedStudents > 0): ?>
                                    <a href="?status=approved" class="btn btn-success">
                                        <i class="fas fa-check-circle"></i>
                                        View <?php echo $approvedStudents; ?> Approved Student<?php echo $approvedStudents > 1 ? 's' : ''; ?>
                                    </a>
                                <?php endif; ?>
                            </div>

                            <div class="filter-suggestion">
                                <p><strong>Current filter:</strong> <?php echo ucfirst($status); ?> Students</p>
                                <p><strong>Database totals:</strong> <?php echo $totalStudents; ?> total, <?php echo $pendingStudents; ?> pending, <?php echo $approvedStudents; ?> approved</p>
                            </div>
                        <?php else: ?>
                            <div class="empty-state-actions">
                                <p>No students have registered yet.</p>
                                <a href="../student/register.php" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i>
                                    GO TO REGISTRATION PAGE
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="students-grid">
                        <?php foreach ($students as $index => $student): ?>
                            <div class="student-card-horizontal">
                                <div class="student-number">
                                    <?php echo $index + 1; ?>
                                </div>
                                <div class="student-header">
                                    <div class="student-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="student-info">
                                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                                        <p class="student-id"><?php echo htmlspecialchars($student['student_id']); ?></p>
                                        <p class="student-email"><?php echo htmlspecialchars($student['email']); ?></p>
                                    </div>
                                    <div class="student-status">
                                        <?php if ($student['is_approved']): ?>
                                            <span class="status approved">
                                                <i class="fas fa-check-circle"></i>
                                                Approved
                                            </span>
                                        <?php else: ?>
                                            <span class="status pending">
                                                <i class="fas fa-clock"></i>
                                                Pending
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="student-details">
                                    <div class="detail-row">
                                        <span class="label">Department:</span>
                                        <span class="value"><?php echo htmlspecialchars($student['department_name'] ?? 'N/A'); ?></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="label">Level:</span>
                                        <span class="value"><?php echo htmlspecialchars($student['level_name'] ?? 'N/A'); ?></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="label">Joined:</span>
                                        <span class="value"><?php echo date('M d, Y', strtotime($student['created_at'])); ?></span>
                                    </div>
                                </div>
                                
                                <div class="student-actions">
                                    <?php if ($student['is_approved']): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="reject">
                                            <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                            <button type="submit" class="btn btn-warning" onclick="return confirm('Revoke approval for this student?')">
                                                <i class="fas fa-times"></i>
                                                Revoke
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="approve">
                                            <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                            <button type="submit" class="btn btn-success" onclick="return confirm('Approve this student?')">
                                                <i class="fas fa-check"></i>
                                                Approve
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                        <button type="submit" class="btn btn-danger" onclick="return confirm('⚠️ PERMANENT DELETION\n\nThis will permanently delete:\n• Student account and profile\n• All quiz attempts and scores\n• All game progress and achievements\n• All activity logs\n\nThis action CANNOT be undone!\n\nAre you absolutely sure?')"
                                            <i class="fas fa-trash"></i>
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <style>
    /* Clean Students Management Styles */
    .filter-tabs {
        display: flex;
        gap: 10px;
        margin: 20px 0;
        border-bottom: 2px solid #e5e7eb;
    }

    .filter-tabs .tab {
        padding: 15px 25px;
        text-decoration: none;
        color: #6b7280;
        border-bottom: 3px solid transparent;
        transition: all 0.2s ease;
        font-weight: 600;
        font-size: 16px;
        border-radius: 8px 8px 0 0;
        background: #f9fafb;
        margin-bottom: -2px;
        position: relative;
    }

    .filter-tabs .tab:hover {
        color: #4f46e5;
        border-bottom-color: #4f46e5;
        background: #f3f4f6;
        transform: translateY(-2px);
    }

    .filter-tabs .tab.active {
        color: #4f46e5;
        border-bottom-color: #4f46e5;
        background: white;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
        z-index: 1;
    }

    .students-container {
        margin-top: 20px;
    }

    .students-grid {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .student-card-horizontal {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 20px;
        position: relative;
    }

    .student-card-horizontal:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    .student-number {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 16px;
        flex-shrink: 0;
    }

    .student-header {
        display: flex;
        align-items: center;
        gap: 15px;
        flex: 1;
    }

    .student-avatar {
        width: 50px;
        height: 50px;
        background: #f3f4f6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6b7280;
        font-size: 20px;
    }

    .student-info h4 {
        margin: 0 0 5px 0;
        color: #1f2937;
        font-size: 18px;
        font-weight: 600;
    }

    .student-id {
        color: #6b7280;
        font-size: 14px;
        margin: 0;
        font-weight: 500;
    }

    .student-email {
        color: #9ca3af;
        font-size: 13px;
        margin: 0;
    }

    .student-status {
        margin-left: auto;
    }

    .status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .status.approved {
        background: #dcfce7;
        color: #166534;
    }

    .status.pending {
        background: #fef3c7;
        color: #92400e;
    }

    .student-details {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 15px;
        background: #f9fafb;
        border-radius: 8px;
        min-width: 200px;
        margin: 0 15px;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
    }

    .detail-row .label {
        color: #6b7280;
        font-size: 14px;
    }

    .detail-row .value {
        color: #1f2937;
        font-size: 14px;
        font-weight: 500;
    }

    .student-actions {
        display: flex;
        gap: 10px;
        align-items: center;
        flex-shrink: 0;
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        transition: all 0.2s ease;
    }

    .btn-success {
        background: #10b981;
        color: white;
    }

    .btn-success:hover {
        background: #059669;
    }

    .btn-warning {
        background: #f59e0b;
        color: white;
    }

    .btn-warning:hover {
        background: #d97706;
    }

    .btn-danger {
        background: #ef4444;
        color: white;
    }

    .btn-danger:hover {
        background: #dc2626;
    }

    .btn-primary {
        background: #4f46e5;
        color: white;
    }

    .btn-primary:hover {
        background: #4338ca;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 48px;
        margin-bottom: 20px;
        opacity: 0.3;
    }

    .empty-state h3 {
        margin: 0 0 10px 0;
        color: #374151;
    }

    .empty-state-actions {
        margin: 30px 0;
        display: flex;
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .empty-state-actions .btn {
        min-width: 200px;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .filter-suggestion {
        background: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
        text-align: left;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .filter-suggestion p {
        margin: 5px 0;
        font-size: 14px;
    }

    .filter-suggestion p:first-child {
        font-weight: 600;
        color: #374151;
    }

    .alert {
        padding: 15px 20px;
        border-radius: 8px;
        margin: 20px 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .alert-success {
        background: #dcfce7;
        color: #166534;
        border: 1px solid #bbf7d0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .student-card-horizontal {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .student-header {
            width: 100%;
        }

        .student-details {
            width: 100%;
            margin: 0;
        }

        .student-actions {
            width: 100%;
            justify-content: center;
        }

        .student-number {
            position: absolute;
            top: 15px;
            right: 15px;
        }
    }
    </style>
</body>
</html>
