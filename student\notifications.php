<?php
/**
 * Student Notifications System
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Check if student is logged in
startSecureSession();
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'student') {
    header('Location: login.php');
    exit();
}

$studentId = $_SESSION['user_id'];

// Get student information
$student = fetchOne("
    SELECT s.*, d.name as department_name, al.level_name
    FROM students s
    JOIN departments d ON s.department_id = d.id
    JOIN academic_levels al ON s.academic_level_id = al.id
    WHERE s.id = :id
", ['id' => $studentId]);

// Mark notification as read if requested
if (isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    updateRecord('notifications', 
        ['is_read' => true], 
        'id = :id AND student_id = :student_id', 
        ['id' => $_GET['mark_read'], 'student_id' => $studentId]
    );
    header('Location: notifications.php');
    exit();
}

// Mark all as read if requested
if (isset($_GET['mark_all_read'])) {
    executeQuery("UPDATE notifications SET is_read = 1 WHERE student_id = :student_id", 
        ['student_id' => $studentId]);
    header('Location: notifications.php');
    exit();
}

// Get notifications
$notifications = fetchAll("
    SELECT * FROM notifications 
    WHERE student_id = :student_id 
    ORDER BY created_at DESC
", ['student_id' => $studentId]);

// Get unread count
$unreadCount = fetchOne("
    SELECT COUNT(*) as count 
    FROM notifications 
    WHERE student_id = :student_id AND is_read = 0
", ['student_id' => $studentId])['count'] ?? 0;

$pageTitle = "Notifications";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Student-specific styling */
        .sidebar-header h3 { color: #28a745; }
        .nav-item.active { background: linear-gradient(135deg, #28a745, #20c997); }
        .btn-primary { background: linear-gradient(135deg, #28a745, #20c997); }
        
        .notifications-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .notifications-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .notifications-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .notifications-title h2 {
            color: #2c3e50;
            margin: 0;
        }
        
        .unread-badge {
            background: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .mark-all-read {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.2s ease;
        }
        
        .mark-all-read:hover {
            background: #218838;
            color: white;
        }
        
        .notification-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            transition: all 0.2s ease;
            border-left: 4px solid transparent;
        }
        
        .notification-item.unread {
            background: #f8f9fa;
            border-left-color: #28a745;
        }
        
        .notification-item.read {
            background: #ffffff;
            border-left-color: #e9ecef;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
        }
        
        .notification-icon.achievement { background: #ffc107; }
        .notification-icon.quiz { background: #28a745; }
        .notification-icon.reminder { background: #17a2b8; }
        .notification-icon.system { background: #6c757d; }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .notification-message {
            color: #6c757d;
            line-height: 1.5;
            margin-bottom: 8px;
        }
        
        .notification-time {
            font-size: 12px;
            color: #adb5bd;
        }
        
        .notification-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .mark-read-btn {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Student Portal</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="quick-game.php" class="nav-item">
                    <i class="fas fa-bolt"></i>
                    <span>Quick Game</span>
                </a>
                <a href="endless-game.php" class="nav-item">
                    <i class="fas fa-infinity"></i>
                    <span>Endless Mode</span>
                </a>
                <a href="mission-mode.php" class="nav-item">
                    <i class="fas fa-map"></i>
                    <span>Mission Mode</span>
                </a>
                <a href="results.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>My Results</span>
                </a>
                <a href="notifications.php" class="nav-item active">
                    <i class="fas fa-bell"></i>
                    <span>Notifications</span>
                    <?php if ($unreadCount > 0): ?>
                        <span class="nav-badge"><?php echo $unreadCount; ?></span>
                    <?php endif; ?>
                </a>
                <a href="profile.php" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
                <a href="../index.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Go Back to Site</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <h4><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h4>
                        <p><?php echo htmlspecialchars($student['student_id']); ?></p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1><i class="fas fa-bell"></i> Notifications</h1>
                    <p>Stay updated with your learning progress and achievements</p>
                </div>
            </header>

            <div class="notifications-container">
                <div class="notifications-header">
                    <div class="notifications-title">
                        <h2>Your Notifications</h2>
                        <?php if ($unreadCount > 0): ?>
                            <span class="unread-badge"><?php echo $unreadCount; ?> unread</span>
                        <?php endif; ?>
                    </div>
                    <?php if ($unreadCount > 0): ?>
                        <a href="?mark_all_read=1" class="mark-all-read">
                            <i class="fas fa-check-double"></i> Mark All Read
                        </a>
                    <?php endif; ?>
                </div>

                <?php if (empty($notifications)): ?>
                    <div class="empty-state">
                        <i class="fas fa-bell-slash"></i>
                        <h3>No notifications yet</h3>
                        <p>You'll see your achievements, reminders, and updates here.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($notifications as $notification): ?>
                        <div class="notification-item <?php echo $notification['is_read'] ? 'read' : 'unread'; ?>">
                            <div class="notification-icon <?php echo $notification['type']; ?>">
                                <?php
                                switch ($notification['type']) {
                                    case 'achievement':
                                        echo '<i class="fas fa-trophy"></i>';
                                        break;
                                    case 'quiz':
                                        echo '<i class="fas fa-brain"></i>';
                                        break;
                                    case 'reminder':
                                        echo '<i class="fas fa-clock"></i>';
                                        break;
                                    default:
                                        echo '<i class="fas fa-info"></i>';
                                }
                                ?>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                <div class="notification-time"><?php echo date('M j, Y g:i A', strtotime($notification['created_at'])); ?></div>
                            </div>
                            <div class="notification-actions">
                                <?php if (!$notification['is_read']): ?>
                                    <a href="?mark_read=<?php echo $notification['id']; ?>" class="mark-read-btn">
                                        <i class="fas fa-check"></i> Mark Read
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
