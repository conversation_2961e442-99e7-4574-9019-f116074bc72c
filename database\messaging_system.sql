-- Messaging System for AI-Powered LMS
-- Ogbonnaya Onu Polytechnic, Aba

-- Messages table for admin-student communication
CREATE TABLE IF NOT EXISTS messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id INT NOT NULL,
    sender_type <PERSON>NU<PERSON>('admin', 'student') NOT NULL,
    receiver_id INT NOT NULL,
    receiver_type ENUM('admin', 'student') NOT NULL,
    subject VARCHAR(255) DEFAULT 'Message',
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    INDEX idx_sender (sender_id, sender_type),
    INDEX idx_receiver (receiver_id, receiver_type),
    INDEX idx_created (created_at DESC),
    INDEX idx_unread (is_read, receiver_id, receiver_type)
) ENGINE=InnoDB;

-- Message threads for grouping conversations
CREATE TABLE IF NOT EXISTS message_threads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    student_id INT NOT NULL,
    subject VA<PERSON>HA<PERSON>(255) DEFAULT 'Conversation',
    last_message_id INT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    admin_unread_count INT DEFAULT 0,
    student_unread_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    UNIQUE KEY unique_admin_student (admin_id, student_id),
    INDEX idx_last_activity (last_activity DESC),
    INDEX idx_admin_unread (admin_id, admin_unread_count),
    INDEX idx_student_unread (student_id, student_unread_count)
) ENGINE=InnoDB;

-- Add foreign key for last_message_id after messages table is created
ALTER TABLE message_threads 
ADD CONSTRAINT fk_thread_last_message 
FOREIGN KEY (last_message_id) REFERENCES messages(id) ON DELETE SET NULL;

-- Add thread_id to messages table for better organization
ALTER TABLE messages 
ADD COLUMN thread_id INT NULL,
ADD CONSTRAINT fk_message_thread 
FOREIGN KEY (thread_id) REFERENCES message_threads(id) ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX idx_messages_thread ON messages(thread_id, created_at);
CREATE INDEX idx_messages_unread_receiver ON messages(receiver_id, receiver_type, is_read);

-- Sample data (optional - remove in production)
-- INSERT INTO message_threads (admin_id, student_id, subject) VALUES (1, 1, 'Welcome Message');
-- INSERT INTO messages (sender_id, sender_type, receiver_id, receiver_type, thread_id, subject, message) 
-- VALUES (1, 'admin', 1, 'student', 1, 'Welcome', 'Welcome to the AI-Powered LMS! Feel free to ask any questions.');
