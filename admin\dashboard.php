<?php
/**
 * Admin Dashboard for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Require admin login
requireLogin('admin');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'approve_student':
                    $id = (int)$_POST['id'];
                    executeQuery("UPDATE students SET is_approved = 1 WHERE id = :id", ['id' => $id]);
                    $_SESSION['success'] = "Student approved successfully!";
                    header('Location: dashboard.php');
                    exit;
                    break;
            }
        }
    } catch (Exception $e) {
        error_log("Dashboard action error: " . $e->getMessage());
        $_SESSION['error'] = "An error occurred. Please try again.";
    }
}

// Get admin info
$adminId = $_SESSION['user_id'];
$admin = fetchOne("SELECT * FROM admins WHERE id = :id", ['id' => $adminId]);

// Get dashboard statistics with error handling
try {
    $stats = [
        'total_students' => fetchOne("SELECT COUNT(*) as count FROM students")['count'] ?? 0,
        'approved_students' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 1")['count'] ?? 0,
        'pending_approvals' => fetchOne("SELECT COUNT(*) as count FROM students WHERE is_approved = 0")['count'] ?? 0,
        'total_departments' => fetchOne("SELECT COUNT(*) as count FROM departments")['count'] ?? 0,

        'active_sessions' => fetchOne("SELECT COUNT(*) as count FROM user_sessions WHERE is_active = 1 AND user_type = 'student' AND expires_at > NOW()")['count'] ?? 0,
        'total_games_today' => fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE DATE(created_at) = CURDATE()")['count'] ?? 0,
        'total_quizzes_completed' => fetchOne("SELECT COUNT(*) as count FROM quiz_sessions WHERE status = 'completed'")['count'] ?? 0,
        'avg_score_today' => fetchOne("SELECT AVG(score_percentage) as avg FROM quiz_sessions WHERE DATE(completed_at) = CURDATE() AND status = 'completed'")['avg'] ?? 0,
        'total_notifications' => fetchOne("SELECT COUNT(*) as count FROM notifications")['count'] ?? 0,
        'students_active_today' => fetchOne("SELECT COUNT(DISTINCT student_id) as count FROM quiz_sessions WHERE DATE(created_at) = CURDATE()")['count'] ?? 0,
        'total_xp_earned' => fetchOne("SELECT SUM(xp_earned) as total FROM quiz_sessions WHERE status = 'completed'")['total'] ?? 0
    ];



    // Get departments with student counts
    $departmentStats = fetchAll("
        SELECT d.name, d.code, COUNT(s.id) as student_count,
               COUNT(CASE WHEN s.is_approved = 1 THEN 1 END) as approved_count
        FROM departments d
        LEFT JOIN students s ON d.id = s.department_id
        GROUP BY d.id, d.name, d.code
        ORDER BY student_count DESC
        LIMIT 10
    ");

    // Get system performance analytics
    $systemAnalytics = [
        'daily_activity' => fetchAll("
            SELECT DATE(created_at) as date,
                   COUNT(*) as quiz_count,
                   COUNT(DISTINCT student_id) as active_students,
                   AVG(score_percentage) as avg_score
            FROM quiz_sessions
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        "),
        'top_performers' => fetchAll("
            SELECT s.first_name, s.last_name, s.student_id,
                   AVG(qs.score_percentage) as avg_score,
                   COUNT(qs.id) as quiz_count,
                   MAX(qs.score_percentage) as best_score
            FROM students s
            JOIN quiz_sessions qs ON s.id = qs.student_id
            WHERE qs.status = 'completed' AND s.is_approved = 1
            GROUP BY s.id
            HAVING quiz_count >= 3
            ORDER BY avg_score DESC
            LIMIT 5
        "),
        'engagement_metrics' => fetchAll("
            SELECT d.name as department,
                   COUNT(DISTINCT qs.student_id) as active_students,
                   COUNT(qs.id) as total_quizzes,
                   AVG(qs.score_percentage) as avg_score
            FROM departments d
            LEFT JOIN quiz_sessions qs ON d.id = qs.department_id
            WHERE qs.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY d.id, d.name
            ORDER BY active_students DESC
        ")
    ];

    // Get recent system alerts/notifications
    $systemAlerts = [];

    // Check for low-performing students
    $strugglingStudents = fetchOne("
        SELECT COUNT(*) as count
        FROM students s
        JOIN quiz_sessions qs ON s.id = qs.student_id
        WHERE qs.status = 'completed' AND s.is_approved = 1
        GROUP BY s.id
        HAVING AVG(qs.score_percentage) < 50 AND COUNT(qs.id) >= 3
    ")['count'] ?? 0;

    if ($strugglingStudents > 0) {
        $systemAlerts[] = [
            'type' => 'warning',
            'title' => 'Students Need Support',
            'message' => "$strugglingStudents students are consistently scoring below 50%",
            'action' => 'View Students',
            'url' => 'students.php?filter=struggling'
        ];
    }

    // Check for inactive students
    $inactiveStudents = fetchOne("
        SELECT COUNT(*) as count
        FROM students s
        WHERE s.is_approved = 1
        AND s.id NOT IN (
            SELECT DISTINCT student_id
            FROM quiz_sessions
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        )
    ")['count'] ?? 0;

    if ($inactiveStudents > 10) {
        $systemAlerts[] = [
            'type' => 'info',
            'title' => 'Student Engagement',
            'message' => "$inactiveStudents students haven't been active in the past week",
            'action' => 'Send Reminders',
            'url' => '#'
        ];
    }


} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $stats = [
        'total_students' => 0,
        'approved_students' => 0,
        'pending_approvals' => 0,
        'total_departments' => 0,

        'active_sessions' => 0,
        'total_games_today' => 0
    ];
    $recentStudents = [];
    $departmentStats = [];
    $recentGames = [];
}

$pageTitle = 'Admin Dashboard';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Dashboard Layout Improvements */
        .main-content {
            flex: 1;
            padding: 2rem;
            background: #f8fafc;
            min-height: 100vh;
            overflow-x: auto !important;
            overflow-y: auto !important;
            position: relative;
        }

        .dashboard-content {
            max-width: none !important;
            margin: 0 !important;
            width: 100% !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 1200px) {
            .content-grid {
                grid-template-columns: 2fr 1fr;
            }
        }

        .content-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e5e7eb;
            width: 100%;
            box-sizing: border-box;
        }

        .content-card.full-width {
            grid-column: 1 / -1;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f1f5f9;
        }

        .card-header h3 {
            margin: 0;
            font-size: 1.375rem;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-header h3 i {
            color: #6366f1;
        }

        .department-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .department-item {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
            width: 100%;
            box-sizing: border-box;
            min-height: 80px;
            position: relative;
        }

        .department-item:hover {
            border-color: #48bb78;
            background: #f0fff4;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .dept-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .dept-info {
            flex: 1;
            min-width: 0;
            padding-right: 1rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .dept-info h4 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1a202c;
            margin: 0 0 0.5rem 0;
            line-height: 1.4;
            word-wrap: break-word;
            display: block;
            width: 100%;
        }

        .dept-code {
            font-size: 0.875rem;
            color: #718096;
            margin: 0;
            font-weight: 500;
        }

        .dept-stats {
            display: flex;
            gap: 2rem;
            align-items: center;
            flex-shrink: 0;
        }

        .stat-item {
            text-align: center;
            min-width: 70px;
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            line-height: 1.2;
        }

        .stat-number.approved {
            color: #48bb78;
        }

        .stat-label {
            display: block;
            font-size: 0.75rem;
            color: #a0aec0;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-top: 0.25rem;
        }

        .dept-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            color: #cbd5e0;
            font-size: 1.25rem;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .department-item:hover .dept-arrow {
            color: #48bb78;
            transform: translateX(4px);
        }

        /* Responsive adjustments for department items */
        @media (max-width: 768px) {
            .department-item {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
                padding: 1.5rem;
            }

            .dept-info {
                padding-right: 0;
                text-align: center;
            }

            .dept-stats {
                gap: 1.5rem;
                justify-content: center;
            }

            .dept-arrow {
                display: none;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .dept-stats {
                gap: 1rem;
            }

            .stat-item {
                min-width: 60px;
            }

            .stat-number {
                font-size: 1.25rem;
            }
        }

        /* Ensure department names are always visible */
        .dept-info h4 {
            overflow: visible !important;
            white-space: normal !important;
            text-overflow: unset !important;
        }

        /* Game Analytics Card Styles */
        .action-card.game-analytics {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .action-card.game-analytics .action-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .action-card.game-analytics .action-content h3 {
            color: white;
        }

        .action-card.game-analytics .action-content p {
            color: rgba(255, 255, 255, 0.9);
        }

        .live-indicator {
            background: #10b981 !important;
            color: white !important;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Settings Card Styles */
        .action-card.settings {
            background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
            color: white;
        }

        .action-card.settings .action-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .action-card.settings .action-content h3 {
            color: white;
        }

        .action-card.settings .action-content p {
            color: rgba(255, 255, 255, 0.9);
        }

        /* Additional layout fixes - Override existing styles */
        .main-content {
            margin-left: 280px !important;
            width: calc(100vw - 280px) !important;
            overflow-x: auto !important;
            overflow-y: auto !important;
            box-sizing: border-box !important;
            padding: 2rem !important;
        }

        /* Ensure dashboard content is scrollable */
        .stats-grid {
            min-width: 1200px; /* Minimum width to show all cards */
            overflow-x: auto !important;
        }

        .content-grid {
            grid-template-columns: 1fr !important; /* Single column since we only have one section */
            min-width: auto !important;
            overflow-x: visible !important;
        }

        /* Add scrollbar styling */
        .main-content::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.6);
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.8);
        }

        /* Responsive adjustments */
        @media (max-width: 1400px) {
            .stats-grid {
                min-width: 1000px;
            }
        }

        @media (max-width: 1200px) {
            .stats-grid {
                min-width: 800px;
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0 !important;
                width: 100vw !important;
            }
            .stats-grid {
                min-width: auto;
                grid-template-columns: 1fr;
            }
        }

        /* Alert Messages */
        .alert {
            padding: 1rem 1.5rem;
            margin: 1rem 0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            animation: slideIn 0.3s ease-out;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert i {
            font-size: 1.2rem;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Message Modal Styles */
        .message-modal {
            max-width: 600px;
        }

        .recipient-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 6px;
            color: #4a5568;
            font-weight: 500;
        }

        .recipient-info i {
            color: #667eea;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2d3748;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: border-color 0.2s ease;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control textarea {
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e2e8f0;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        /* Fix for department section specifically */
        .content-card .card-content {
            width: 100%;
            overflow: visible;
        }

        .department-list {
            width: 100%;
            max-width: none;
        }

        /* Ensure proper text wrapping for long department names */
        .dept-info {
            word-break: break-word;
            hyphens: auto;
        }

        /* Better spacing for the entire dashboard */
        .dashboard-header {
            margin-bottom: 2rem;
        }

        .dashboard-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 0.5rem 0;
        }

        .dashboard-header p {
            color: #6b7280;
            margin: 0;
            font-size: 1.125rem;
        }

        .game-activity-table {
            overflow-x: auto;
        }

        .game-activity-table table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .game-activity-table th,
        .game-activity-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .game-activity-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .student-info {
            min-width: 150px;
        }

        .student-info strong {
            display: block;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .student-info small {
            color: #6b7280;
            font-size: 0.75rem;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 3rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }

        .empty-state h4 {
            margin: 0 0 0.5rem 0;
            color: #374151;
        }

        .empty-state p {
            margin: 0 0 1rem 0;
        }

        /* Real-time Scores Styles */
        .game-modes-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid #f1f5f9;
            padding-bottom: 1rem;
        }

        .tab-btn {
            background: none;
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tab-btn:hover {
            border-color: #6366f1;
            color: #6366f1;
        }

        .tab-btn.active {
            background: #6366f1;
            border-color: #6366f1;
            color: white;
        }

        .scores-content {
            display: none;
        }

        .scores-content.active {
            display: block;
        }

        .student-score-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            margin-bottom: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .student-score-item:hover {
            border-left-color: #6366f1;
            background: #f1f5f9;
            transform: translateX(4px);
        }

        .student-score-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .student-score-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }

        .student-score-details h4 {
            margin: 0;
            font-size: 1rem;
            color: #1f2937;
            font-weight: 600;
        }

        .student-score-details p {
            margin: 0;
            font-size: 0.875rem;
            color: #6b7280;
        }

        .student-score-stats {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .score-stat {
            text-align: center;
        }

        .score-stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .score-stat-label {
            font-size: 0.75rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .loading-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }

        .loading-state i {
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.jpg" alt="Logo" class="sidebar-logo">
                <div class="sidebar-title">
                    <h3>Admin Panel</h3>
                    <p>AI-Powered LMS</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.php" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="students.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Students</span>
                    <?php if ($stats['pending_approvals'] > 0): ?>
                        <span class="badge"><?php echo $stats['pending_approvals']; ?></span>
                    <?php endif; ?>
                </a>
                <a href="departments.php" class="nav-item">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
                <a href="game-analytics.php" class="nav-item">
                    <i class="fas fa-gamepad"></i>
                    <span>Game Analytics</span>
                </a>
                <a href="settings.php" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>

                <div class="nav-divider"></div>

                <a href="../index.php" class="nav-item nav-secondary">
                    <i class="fas fa-home"></i>
                    <span>Back to Site</span>
                </a>
                <a href="logout.php" class="nav-item nav-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="admin-profile">
                    <div class="admin-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="admin-info">
                        <span class="admin-name"><?php echo htmlspecialchars($admin['full_name'] ?? 'Admin'); ?></span>
                        <span class="admin-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="nav-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <h1>Dashboard Overview</h1>
                    <p>Welcome back, <?php echo htmlspecialchars($admin['full_name'] ?? $_SESSION['username']); ?>! Here's what's happening today.</p>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="location.href='students.php'">
                            <i class="fas fa-plus"></i>
                            Manage Students
                        </button>
                        <div class="header-stats">
                            <span class="stat-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('M d, Y'); ?>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                <?php echo date('H:i'); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Messages -->
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="students.php" class="action-card students">
                    <div class="action-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="action-content">
                        <h3>Manage Students</h3>
                        <p>View, approve, and manage student accounts</p>
                        <?php if ($stats['pending_approvals'] > 0): ?>
                            <span class="action-badge"><?php echo $stats['pending_approvals']; ?> pending</span>
                        <?php endif; ?>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="departments.php" class="action-card departments">
                    <div class="action-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="action-content">
                        <h3>Departments</h3>
                        <p>Manage departments and courses</p>
                        <span class="action-badge"><?php echo $stats['total_departments']; ?> departments</span>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="game-analytics.php" class="action-card game-analytics">
                    <div class="action-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <div class="action-content">
                        <h3>Game Analytics</h3>
                        <p>Real-time student scores & levels</p>
                        <span class="action-badge live-indicator">Live</span>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>

                <a href="settings.php" class="action-card settings">
                    <div class="action-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="action-content">
                        <h3>Settings</h3>
                        <p>System configuration and preferences</p>
                        <span class="action-badge">Configure</span>
                    </div>
                    <div class="action-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid" title="Scroll horizontally to see all statistics">
                <div class="stat-card primary" onclick="location.href='students.php'">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_students']); ?></h3>
                        <p>Total Students</p>
                        <span class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <?php echo number_format($stats['approved_students']); ?> approved
                        </span>
                    </div>
                </div>

                <div class="stat-card warning" onclick="location.href='students.php?filter=pending'">
                    <div class="stat-icon">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['pending_approvals']); ?></h3>
                        <p>Pending Approvals</p>
                        <span class="stat-change">
                            <i class="fas fa-clock"></i>
                            Requires attention
                        </span>
                    </div>
                </div>

                <div class="stat-card success" onclick="location.href='departments.php'">
                    <div class="stat-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_departments']); ?></h3>
                        <p>Departments</p>
                        <span class="stat-change">
                            <i class="fas fa-graduation-cap"></i>
                            Active programs
                        </span>
                    </div>
                </div>

                <div class="stat-card purple" onclick="location.href='game-analytics.php'">
                    <div class="stat-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_games_today']); ?></h3>
                        <p>Games Today</p>
                        <span class="stat-change">
                            <i class="fas fa-chart-line"></i>
                            Daily activity
                        </span>
                    </div>
                </div>
            </div>

            <!-- Real-time Student Scores -->
            <div class="content-grid">
                <div class="content-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-trophy"></i> Real-time Student Scores & Levels</h3>
                        <div class="live-indicator">
                            <div class="live-dot"></div>
                            Live Updates
                        </div>
                    </div>

                    <div class="game-modes-tabs">
                        <button class="tab-btn active" onclick="switchGameMode('quick')">
                            <i class="fas fa-bolt"></i> Quick Game
                        </button>
                        <button class="tab-btn" onclick="switchGameMode('endless')">
                            <i class="fas fa-infinity"></i> Endless Mode
                        </button>
                        <button class="tab-btn" onclick="switchGameMode('mission')">
                            <i class="fas fa-flag-checkered"></i> Mission Mode
                        </button>
                    </div>

                    <div id="quick-scores" class="scores-content active">
                        <div class="loading-state">Loading Quick Game scores...</div>
                    </div>

                    <div id="endless-scores" class="scores-content">
                        <div class="loading-state">Loading Endless Mode scores...</div>
                    </div>

                    <div id="mission-scores" class="scores-content">
                        <div class="loading-state">Loading Mission Mode progress...</div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Student Progress Modal -->
    <div id="studentProgressModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Student Progress</h3>
                <span class="close" onclick="closeModal('studentProgressModal')">&times;</span>
            </div>
            <div class="modal-body" id="studentProgressContent">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    Loading student progress...
                </div>
            </div>
        </div>
    </div>

    <!-- Message Modal -->
    <div id="messageModal" class="modal">
        <div class="modal-content message-modal">
            <div class="modal-header">
                <h3><i class="fas fa-envelope"></i> Send Message</h3>
                <span class="close" onclick="closeModal('messageModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="messageForm" method="POST" action="ajax/send-message.php">
                    <input type="hidden" id="messageStudentId" name="student_id" value="">

                    <div class="form-group">
                        <label>To:</label>
                        <div class="recipient-info">
                            <i class="fas fa-user"></i>
                            <span id="recipientName">Student Name</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="messageSubject">Subject:</label>
                        <input type="text" id="messageSubject" name="subject" class="form-control" placeholder="Enter message subject" required>
                    </div>

                    <div class="form-group">
                        <label for="messageContent">Message:</label>
                        <textarea id="messageContent" name="message" class="form-control" rows="6" placeholder="Type your message here..." required></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('messageModal')">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            Send Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>



    <script src="../assets/js/admin-dashboard.js"></script>
    <script>
        // Dashboard specific functions
        function viewStudentProgress(studentId) {
            const modal = document.getElementById('studentProgressModal');
            const content = document.getElementById('studentProgressContent');

            modal.style.display = 'block';
            content.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading student progress...</div>';

            fetch(`ajax/student-progress.php?id=${studentId}`)
                .then(response => response.text())
                .then(data => {
                    content.innerHTML = data;
                })
                .catch(error => {
                    content.innerHTML = '<div class="error">Error loading student progress. Please try again.</div>';
                });
        }



        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function approveStudent(studentId) {
            if (confirm('Are you sure you want to approve this student?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="approve_student">
                    <input type="hidden" name="id" value="${studentId}">
                `;

                document.body.appendChild(form);
                form.submit();
            }
        }

        function viewFullProfile(studentId) {
            window.location.href = `students.php?view=${studentId}`;
        }

        function openMessageModal(studentId, studentName) {
            document.getElementById('messageStudentId').value = studentId;
            document.getElementById('recipientName').textContent = studentName;
            document.getElementById('messageSubject').value = '';
            document.getElementById('messageContent').value = '';
            document.getElementById('messageModal').style.display = 'block';
        }

        // Handle message form submission
        document.addEventListener('DOMContentLoaded', function() {
            const messageForm = document.getElementById('messageForm');
            if (messageForm) {
                messageForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    // Show loading state
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                    submitBtn.disabled = true;

                    fetch('ajax/send-message.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            alert('✅ ' + data.message);
                            closeModal('messageModal');
                            // Reset form
                            messageForm.reset();
                        } else {
                            alert('❌ ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('❌ Failed to send message. Please try again.');
                    })
                    .finally(() => {
                        // Restore button state
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Real-time scores functionality
        let currentGameMode = 'quick';

        function switchGameMode(mode) {
            currentGameMode = mode;

            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Update content
            document.querySelectorAll('.scores-content').forEach(content => content.classList.remove('active'));
            document.getElementById(mode + '-scores').classList.add('active');

            // Load data for the selected mode
            loadGameModeScores(mode);
        }

        function loadGameModeScores(mode) {
            const contentEl = document.getElementById(mode + '-scores');
            contentEl.innerHTML = '<div class="loading-state"><i class="fas fa-spinner"></i><br>Loading ' + mode + ' scores...</div>';

            fetch('api/student-scores.php?mode=' + mode)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.students) {
                        displayStudentScores(data.students, mode, contentEl);
                    } else {
                        contentEl.innerHTML = '<div class="loading-state">No data available for ' + mode + ' mode</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading scores:', error);
                    contentEl.innerHTML = '<div class="loading-state">Error loading scores</div>';
                });
        }

        function displayStudentScores(students, mode, container) {
            if (!students || students.length === 0) {
                container.innerHTML = '<div class="loading-state">No students have played ' + mode + ' mode yet</div>';
                return;
            }

            let html = '';
            students.forEach((student, index) => {
                const initials = (student.first_name.charAt(0) + student.last_name.charAt(0)).toUpperCase();

                let stats = '';
                if (mode === 'quick') {
                    stats = `
                        <div class="score-stat">
                            <div class="score-stat-value">${student.best_score || 0}</div>
                            <div class="score-stat-label">Best Score</div>
                        </div>
                        <div class="score-stat">
                            <div class="score-stat-value">${student.games_played || 0}</div>
                            <div class="score-stat-label">Games</div>
                        </div>
                    `;
                } else if (mode === 'endless') {
                    stats = `
                        <div class="score-stat">
                            <div class="score-stat-value">${student.highest_score || 0}</div>
                            <div class="score-stat-label">High Score</div>
                        </div>
                        <div class="score-stat">
                            <div class="score-stat-value">${student.current_level || 1}</div>
                            <div class="score-stat-label">Level</div>
                        </div>
                    `;
                } else if (mode === 'mission') {
                    stats = `
                        <div class="score-stat">
                            <div class="score-stat-value">${student.current_level || 1}</div>
                            <div class="score-stat-label">Level</div>
                        </div>
                        <div class="score-stat">
                            <div class="score-stat-value">${student.missions_completed || 0}</div>
                            <div class="score-stat-label">Completed</div>
                        </div>
                    `;
                }

                html += `
                    <div class="student-score-item">
                        <div class="student-score-info">
                            <div class="student-score-avatar">${initials}</div>
                            <div class="student-score-details">
                                <h4>${student.first_name} ${student.last_name}</h4>
                                <p>${student.student_id}</p>
                            </div>
                        </div>
                        <div class="student-score-stats">
                            ${stats}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Load initial data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadGameModeScores('quick');
        });

        // Auto-refresh scores every 30 seconds
        setInterval(() => {
            loadGameModeScores(currentGameMode);
        }, 30000);

        // Auto-refresh stats every 30 seconds
        setInterval(() => {
            fetch('ajax/refresh-stats.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update stat cards
                        Object.keys(data.stats).forEach(key => {
                            const element = document.querySelector(`[data-stat="${key}"]`);
                            if (element) {
                                element.textContent = new Intl.NumberFormat().format(data.stats[key]);
                            }
                        });
                    }
                })
                .catch(error => console.log('Stats refresh failed:', error));
        }, 30000);
    </script>
</body>
</html>
