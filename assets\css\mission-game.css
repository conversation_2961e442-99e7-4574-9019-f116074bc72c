/* Mission Game Styles - Immersive Adventure Learning */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.mission-game-page {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh !important;
    overflow-x: hidden !important;
    display: block !important;
    align-items: unset !important;
    justify-content: unset !important;
    padding: 0 !important;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.mission-game-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Mission Header */
.mission-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.mission-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mission-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.mission-info h1 {
    font-size: 1.5rem;
    font-weight: 800;
    color: #1f2937;
    margin: 0;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-badge.easy {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.difficulty-badge.medium {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.difficulty-badge.hard {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.mission-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.stat-item i {
    color: #667eea;
}

.mission-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Progress Bar */
.progress-container {
    padding: 0 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 3px;
}

.progress-text {
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

/* Main Game Area */
.mission-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

/* Story Screen */
.story-screen {
    width: 100%;
    max-width: 600px;
    text-align: center;
}

.story-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.story-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    color: white;
    animation: float 3s ease-in-out infinite;
}

.story-content h2 {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.story-text {
    font-size: 1.1rem;
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Loading Screen */
.loading-screen {
    width: 100%;
    max-width: 500px;
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

.loading-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.loading-content p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.loading-progress {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-bar {
    height: 100%;
    background: white;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* Question Container */
.question-container {
    width: 100%;
    max-width: 800px;
}

.question-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.question-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: #6b7280;
}

.lives-display {
    display: flex;
    gap: 0.5rem;
}

.life-heart {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.life-heart.active {
    color: #ef4444;
    animation: heartbeat 2s ease-in-out infinite;
}

.life-heart.lost {
    color: #d1d5db;
    transform: scale(0.8);
}

.question-content {
    margin-bottom: 2rem;
}

.question-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
}

.options-container {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.option-btn {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.option-btn:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

.option-letter {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: #374151;
    flex-shrink: 0;
}

.option-text {
    flex: 1;
    font-weight: 500;
    color: #374151;
}

.question-actions {
    text-align: center;
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    font-size: 1rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-submit {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-submit:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-submit:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Enhanced Mission Animations */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes correctAnswer {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); background-color: #10b981; }
    100% { transform: scale(1); }
}

@keyframes incorrectAnswer {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

@keyframes floatUp {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -150%) scale(1);
    }
}

@keyframes screenShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes celebrationParticle {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translate(var(--random-x, 0), var(--random-y, 0)) scale(0);
        opacity: 0;
    }
}

@keyframes pulse-animation {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes timerPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes bounce {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Mission Complete/Failed Screens */
.mission-complete-screen, .mission-failed-screen {
    width: 100%;
    max-width: 600px;
    text-align: center;
}

.complete-content, .failed-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.complete-icon, .failed-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 3rem;
    color: white;
}

.complete-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    animation: bounce 1s ease infinite alternate;
}

.failed-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    animation: shake 0.5s ease-in-out infinite;
}

.complete-content h1, .failed-content h1 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
}

.story-completion {
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 12px;
    font-style: italic;
    color: #6b7280;
}

.mission-results {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.result-stat {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.complete-actions, .failed-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Enhanced Interactive Elements */
.option-btn.selected {
    border-color: #667eea !important;
    background: rgba(102, 126, 234, 0.1) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2) !important;
}

.option-btn.correct {
    border-color: #10b981 !important;
    background: rgba(16, 185, 129, 0.1) !important;
    color: #10b981 !important;
}

.option-btn.incorrect {
    border-color: #ef4444 !important;
    background: rgba(239, 68, 68, 0.1) !important;
    color: #ef4444 !important;
}

.pulse-animation {
    animation: pulse-animation 1s infinite;
}

.timer-warning {
    animation: timerPulse 1s infinite;
    color: #ef4444 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .mission-stats {
        gap: 1rem;
    }

    .mission-main {
        padding: 1rem;
    }

    .question-card, .story-content, .complete-content, .failed-content {
        padding: 1.5rem;
    }

    .mission-results {
        grid-template-columns: 1fr;
    }

    .complete-actions, .failed-actions {
        flex-direction: column;
    }
}

/* Pause Overlay Styles */
.pause-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.pause-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    max-width: 400px;
    width: 90%;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
}

.pause-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.pause-content h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.pause-content p {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.pause-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.pause-buttons .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    font-size: 1rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@media (max-width: 768px) {
    .pause-buttons {
        flex-direction: column;
    }

    .pause-content {
        padding: 2rem 1.5rem;
    }
}

/* Level unlock notification animations */
@keyframes unlockPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    20% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 1;
    }
    40% {
        transform: translate(-50%, -50%) scale(0.95);
    }
    60% {
        transform: translate(-50%, -50%) scale(1.05);
    }
    80% {
        transform: translate(-50%, -50%) scale(0.98);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}
