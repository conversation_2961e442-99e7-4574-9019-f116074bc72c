<?php
/**
 * Student Registration Page for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

require_once '../config/database.php';

// Initialize PDO connection for compatibility
$pdo = getPDO();

// Check if registration is open (skip if system_settings table doesn't exist)
try {
    $registrationOpen = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = 'registration_open'");
    if (!$registrationOpen || $registrationOpen['setting_value'] != '1') {
        // Allow registration for now since system_settings might not exist
        // $error = 'Student registration is currently closed. Please contact the administration.';
    }
} catch (Exception $e) {
    // System settings table doesn't exist, allow registration
    error_log("System settings check failed: " . $e->getMessage());
}

// Get departments and academic levels
try {
    $departments = fetchAll("SELECT id, name, code FROM departments ORDER BY name");
    $academicLevels = fetchAll("SELECT id, level_name, level_code FROM academic_levels ORDER BY id");
    $securityQuestions = fetchAll("SELECT id, question FROM security_questions ORDER BY id");
} catch (Exception $e) {
    error_log("Failed to load registration data: " . $e->getMessage());
    $error = "Registration system is currently unavailable. Please try again later.";
    $departments = [];
    $academicLevels = [];
    $securityQuestions = [];
}

$error = '';
$success = '';

// Debug form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("POST received: " . print_r($_POST, true));

    // Check if this is our registration form
    if (isset($_POST['register_submit'])) {
    // Debug: Log that POST was received
    error_log("Registration POST received");

    $firstName = sanitizeInput($_POST['first_name']);
    $lastName = sanitizeInput($_POST['last_name']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    $matriculationNo = strtoupper(sanitizeInput($_POST['matriculation_no']));
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    $departmentId = (int)$_POST['department_id'];
    $academicLevelId = (int)$_POST['academic_level_id'];

    // Debug: Log extracted data
    error_log("Registration data: Name=$firstName $lastName, Email=$email, MatNo=$matriculationNo");
    
    // Security questions (reduced to 2)
    $securityAnswers = [
        ['question_id' => (int)$_POST['security_question_1'], 'answer' => sanitizeInput($_POST['security_answer_1'])],
        ['question_id' => (int)$_POST['security_question_2'], 'answer' => sanitizeInput($_POST['security_answer_2'])]
    ];
    
    // Validation with detailed logging
    error_log("Validation check - firstName: '$firstName', lastName: '$lastName', email: '$email', matriculationNo: '$matriculationNo', password length: " . strlen($password));
    error_log("Validation check - departmentId: $departmentId, academicLevelId: $academicLevelId");

    if (empty($firstName) || empty($lastName) || empty($email) || empty($matriculationNo) || empty($password)) {
        $error = 'Please fill in all required fields.';
        error_log("Validation failed: Missing required fields");
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
        error_log("Validation failed: Passwords don't match");
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long.';
        error_log("Validation failed: Password too short");
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
        error_log("Validation failed: Invalid email format");
    } elseif ($departmentId <= 0 || $academicLevelId <= 0) {
        $error = 'Please select your department and academic level.';
        error_log("Validation failed: Invalid department ($departmentId) or level ($academicLevelId)");
    } else {
        error_log("All validation checks passed, proceeding with database checks");
        // Check for duplicate email
        $existingStudent = fetchOne("SELECT id FROM students WHERE email = :email", ['email' => $email]);
        if ($existingStudent) {
            $error = 'An account with this email already exists.';
        } else {
            // Check for duplicate matriculation number
            $existingMatNo = fetchOne("SELECT id FROM students WHERE student_id = :mat_no", ['mat_no' => $matriculationNo]);
            if ($existingMatNo) {
                $error = 'Matriculation number already exists in the database. Please verify your matriculation number.';
            } else {
                // Validate security questions
                $questionIds = array_column($securityAnswers, 'question_id');
                if (count(array_unique($questionIds)) !== 2) {
                    $error = 'Please select two different security questions.';
                } else {
                    foreach ($securityAnswers as $answer) {
                        if (empty($answer['answer'])) {
                            $error = 'Please answer all security questions.';
                            break;
                        }
                    }
                }
            }
        }
    }

    if (!$error) {
        try {
            // Debug: Log that we're starting insertion
            error_log("Starting student insertion - no validation errors");

            // Hash password
            $passwordHash = generateSecureHash($password);
            error_log("Password hashed successfully");

            // Insert student
            error_log("Attempting to insert student with MatNo: $matriculationNo");

            // Ensure department and academic level exist
            $deptExists = fetchOne("SELECT id FROM departments WHERE id = ?", [$departmentId]);
            $levelExists = fetchOne("SELECT id FROM academic_levels WHERE id = ?", [$academicLevelId]);

            if (!$deptExists) {
                throw new Exception("Selected department does not exist. Please refresh the page and try again.");
            }

            if (!$levelExists) {
                throw new Exception("Selected academic level does not exist. Please refresh the page and try again.");
            }

            $newStudentId = insertRecord('students', [
                'student_id' => $matriculationNo,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
                'phone' => $phone,
                'password_hash' => $passwordHash,
                'department_id' => $departmentId,
                'academic_level_id' => $academicLevelId,
                'is_approved' => false  // Pending approval
            ]);

            error_log("Insert result: " . ($newStudentId ? "Success (ID: $newStudentId)" : "Failed"));

            if ($newStudentId) {
                error_log("Student inserted successfully with ID: $newStudentId");

                // Insert security answers
                foreach ($securityAnswers as $answer) {
                    error_log("Inserting security answer for question ID: " . $answer['question_id']);
                    $answerResult = insertRecord('student_security_answers', [
                        'student_id' => $newStudentId,
                        'question_id' => $answer['question_id'],
                        'answer_hash' => generateSecureHash(strtolower($answer['answer']))
                    ]);
                    error_log("Security answer insert result: " . ($answerResult ? "Success (ID: $answerResult)" : "Failed"));
                }

                $success = true;
                $matriculationNumber = $matriculationNo;
            } else {
                $error = 'Registration failed. Please try again.';
            }
        } catch (Exception $e) {
            error_log("Registration exception: " . $e->getMessage());
            error_log("Registration exception trace: " . $e->getTraceAsString());
            $error = 'Registration failed: ' . $e->getMessage() . ' (Check error log for details)';
        }
    } else {
        error_log("Registration validation failed: $error");
    }
    } // End of register_submit check
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration - AI-Powered LMS</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Enhanced Registration Layout - Compact Version */
        .register-card {
            max-width: 600px;
            width: 90%;
            margin: 1rem auto;
            padding: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .auth-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 0;
        }

        .auth-body {
            padding: 1.5rem;
        }

        /* Form Sections with Compact Spacing */
        .form-section {
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid rgba(226, 232, 240, 0.8);
        }

        .form-section h3 {
            color: #1e293b;
            font-size: 1rem;
            font-weight: 600;
            margin: 0 0 0.75rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-section h3 i {
            color: #3b82f6;
            font-size: 1rem;
        }

        .section-description {
            color: #64748b;
            font-size: 0.85rem;
            margin: 0 0 0.75rem 0;
            font-style: italic;
        }

        /* Form Groups with Compact Spacing */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .register-card {
                margin: 0.5rem auto;
                width: 95%;
            }

            .auth-header, .auth-body {
                padding: 1rem;
            }

            .form-section {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }
        }

        /* Compact Input Styling */
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem 0.875rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .form-group label {
            display: block;
            margin-bottom: 0.4rem;
            font-weight: 500;
            color: #374151;
            font-size: 0.85rem;
        }

        .form-help {
            display: block;
            margin-top: 0.2rem;
            font-size: 0.75rem;
            color: #6b7280;
        }

        /* Security Questions Enhancement - Compact */
        .security-question-group {
            background: white;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e5e7eb;
        }

        .security-question-group:last-child {
            margin-bottom: 0;
        }

        /* Password Strength Indicator */
        .strength-bar {
            margin-top: 8px;
            display: none;
        }

        .strength-container {
            width: 100%;
            height: 6px;
            background-color: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: width 0.3s ease, background-color 0.3s ease;
            border-radius: 3px;
        }

        .strength-fill.weak {
            background: linear-gradient(90deg, #dc3545, #e74c3c);
        }

        .strength-fill.medium {
            background: linear-gradient(90deg, #ffc107, #f39c12);
        }

        .strength-fill.strong {
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .strength-text {
            font-size: 12px;
            margin-top: 4px;
            font-weight: 500;
        }

        .strength-text.weak {
            color: #dc3545;
        }

        .strength-text.medium {
            color: #ffc107;
        }

        .strength-text.strong {
            color: #28a745;
        }

        /* Password Input Container */
        .password-input-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.25rem;
        }

        .password-toggle:hover {
            color: #3b82f6;
        }

        /* Success Modal Overlay */
        .success-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        /* Success Modal Box */
        .success-modal {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 550px;
            width: 92%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            text-align: center;
            position: relative;
            animation: slideUp 0.4s ease-out;
        }

        /* Success Modal Content */
        .success-modal-header {
            margin-bottom: 1.5rem;
        }

        .success-modal h2 {
            color: #059669;
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .success-modal .check-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem auto;
            animation: bounceIn 0.6s ease-out 0.2s both;
        }

        .success-modal .check-icon i {
            color: white;
            font-size: 1.8rem;
        }

        .success-modal-body {
            margin-bottom: 2rem;
        }

        .success-modal p {
            color: #374151;
            font-size: 1.1rem;
            line-height: 1.6;
            margin: 0 0 1rem 0;
        }

        .matriculation-number-box {
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            border: 2px solid #3b82f6;
            border-radius: 15px;
            padding: 1.2rem 0.8rem;
            margin: 1.5rem 0;
            position: relative;
            overflow: hidden;
            width: 100%;
            box-sizing: border-box;
        }

        .matriculation-number-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        .matriculation-label {
            color: #1e40af;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.75rem;
        }

        .matriculation-number {
            color: #1e40af;
            font-size: 1.4rem;
            font-weight: 800;
            letter-spacing: 1px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            line-height: 1.3;
            padding: 0 0.5rem;
        }

        .success-modal-footer {
            border-top: 1px solid #e5e7eb;
            padding-top: 1.5rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .modal-btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 120px;
            justify-content: center;
        }

        .modal-btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
        }

        .modal-btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .modal-btn-secondary {
            background: #f8fafc;
            color: #64748b;
            border: 2px solid #e2e8f0;
        }

        .modal-btn-secondary:hover {
            background: #e2e8f0;
            color: #475569;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Mobile responsiveness for modal */
        @media (max-width: 768px) {
            .success-modal {
                width: 95%;
                padding: 1.5rem;
                margin: 1rem;
            }

            .success-modal h2 {
                font-size: 1.5rem;
            }

            .matriculation-number {
                font-size: 1.1rem;
                letter-spacing: 0.5px;
                padding: 0 0.25rem;
            }

            .modal-btn {
                min-width: 100px;
                padding: 0.6rem 1.2rem;
            }

            .success-modal-footer {
                flex-direction: column;
                align-items: center;
            }
        }

        .alert-success::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .success-actions {
            margin-top: 20px;
        }

        .success-actions .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .success-actions .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .success-actions .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        /* Enhanced Register Button */
        .btn-register {
            width: 100%;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-register:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-register:active {
            transform: translateY(0);
        }

        /* Auth Links Enhancement */
        .auth-links {
            margin-top: 2rem;
            text-align: center;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .auth-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: #6b7280;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .auth-links a:hover {
            color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        /* Alert Enhancements */
        .alert {
            padding: 1rem 1.25rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            font-weight: 500;
        }

        .alert-error {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .alert i {
            margin-top: 0.125rem;
            flex-shrink: 0;
        }

        /* Success Actions Styling */
        .success-actions {
            margin-top: 2rem;
            text-align: center;
        }

        .success-actions .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .success-actions .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .success-actions .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="logo-container">
                    <img src="../images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="logo" style="width: 60px; height: 60px;">
                </div>
                <h1 style="font-size: 1.5rem; margin: 0.5rem 0;">Student Registration</h1>
                <p style="font-size: 0.9rem; margin: 0.25rem 0;">AI-Powered Learning Management System</p>
                <p class="school-name" style="font-size: 0.85rem; margin: 0.25rem 0;">Ogbonnaya Onu Polytechnic, Aba</p>
            </div>
            
            <div class="auth-body">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class='success-modal-overlay' id='successModal'>
                        <div class='success-modal'>
                            <div class='success-modal-header'>
                                <div class='check-icon'>
                                    <i class='fas fa-check'></i>
                                </div>
                                <h2>Registration Successful!</h2>
                            </div>

                            <div class='success-modal-body'>
                                <p>🎉 Congratulations! Your registration has been submitted successfully.</p>

                                <div class='matriculation-number-box'>
                                    <div class='matriculation-label'>Your Matriculation Number</div>
                                    <div class='matriculation-number'><?php echo $matriculationNumber; ?></div>
                                </div>

                                <p><i class='fas fa-info-circle' style='color: #3b82f6; margin-right: 0.5rem;'></i>
                                Your registration details have been submitted for administrator approval. You will be notified once your account is activated.</p>

                                <p style='font-size: 0.95rem; color: #6b7280;'>
                                <i class='fas fa-clock' style='margin-right: 0.5rem;'></i>
                                Please keep your matriculation number safe for future reference.
                                </p>
                            </div>

                            <div class='success-modal-footer'>
                                <a href='../index.php' class='modal-btn modal-btn-primary'>
                                    <i class='fas fa-home'></i>
                                    Return to Home
                                </a>
                                <button onclick='printDetails()' class='modal-btn modal-btn-secondary'>
                                    <i class='fas fa-print'></i>
                                    Print Details
                                </button>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <form method="POST" class="auth-form register-form">
                        <!-- Personal Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-user"></i> Personal Information</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first_name">First Name *</label>
                                    <input type="text" id="first_name" name="first_name" required 
                                           value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                                </div>
                                <div class="form-group">
                                    <label for="last_name">Last Name *</label>
                                    <input type="text" id="last_name" name="last_name" required 
                                           value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" 
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                        </div>
                        
                        <!-- Academic Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-graduation-cap"></i> Academic Information</h3>
                            
                            <div class="form-group">
                                <label for="department_id">Department *</label>
                                <select id="department_id" name="department_id" required>
                                    <option value="">Select your department</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>" 
                                                data-code="<?php echo $dept['code']; ?>"
                                                <?php echo (isset($_POST['department_id']) && $_POST['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($dept['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="academic_level_id">Academic Level *</label>
                                <select id="academic_level_id" name="academic_level_id" required>
                                    <option value="">Select your level</option>
                                    <?php foreach ($academicLevels as $level): ?>
                                        <option value="<?php echo $level['id']; ?>"
                                                <?php echo (isset($_POST['academic_level_id']) && $_POST['academic_level_id'] == $level['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($level['level_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="matriculation_no">Matriculation Number *</label>
                                <input type="text" id="matriculation_no" name="matriculation_no" required
                                       placeholder="Enter your matriculation number"
                                       value="<?php echo isset($_POST['matriculation_no']) ? htmlspecialchars($_POST['matriculation_no']) : ''; ?>">
                                <small class="form-help">Enter your unique matriculation number as provided by the institution</small>
                            </div>
                        </div>
                        
                        <!-- Password Section -->
                        <div class="form-section">
                            <h3><i class="fas fa-lock"></i> Password</h3>
                            
                            <div class="form-group">
                                <label for="password">Password *</label>
                                <div class="password-input-container">
                                    <input type="password" id="password" name="password" required minlength="8">
                                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="strength-bar">
                                    <div class="strength-container">
                                        <div class="strength-fill"></div>
                                    </div>
                                    <div class="strength-text"></div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password">Confirm Password *</label>
                                <div class="password-input-container">
                                    <input type="password" id="confirm_password" name="confirm_password" required minlength="8">
                                    <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Security Questions -->
                        <div class="form-section">
                            <h3><i class="fas fa-shield-alt"></i> Security Questions</h3>
                            <p class="section-description">Choose 2 different security questions for password recovery</p>

                            <?php for ($i = 1; $i <= 2; $i++): ?>
                                <div class="security-question-group">
                                    <div class="form-group">
                                        <label for="security_question_<?php echo $i; ?>">Security Question <?php echo $i; ?> *</label>
                                        <select id="security_question_<?php echo $i; ?>" name="security_question_<?php echo $i; ?>" required>
                                            <option value="">Select a question</option>
                                            <?php foreach ($securityQuestions as $question): ?>
                                                <option value="<?php echo $question['id']; ?>"
                                                        <?php echo (isset($_POST["security_question_{$i}"]) && $_POST["security_question_{$i}"] == $question['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($question['question']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="security_answer_<?php echo $i; ?>">Answer *</label>
                                        <input type="text" id="security_answer_<?php echo $i; ?>" name="security_answer_<?php echo $i; ?>" required
                                               value="<?php echo isset($_POST["security_answer_{$i}"]) ? htmlspecialchars($_POST["security_answer_{$i}"]) : ''; ?>">
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                        
                        <button type="submit" name="register_submit" class="btn btn-primary btn-register">
                            <i class="fas fa-user-plus"></i>
                            Register Account
                        </button>
                    </form>
                <?php endif; ?>
                
                <div class="auth-links">
                    <a href="login.php" class="link-secondary">
                        <i class="fas fa-sign-in-alt"></i>
                        Already have an account? Login
                    </a>
                    <a href="../index.php" class="link-secondary">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </a>
                </div>
            </div>
            
            <div class="auth-footer">
                <div class="security-info">
                    <i class="fas fa-shield-alt"></i>
                    <span>Your information is secure and encrypted</span>
                </div>
                <p>&copy; 2025 Ogbonnaya Onu Polytechnic, Aba. All rights reserved.</p>
            </div>
        </div>
    </div>
    
    <!-- JavaScript for password features -->
    <script>
        // Password visibility toggle
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = field.nextElementSibling.querySelector('i');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;
            const checks = {
                length: password.length >= 8,
                lowercase: /[a-z]/.test(password),
                uppercase: /[A-Z]/.test(password),
                numbers: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            strength = Object.values(checks).filter(Boolean).length;
            return { strength, checks };
        }

        function updatePasswordStrength() {
            const password = document.getElementById('password').value;
            const strengthBar = document.querySelector('.strength-bar');
            const strengthText = document.querySelector('.strength-text');
            const strengthFill = document.querySelector('.strength-fill');

            if (!password) {
                strengthBar.style.display = 'none';
                return;
            }

            strengthBar.style.display = 'block';
            const { strength, checks } = checkPasswordStrength(password);

            // Update strength bar
            const percentage = (strength / 5) * 100;
            strengthFill.style.width = percentage + '%';

            // Update colors and text
            if (strength <= 2) {
                strengthFill.className = 'strength-fill weak';
                strengthText.textContent = 'Weak';
                strengthText.className = 'strength-text weak';
            } else if (strength <= 3) {
                strengthFill.className = 'strength-fill medium';
                strengthText.textContent = 'Medium';
                strengthText.className = 'strength-text medium';
            } else {
                strengthFill.className = 'strength-fill strong';
                strengthText.textContent = 'Strong';
                strengthText.className = 'strength-text strong';
            }
        }

        // Security Questions Management
        function updateSecurityQuestions() {
            const question1 = document.getElementById('security_question_1');
            const question2 = document.getElementById('security_question_2');

            if (!question1 || !question2) return;

            const selectedValue1 = question1.value;
            const selectedValue2 = question2.value;

            // Store all original options if not already stored
            if (!question1.dataset.originalOptions) {
                question1.dataset.originalOptions = question1.innerHTML;
            }
            if (!question2.dataset.originalOptions) {
                question2.dataset.originalOptions = question2.innerHTML;
            }

            // Reset both dropdowns to original options
            question1.innerHTML = question1.dataset.originalOptions;
            question2.innerHTML = question2.dataset.originalOptions;

            // Remove selected option from the other dropdown
            if (selectedValue1) {
                const optionToRemove = question2.querySelector(`option[value="${selectedValue1}"]`);
                if (optionToRemove && selectedValue1 !== '') {
                    optionToRemove.remove();
                }
            }

            if (selectedValue2) {
                const optionToRemove = question1.querySelector(`option[value="${selectedValue2}"]`);
                if (optionToRemove && selectedValue2 !== '') {
                    optionToRemove.remove();
                }
            }

            // Restore selected values
            question1.value = selectedValue1;
            question2.value = selectedValue2;
        }

        // Form validation enhancement
        function validateForm() {
            const question1 = document.getElementById('security_question_1').value;
            const question2 = document.getElementById('security_question_2').value;

            if (question1 && question2 && question1 === question2) {
                alert('Please select two different security questions.');
                return false;
            }

            return true;
        }

        // Add event listeners when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('password');
            if (passwordField) {
                passwordField.addEventListener('input', updatePasswordStrength);
            }

            // Add security question listeners
            const question1 = document.getElementById('security_question_1');
            const question2 = document.getElementById('security_question_2');
            const form = document.querySelector('.register-form');

            if (question1 && question2) {
                question1.addEventListener('change', updateSecurityQuestions);
                question2.addEventListener('change', updateSecurityQuestions);

                // Initial update to handle pre-selected values
                setTimeout(updateSecurityQuestions, 100);
            }

            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateForm()) {
                        e.preventDefault();
                    }
                });
            }
        });

        // Print function for success modal
        function printDetails() {
            const matriculationNumber = '<?php echo isset($matriculationNumber) ? $matriculationNumber : ""; ?>';
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>Registration Details - Ogbonnaya Onu Polytechnic</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .details { background: #f8f9fa; padding: 20px; border-radius: 10px; }
                        .matriculation { font-size: 24px; font-weight: bold; color: #1e40af; text-align: center; margin: 20px 0; }
                        .note { color: #666; font-style: italic; margin-top: 20px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>Ogbonnaya Onu Polytechnic, Aba</h1>
                        <h2>Student Registration Confirmation</h2>
                    </div>
                    <div class="details">
                        <h3>Registration Successful!</h3>
                        <div class="matriculation">Matriculation Number: ${matriculationNumber}</div>
                        <p><strong>Status:</strong> Pending Administrator Approval</p>
                        <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
                        <div class="note">
                            <p>Please keep this document safe for your records. You will be notified once your account is activated.</p>
                        </div>
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
