/**
 * Mission Mode JavaScript
 * AI-Powered LMS - Ogbonnaya Onu Polytechnic, Aba
 */

// Initialize mission mode when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeMissionMode();

    // Check if we just returned from a completed mission
    checkForMissionCompletion();
});

function initializeMissionMode() {
    // Add click handlers for mission levels
    const missionLevels = document.querySelectorAll('.mission-level.available, .mission-level.completed');
    missionLevels.forEach(level => {
        level.addEventListener('click', function() {
            const levelId = this.dataset.level;
            if (levelId) {
                startMission(levelId);
            }
        });
    });

    // Add keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    // Add scroll animations
    addScrollAnimations();
}

function startMission(levelId) {
    // Show loading state
    showLoadingModal('Loading mission details...');
    
    // Fetch mission details
    fetch(`mission-start.php?level_id=${levelId}`)
        .then(response => response.json())
        .then(data => {
            hideLoadingModal();
            if (data.success) {
                showMissionStartModal(data.mission);
            } else {
                showErrorModal(data.message || 'Failed to load mission details');
            }
        })
        .catch(error => {
            hideLoadingModal();
            showErrorModal('Error loading mission: ' + error.message);
        });
}

function showMissionStartModal(mission) {
    const modal = document.getElementById('missionModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    
    modalTitle.innerHTML = `
        <i class="fas fa-${mission.boss_level ? 'dragon' : 'flag'}"></i>
        Level ${mission.level_number}: ${mission.title}
    `;
    
    modalBody.innerHTML = `
        <div class="mission-preview">
            <div class="mission-description">
                <h4>Mission Briefing</h4>
                <p>${mission.description}</p>
            </div>
            
            <div class="mission-requirements">
                <h4>Mission Requirements</h4>
                <div class="requirement-grid">
                    <div class="requirement-item">
                        <i class="fas fa-signal"></i>
                        <span>Difficulty: <strong>${mission.difficulty}</strong></span>
                    </div>
                    <div class="requirement-item">
                        <i class="fas fa-question-circle"></i>
                        <span>Questions: <strong>${mission.questions_required}</strong></span>
                    </div>
                    <div class="requirement-item">
                        <i class="fas fa-coins"></i>
                        <span>Reward: <strong>${mission.points_reward} points</strong></span>
                    </div>
                    ${mission.boss_level ? '<div class="requirement-item boss-warning"><i class="fas fa-exclamation-triangle"></i><span><strong>Boss Level!</strong> Extra challenging</span></div>' : ''}
                </div>
            </div>
            
            ${mission.student_status === 'completed' ? `
                <div class="previous-completion">
                    <h4>Previous Best Performance</h4>
                    <div class="completion-stats">
                        <div class="stat">
                            <i class="fas fa-star"></i>
                            <span>${mission.stars_earned}/3 Stars</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-trophy"></i>
                            <span>${mission.best_score} Points</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-redo"></i>
                            <span>${mission.attempts} Attempts</span>
                        </div>
                    </div>
                </div>
            ` : ''}
            
            <div class="mission-actions">
                <button class="btn-secondary" onclick="closeMissionModal()">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <button class="btn-primary" onclick="launchMission(${mission.id})">
                    <i class="fas fa-rocket"></i>
                    ${mission.student_status === 'completed' ? 'Replay Mission' : 'Start Mission'}
                </button>
            </div>
        </div>
    `;
    
    modal.style.display = 'block';
    
    // Add modal close on outside click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeMissionModal();
        }
    });
}

function launchMission(levelId) {
    closeMissionModal();
    showLoadingModal('Preparing your mission...');
    
    // Create mission session
    fetch('mission-launch.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            level_id: levelId
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();
        if (data.success) {
            // Redirect to mission game
            window.location.href = `mission-game.php?session_id=${data.session_id}`;
        } else {
            showErrorModal(data.message || 'Failed to start mission');
        }
    })
    .catch(error => {
        hideLoadingModal();
        showErrorModal('Error starting mission: ' + error.message);
    });
}

function closeMissionModal() {
    const modal = document.getElementById('missionModal');
    modal.style.display = 'none';
}

function goBack() {
    window.location.href = 'dashboard.php';
}

function handleKeyboardNavigation(e) {
    switch(e.key) {
        case 'Escape':
            closeMissionModal();
            break;
        case 'Enter':
            // If modal is open and start button is visible, click it
            const modal = document.getElementById('missionModal');
            if (modal.style.display === 'block') {
                const startBtn = modal.querySelector('.btn-primary');
                if (startBtn) {
                    startBtn.click();
                }
            }
            break;
    }
}

function addScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate__animated', 'animate__fadeInUp');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    const missionLevels = document.querySelectorAll('.mission-level');
    missionLevels.forEach(level => {
        observer.observe(level);
    });
}

// Utility functions
function showLoadingModal(message) {
    const modal = document.createElement('div');
    modal.id = 'loadingModal';
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content" style="text-align: center; max-width: 400px;">
            <div class="modal-body">
                <div class="loading-spinner" style="margin: 2rem auto;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 3rem; color: #667eea;"></i>
                </div>
                <h3 style="margin: 1rem 0; color: #333;">${message}</h3>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

function hideLoadingModal() {
    const modal = document.getElementById('loadingModal');
    if (modal) {
        document.body.removeChild(modal);
    }
}

function showErrorModal(message) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header" style="background: linear-gradient(135deg, #ff6b6b, #ff5252);">
                <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div class="modal-body">
                <p style="color: #666; font-size: 1.1rem; line-height: 1.5;">${message}</p>
                <div style="text-align: center; margin-top: 2rem;">
                    <button class="btn-primary" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-check"></i>
                        OK
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (document.body.contains(modal)) {
            document.body.removeChild(modal);
        }
    }, 5000);
}

// CSS for buttons (inline styles for modal buttons)
const style = document.createElement('style');
style.textContent = `
    .btn-primary, .btn-secondary {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 1rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-secondary {
        background: #f8f9fa;
        color: #666;
        border: 1px solid #dee2e6;
    }
    
    .btn-secondary:hover {
        background: #e9ecef;
        transform: translateY(-1px);
    }
    
    .mission-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #eee;
    }
    
    .requirement-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }
    
    .requirement-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .requirement-item.boss-warning {
        background: linear-gradient(135deg, #ff6b6b, #ff5252);
        color: white;
    }
    
    .completion-stats {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin: 1rem 0;
    }
    
    .completion-stats .stat {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
        flex: 1;
    }
    
    .completion-stats .stat i {
        display: block;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: #667eea;
    }

    @keyframes starsPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); color: #ffd700; }
        100% { transform: scale(1); }
    }

    @keyframes slideInRight {
        0% { transform: translateX(100%); opacity: 0; }
        100% { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        0% { transform: translateX(0); opacity: 1; }
        100% { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Check if we just returned from a completed mission and need to refresh
function checkForMissionCompletion() {
    // Check URL parameters for mission completion
    const urlParams = new URLSearchParams(window.location.search);
    const missionCompleted = urlParams.get('mission_completed');
    const levelCompleted = urlParams.get('level_completed');

    if (missionCompleted === 'true' || levelCompleted) {
        console.log('Mission completed, refreshing to show updated progress...');
        // Remove the URL parameters and refresh
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
        location.reload();
        return;
    }

    // Check localStorage for mission completion flag
    const completionFlag = localStorage.getItem('missionJustCompleted');
    if (completionFlag) {
        console.log('Mission completion detected from localStorage, refreshing...');
        localStorage.removeItem('missionJustCompleted');
        location.reload();
        return;
    }
}



// Show notification when stars are earned
function showStarsEarnedNotification(starsEarned) {
    const notification = document.createElement('div');
    notification.className = 'stars-notification';
    notification.innerHTML = `<i class="fas fa-star"></i> +${starsEarned} Stars Earned!`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #333;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        z-index: 10000;
        animation: slideInRight 0.5s ease-out;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.5s ease-in';
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 3000);
}

function showLevelProgressNotification(newLevel) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'level-notification';
    notification.innerHTML = `<i class="fas fa-flag"></i> Now on Level ${newLevel}!`;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        background: linear-gradient(135deg, #4CAF50, #66BB6A);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        z-index: 10000;
        animation: slideInRight 0.5s ease-out;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.5s ease-in';
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 3000);
}
