/**
 * Setup Page JavaScript for AI-Powered LMS
 * Ogbonnaya Onu Polytechnic, Aba
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeSetup();
});

function initializeSetup() {
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const strengthIndicator = document.getElementById('password-strength');
    
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            validatePasswordMatch();
        });
    }
    
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validatePasswordMatch);
    }
    
    // Add form validation
    const form = document.querySelector('.setup-form');
    if (form) {
        form.addEventListener('submit', validateForm);
    }
    
    // Add enter key support
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
            const submitBtn = document.querySelector('.btn-setup');
            if (submitBtn) {
                submitBtn.click();
            }
        }
    });
}

function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function checkPasswordStrength(password) {
    const strengthIndicator = document.getElementById('password-strength');
    if (!strengthIndicator) return;
    
    let strength = 0;
    let feedback = '';
    
    // Length check
    if (password.length >= 8) strength += 1;
    if (password.length >= 12) strength += 1;
    
    // Character variety checks
    if (/[a-z]/.test(password)) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    
    // Remove existing classes
    strengthIndicator.classList.remove('weak', 'medium', 'strong');
    
    if (password.length === 0) {
        strengthIndicator.style.width = '0%';
        strengthIndicator.style.background = 'transparent';
        return;
    }
    
    if (strength <= 2) {
        strengthIndicator.classList.add('weak');
        feedback = 'Weak password';
    } else if (strength <= 4) {
        strengthIndicator.classList.add('medium');
        feedback = 'Medium strength';
    } else {
        strengthIndicator.classList.add('strong');
        feedback = 'Strong password';
    }
    
    // Add tooltip or aria-label for accessibility
    strengthIndicator.setAttribute('aria-label', feedback);
    strengthIndicator.setAttribute('title', feedback);
}

function validatePasswordMatch() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    if (!password || !confirmPassword) return;
    
    const isMatch = password.value === confirmPassword.value;
    const isEmpty = confirmPassword.value === '';
    
    // Remove existing validation classes
    confirmPassword.classList.remove('valid', 'invalid');
    
    if (!isEmpty) {
        if (isMatch) {
            confirmPassword.classList.add('valid');
            confirmPassword.style.borderColor = '#10b981';
        } else {
            confirmPassword.classList.add('invalid');
            confirmPassword.style.borderColor = '#ef4444';
        }
    } else {
        confirmPassword.style.borderColor = '#e5e7eb';
    }
}

function validateForm(e) {
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    let isValid = true;
    let errorMessage = '';
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
        errorMessage = 'Email is required.';
        isValid = false;
    } else if (!emailRegex.test(email)) {
        errorMessage = 'Please enter a valid email address.';
        isValid = false;
    }
    
    // Password validation
    if (!password) {
        errorMessage = 'Password is required.';
        isValid = false;
    } else if (password.length < 8) {
        errorMessage = 'Password must be at least 8 characters long.';
        isValid = false;
    }
    
    // Confirm password validation
    if (!confirmPassword) {
        errorMessage = 'Please confirm your password.';
        isValid = false;
    } else if (password !== confirmPassword) {
        errorMessage = 'Passwords do not match.';
        isValid = false;
    }
    
    if (!isValid) {
        e.preventDefault();
        showError(errorMessage);
        return false;
    }
    
    // Show loading state
    const submitBtn = document.querySelector('.btn-setup');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Setting up...';
        submitBtn.disabled = true;
    }
    
    return true;
}

function showError(message) {
    // Remove existing error alerts
    const existingAlert = document.querySelector('.alert-error');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    // Create new error alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-error';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        ${message}
    `;
    
    // Insert at the beginning of setup-body
    const setupBody = document.querySelector('.setup-body');
    setupBody.insertBefore(alertDiv, setupBody.firstChild);
    
    // Scroll to error
    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle"></i>
        ${message}
    `;
    
    const setupBody = document.querySelector('.setup-body');
    setupBody.insertBefore(alertDiv, setupBody.firstChild);
    
    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Add CSS for validation states
const style = document.createElement('style');
style.textContent = `
    .form-group input.valid {
        border-color: #10b981 !important;
        background-color: #f0fdf4;
    }
    
    .form-group input.invalid {
        border-color: #ef4444 !important;
        background-color: #fef2f2;
    }
    
    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }
    
    .alert {
        animation: slideDown 0.3s ease-out;
    }
    
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
